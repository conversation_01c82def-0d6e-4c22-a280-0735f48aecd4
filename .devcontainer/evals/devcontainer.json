{"name": "jupyter-lab-311", "image": "mcr.microsoft.com/devcontainers/python:3.11-bookworm", "features": {"ghcr.io/devcontainers/features/common-utils:2": {}, "ghcr.io/devcontainers/features/python:1": {"version": "3.11"}}, "postCreateCommand": "pip install --upgrade pip && pip install -r src/Evals/requirements.txt && pip install -e clients/minikai-frontend-client && pip list --outdated", "postStartCommand": "nohup jupyter lab --ip=0.0.0.0 --port=8894 --no-browser --allow-root --NotebookApp.token='' &", "forwardPorts": [8894], "portsAttributes": {"8894": {"label": "Jupyter Lab", "onAutoForward": "openPreview"}}, "runArgs": ["--env-file", "src/Evals/.env", "-e", "PYTHONPATH=/workspaces/minikai-platform/src/Evals:${PYTHONPATH}"], "customizations": {"vscode": {"extensions": ["ms-python.python", "charliermarsh.ruff", "ms-python.black-formatter", "ms-toolsai.jupyter", "ms-toolsai.jupyter-keymap", "ms-toolsai.jupyter-renderers", "openai.chatgpt"], "settings": {"jupyter.notebookFileRoot": "${workspaceFolder}", "python.formatting.provider": "black", "editor.formatOnSave": true, "ruff.organizeImports": true, "python.linting.enabled": true, "python.linting.mypyEnabled": true}}}}