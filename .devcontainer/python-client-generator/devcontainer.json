{"name": "python-client-generator", "image": "mcr.microsoft.com/devcontainers/python:3.11-bookworm", "features": {"ghcr.io/devcontainers/features/common-utils:2": {}, "ghcr.io/devcontainers/features/python:1": {"version": "3.11"}}, "postCreateCommand": "pipx install --include-deps openapi-python-client && pipx install ruff black pre-commit && pre-commit install", "customizations": {"vscode": {"extensions": ["ms-python.python", "charliermarsh.ruff", "ms-python.black-formatter"], "settings": {"python.formatting.provider": "black", "editor.formatOnSave": true, "ruff.organizeImports": true}}}, "remoteUser": "vscode"}