# Centralized API Management

This directory contains all API specifications, policies, and documentation for the Minikai platform.

## Directory Structure

```
apis/
├── specs/                          # OpenAPI specifications
│   ├── client-app/                 # NextJS API specs (next-swagger-doc generated)
│   │   └── client-api.openapi.json # Client API specification
│   ├── web-api/                    # .NET Web API specs (NSwag generated)
│   │   └── web-api.openapi.json    # Web API specification
│   ├── functions/                  # Azure Functions specs
│   │   ├── icare-sync-engine.openapi.json
│   │   ├── lumary-sync-engine.openapi.json
│   │   └── minikai-tools.openapi.json
│   └── external/                   # External API specs
│       ├── icare.openapi.json
│       ├── mixpanel.openapi.json
│       └── openai.openapi.yaml
├── policies/                       # API Management policies
│   ├── client-app/
│   │   └── client-api-policy.xml   # Client API policy
│   ├── web-api/
│   │   └── web-api-policy.xml      # Web API policy
│   ├── functions/
│   │   ├── icare-sync-engine-api-policy.xml
│   │   └── minikai-tools-policy.xml
│   ├── external/
│   │   ├── icare-policy.xml
│   │   ├── mixpanel-policy.xml
│   │   └── openai-policy.xml
│   └── shared/                     # Common policies (future)
├── docs/                           # API documentation
│   └── chat-api-usage.md           # Chat API usage guide
└── README.md
```

## NextJS API Spec Generation

### Automatic Generation (Development)

The NextJS client API spec is automatically generated using `next-swagger-doc` when you run the development server:

```bash
cd src/ClientApp
npm run dev
```

This will:

1. Parse JSDoc comments in API route files
2. Generate OpenAPI spec based on the comments
3. Output the spec to `apis/specs/client-app/client-api.openapi.json`

### Manual Generation

You can also generate specs manually:

```bash
cd src/ClientApp
npm run generate-specs
```

### Adding New API Routes

To add documentation for new NextJS API routes:

1. Add JSDoc comments to your route file following the pattern in `src/ClientApp/src/app/api/chat/route.ts`
2. The spec will be automatically generated when you run the dev server or manual generation command

Example JSDoc format:

```typescript
/**
 * @swagger
 * /your-route:
 *   post:
 *     summary: Your route description
 *     tags: [YourTag]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/YourRequestSchema'
 *     responses:
 *       200:
 *         description: Success response
 */
export async function POST(req: NextRequest) {
  // your implementation
}
```

## .NET Web API Specs

### Automatic Generation (Development)

The .NET Web API spec is automatically generated using NSwag when you build the Web project in Debug mode:

```bash
cd src/Web
dotnet build
```

Or when you run "Run and Debug" in VSCode for the Web project.

This will:

1. Generate OpenAPI spec from your controllers and XML documentation
2. Output the spec to both:
   - `src/Web/wwwroot/api/specification.json` (for runtime use)
   - `apis/specs/web-api/web-api.openapi.json` (centralized location)
3. Generate TypeScript client for the NextJS app

### Configuration

- **Main Config**: `src/Web/config.nswag` (generates wwwroot spec and TypeScript client)
- **Centralized Config**: `src/Web/config.centralized.nswag` (generates centralized spec)
- **Output**: `apis/specs/web-api/web-api.openapi.json`

The generation runs automatically during Debug builds and can be disabled by setting `SkipNSwag=True`.

## Azure Functions Specs

Azure Functions specs are manually maintained or generated using Azure Functions OpenAPI extensions.

## API Management Policies

Each API has corresponding policies in the `policies/` directory that define:

- Authentication requirements
- Rate limiting
- CORS settings
- Request/response transformations
- Error handling

## Usage

1. **Development**: Specs are automatically updated when you modify API routes
2. **Deployment**: Use the specs and policies to configure Azure API Management
3. **Documentation**: Generated specs serve as the source of truth for API documentation
4. **Client Generation**: Use specs to generate typed clients for different languages

## Configuration

### NextJS Swagger Configuration

- **Config File**: `src/ClientApp/swagger.config.json` (for CLI generation)
- **Output**: `apis/specs/client-app/client-api.openapi.json`

### Environment Variables for Policies

When deploying policies to API Management, replace these template variables:

- `{{TenantId}}` - Your Azure AD Tenant ID
- `{{ClientId}}` - Your Azure AD Application Client ID
- `{{Environment}}` - Environment suffix (dev, staging, prod)

## Best Practices

1. **Keep JSDoc comments up to date** with your API implementations
2. **Test generated specs** before deploying to API Management
3. **Version your APIs** when making breaking changes
4. **Use consistent naming** across all API specifications
5. **Document error responses** thoroughly for better client integration

## Future Enhancements

- Automated CI/CD pipeline for spec deployment
- Cross-service client generation
- API versioning strategy
- Shared policy templates
- Integration testing with generated specs
