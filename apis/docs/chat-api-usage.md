# Minikai Chat API Usage Guide

This guide explains how to authenticate and use the Minikai Chat API through Azure API Management.

## Overview

The Chat API allows external scripts and applications to interact with Minikai's AI chat system using JWT authentication
via Azure AD OAuth2 authorization code flow.

## Files Created

- **OpenAPI Spec**: `.azure/bicep/specs/chat-api-openapi.json`
- **API Policy**: `.azure/bicep/policies/chat-api-policy.xml`
- **Middleware Update**: `src/ClientApp/src/middleware.ts` (updated to protect `/api/chat`)

## Configuration Required

Before deploying to API Management, you'll need to configure these template variables in the policy:

- `{{TenantId}}` - Your Azure AD Tenant ID
- `{{ClientId}}` - Your Azure AD Application Client ID
- `{{Environment}}` - Environment suffix (dev, staging, prod)

## Authentication Flow

### Step 1: Get Authorization Code

Direct users to the Azure AD authorization endpoint:

```
https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize?
  client_id={client-id}&
  response_type=code&
  redirect_uri={redirect-uri}&
  scope=api://{client-id}/Files.Read&
  response_mode=query
```

### Step 2: Exchange Code for JWT Token

```bash
curl -X POST "https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id={client-id}" \
  -d "client_secret={client-secret}" \
  -d "code={authorization-code}" \
  -d "redirect_uri={redirect-uri}" \
  -d "grant_type=authorization_code"
```

Response:

```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "...",
  "scope": "api://your-client-id/Files.Read"
}
```

### Step 3: Use JWT Token with API

```bash
curl -X POST "https://your-api-management-url/chat" \
  -H "Authorization: Bearer {jwt-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Hello, can you help me?"
      }
    ],
    "chatId": "chat_123456789",
    "miniId": "mini_987654321",
    "timezone": "Australia/Sydney"
  }'
```

## Python Example

```python
import requests
import webbrowser
from urllib.parse import parse_qs, urlparse
import json

class MinikaiChatClient:
    def __init__(self, tenant_id, client_id, client_secret, api_base_url):
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret
        self.api_base_url = api_base_url
        self.access_token = None

    def authenticate(self, redirect_uri="http://localhost:8080/callback"):
        """Perform OAuth2 authorization code flow"""

        # Step 1: Get authorization code
        auth_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/authorize"
        params = {
            "client_id": self.client_id,
            "response_type": "code",
            "redirect_uri": redirect_uri,
            "scope": f"api://{self.client_id}/Files.Read",
            "response_mode": "query"
        }

        auth_request_url = auth_url + "?" + "&".join([f"{k}={v}" for k, v in params.items()])
        print(f"Please visit this URL to authorize: {auth_request_url}")
        webbrowser.open(auth_request_url)

        # Get the authorization code from user
        callback_url = input("Paste the full callback URL here: ")
        parsed_url = urlparse(callback_url)
        query_params = parse_qs(parsed_url.query)
        auth_code = query_params.get('code', [None])[0]

        if not auth_code:
            raise Exception("Authorization code not found in callback URL")

        # Step 2: Exchange code for token
        token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        token_data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": auth_code,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code"
        }

        response = requests.post(token_url, data=token_data)
        response.raise_for_status()

        token_response = response.json()
        self.access_token = token_response["access_token"]
        print("Authentication successful!")

        return self.access_token

    def send_chat_message(self, messages, chat_id, mini_id, timezone=None):
        """Send a chat message to the API"""
        if not self.access_token:
            raise Exception("Not authenticated. Call authenticate() first.")

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        payload = {
            "messages": messages,
            "chatId": chat_id,
            "miniId": mini_id
        }

        if timezone:
            payload["timezone"] = timezone

        response = requests.post(
            f"{self.api_base_url}/chat",
            headers=headers,
            json=payload,
            stream=True  # For streaming response
        )

        response.raise_for_status()
        return response

# Usage example
if __name__ == "__main__":
    client = MinikaiChatClient(
        tenant_id="your-tenant-id",
        client_id="your-client-id",
        client_secret="your-client-secret",
        api_base_url="https://your-api-management-url"
    )

    # Authenticate
    client.authenticate()

    # Send a chat message
    messages = [
        {"role": "user", "content": "Hello, can you help me with my query?"}
    ]

    response = client.send_chat_message(
        messages=messages,
        chat_id="chat_123456789",
        mini_id="mini_987654321",
        timezone="Australia/Sydney"
    )

    # Handle streaming response
    for line in response.iter_lines():
        if line:
            print(line.decode('utf-8'))
```

## JavaScript/Node.js Example

```javascript
const axios = require('axios')
const open = require('open')
const readline = require('readline')

class MinikaiChatClient {
  constructor(tenantId, clientId, clientSecret, apiBaseUrl) {
    this.tenantId = tenantId
    this.clientId = clientId
    this.clientSecret = clientSecret
    this.apiBaseUrl = apiBaseUrl
    this.accessToken = null
  }

  async authenticate(redirectUri = 'http://localhost:8080/callback') {
    // Step 1: Get authorization code
    const authUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/authorize`
    const params = new URLSearchParams({
      client_id: this.clientId,
      response_type: 'code',
      redirect_uri: redirectUri,
      scope: `api://${this.clientId}/Files.Read`,
      response_mode: 'query',
    })

    const authRequestUrl = `${authUrl}?${params.toString()}`
    console.log(`Please visit this URL to authorize: ${authRequestUrl}`)
    await open(authRequestUrl)

    // Get authorization code from user
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    })

    const callbackUrl = await new Promise(resolve => {
      rl.question('Paste the full callback URL here: ', resolve)
    })
    rl.close()

    const url = new URL(callbackUrl)
    const authCode = url.searchParams.get('code')

    if (!authCode) {
      throw new Error('Authorization code not found in callback URL')
    }

    // Step 2: Exchange code for token
    const tokenUrl = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`
    const tokenData = new URLSearchParams({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      code: authCode,
      redirect_uri: redirectUri,
      grant_type: 'authorization_code',
    })

    const response = await axios.post(tokenUrl, tokenData, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })

    this.accessToken = response.data.access_token
    console.log('Authentication successful!')
    return this.accessToken
  }

  async sendChatMessage(messages, chatId, miniId, timezone = null) {
    if (!this.accessToken) {
      throw new Error('Not authenticated. Call authenticate() first.')
    }

    const payload = {
      messages,
      chatId,
      miniId,
      ...(timezone && { timezone }),
    }

    const response = await axios.post(`${this.apiBaseUrl}/chat`, payload, {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
      responseType: 'stream',
    })

    return response
  }
}

// Usage example
async function main() {
  const client = new MinikaiChatClient(
    'your-tenant-id',
    'your-client-id',
    'your-client-secret',
    'https://your-api-management-url'
  )

  try {
    // Authenticate
    await client.authenticate()

    // Send a chat message
    const messages = [{ role: 'user', content: 'Hello, can you help me with my query?' }]

    const response = await client.sendChatMessage(messages, 'chat_123456789', 'mini_987654321', 'Australia/Sydney')

    // Handle streaming response
    response.data.on('data', chunk => {
      console.log(chunk.toString())
    })
  } catch (error) {
    console.error('Error:', error.message)
  }
}

main()
```

## API Response Format

The API returns a streaming response with server-sent events. Each chunk contains part of the AI's response.

### Success Response (200)

- Content-Type: `text/plain`
- Body: Streaming text response from the AI

### Error Responses

#### 400 Bad Request

```json
{
  "error": "Invalid request format",
  "details": {
    /* validation error details */
  },
  "success": false
}
```

#### 401 Unauthorized

```json
{
  "error": "Authentication failed",
  "message": "Invalid or missing JWT token. Please authenticate using Azure AD OAuth2 flow.",
  "success": false
}
```

#### 429 Too Many Requests

```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Maximum 1000 requests per minute allowed.",
  "success": false
}
```

#### 500 Internal Server Error

```json
{
  "error": "Server error",
  "success": false
}
```

## Rate Limiting

- **Limit**: 1000 requests per minute per IP address
- **Response**: HTTP 429 when exceeded
- **Reset**: Counter resets every minute

## Security Notes

1. **JWT Validation**: All requests must include a valid JWT token from Azure AD
2. **HTTPS Only**: All communication must use HTTPS
3. **Token Expiry**: JWT tokens expire after 1 hour by default
4. **Scope Required**: Tokens must include the `api://{client-id}/Files.Read` scope

## Troubleshooting

### Common Issues

1. **401 Unauthorized**

   - Check JWT token is valid and not expired
   - Verify token includes correct audience (`api://{client-id}`)
   - Ensure Authorization header format: `Bearer {token}`

2. **400 Bad Request**

   - Validate JSON payload structure
   - Ensure required fields are present: `messages`, `chatId`, `miniId`
   - Check message format: each message needs `role` and `content`

3. **429 Rate Limited**

   - Implement exponential backoff in your client
   - Consider distributing requests across multiple IP addresses if needed

4. **500 Server Error**
   - Check if the Mini ID exists and is accessible
   - Verify the chat ID is valid
   - Contact support if issue persists

## Support

For additional support or questions about the API, please contact the Minikai development team.
