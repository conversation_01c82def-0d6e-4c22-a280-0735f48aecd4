<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <rate-limit-by-key calls="10000" renewal-period="60" counter-key="@(context.Request.IpAddress)" />
        <cors>
            <allowed-origins>
                <origin>{{ApiManagementDeveloperPortalUrl}}</origin>
            </allowed-origins>
            <allowed-methods>
                <method>GET</method>
                <method>POST</method>
                <method>PUT</method>
                <method>DELETE</method>
                <method>OPTIONS</method>
            </allowed-methods>
            <allowed-headers>
                <header>*</header>
            </allowed-headers>
        </cors>
        <validate-jwt header-name="Authorization" failed-validation-httpcode="401" failed-validation-error-message="Unauthorized. Access token is missing or invalid.">
            <openid-config url="{{JwtIssuerUrl}}/v2.0/.well-known/openid-configuration" />
            <audiences>
                <audience>{{FrontendClientId}}</audience>
            </audiences>
        </validate-jwt>
        <!-- Request an OAuth token -->
        <send-request mode="new" response-variable-name="tokenResponse" timeout="20" ignore-error="false">
            <set-url>{{iCareUrl}}/OAuth/Token</set-url>
            <set-method>POST</set-method>
            <set-header name="Content-Type" exists-action="override">
                <value>application/x-www-form-urlencoded</value>
            </set-header>
            <set-body>grant_type=client_credentials&amp;client_id={{iCareClientId}}&amp;client_secret={{iCareClientSecret}}</set-body>
        </send-request>
        <!-- Extract the token from the response -->
        <set-variable name="accessToken" value="@(((IResponse)context.Variables[&quot;tokenResponse&quot;]).Body.As&lt;JObject&gt;()[&quot;access_token&quot;].ToString())" />
        <!-- Set Authorization header for backend call -->
        <set-header name="Authorization" exists-action="override">
            <value>@("Bearer " + context.Variables["accessToken"])</value>
        </set-header>
        <set-backend-service backend-id="{{iCareBackendId}}" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
