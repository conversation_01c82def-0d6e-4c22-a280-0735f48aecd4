<policies>
  <inbound>
    <base />
    <rate-limit-by-key calls="10000" renewal-period="60" counter-key="@(context.Request.IpAddress)" />
    <cors>
      <allowed-origins>
        <origin>{{ApiManagementDeveloperPortalUrl}}</origin>
        <origin>{{FrontendCustomDomainOrigin}}</origin>
      </allowed-origins>
      <allowed-methods>
        <method>GET</method>
        <method>POST</method>
        <method>PUT</method>
        <method>DELETE</method>
        <method>OPTIONS</method>
      </allowed-methods>
      <allowed-headers>
        <header>*</header>
      </allowed-headers>
    </cors>
    <set-backend-service backend-id="{{MixpanelBackendId}}" />
  </inbound>
  <backend>
    <base />
  </backend>
  <outbound>
    <base />
  </outbound>
  <on-error>
    <base />
  </on-error>
</policies>
