<policies>
  <inbound>
    <base />
    <rate-limit-by-key calls="10000" renewal-period="60" counter-key="@(context.Request.IpAddress)" />
    <cors>
      <allowed-origins>
        <origin>{{ApiManagementDeveloperPortalUrl}}</origin>
        <origin>{{FrontendCustomDomainOrigin}}</origin>
      </allowed-origins>
      <allowed-methods>
        <method>GET</method>
        <method>POST</method>
        <method>PUT</method>
        <method>DELETE</method>
        <method>OPTIONS</method>
      </allowed-methods>
      <allowed-headers>
        <header>*</header>
      </allowed-headers>
    </cors>
    <validate-jwt header-name="Authorization" failed-validation-httpcode="401" failed-validation-error-message="Unauthorized. Access token is missing or invalid.">
      <openid-config url="{{JwtIssuerUrl}}/v2.0/.well-known/openid-configuration" />
      <audiences>
        <audience>{{FrontendClientId}}</audience>
      </audiences>
    </validate-jwt>
    <set-backend-service backend-id="{{OpenAiBackendId}}" />
    <authentication-managed-identity resource="https://cognitiveservices.azure.com" />
  </inbound>
  <backend>
    <base />
  </backend>
  <outbound>
    <base />
  </outbound>
  <on-error>
    <base />
  </on-error>
</policies>
