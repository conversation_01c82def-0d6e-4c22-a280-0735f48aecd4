<policies>
  <inbound>
    <base />
    <rate-limit-by-key calls="1000" renewal-period="60" counter-key="@(context.Request.IpAddress)" />
    <cors>
      <allowed-origins>
        <origin>{{ApiManagementDeveloperPortalUrl}}</origin>
        <origin>{{FrontendCustomDomainOrigin}}</origin>
      </allowed-origins>
      <allowed-methods>
        <method>GET</method>
        <method>POST</method>
        <method>OPTIONS</method>
      </allowed-methods>
      <allowed-headers>
        <header>*</header>
      </allowed-headers>
    </cors>
    <validate-jwt header-name="Authorization" failed-validation-httpcode="401" failed-validation-error-message="Unauthorized. Access token is missing or invalid.">
      <openid-config url="{{JwtIssuerUrl}}/v2.0/.well-known/openid-configuration" />
      <audiences>
        <audience>{{FrontendClientId}}</audience>
      </audiences>
    </validate-jwt>
    <set-header name="X-User-Token" exists-action="override">
      <value>@(context.Request.Headers.GetValueOrDefault("Authorization",""))</value>
    </set-header>
    <set-backend-service backend-id="{{MinikaiToolsBackendName}}" />
    <authentication-certificate certificate-id="{{MinikaiToolsFunctionKey}}" />
  </inbound>
  <backend>
    <base />
  </backend>
  <outbound>
    <base />
  </outbound>
  <on-error>
    <base />
  </on-error>
</policies>
