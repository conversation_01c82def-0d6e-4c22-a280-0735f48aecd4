{"openapi": "3.0.1", "info": {"title": "Minikai Client API", "description": "NextJS API routes for the Minikai platform", "version": "1.0"}, "servers": [{"url": "https://app.{env}.minikai.com/api"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token obtained from Microsoft Azure AD OAuth2 authorization code flow"}}, "schemas": {"CreateChatRequest": {"type": "object", "required": ["miniId", "firstMessageContent"], "properties": {"miniId": {"type": "integer", "description": "Unique identifier for the Mini (AI assistant configuration)", "example": 123}, "firstMessageContent": {"type": "string", "description": "Content of the first message to start the chat", "example": "Hello, I need help with my evaluation task", "minLength": 1}}}, "CreateChatResponse": {"type": "object", "properties": {"chatId": {"type": "string", "description": "Unique identifier for the created chat session (GUID format)", "example": "550e8400-e29b-41d4-a716-************"}, "success": {"type": "boolean", "description": "Whether the chat was created successfully", "example": true}, "error": {"type": "string", "description": "Error message if creation failed"}}}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message describing what went wrong"}, "details": {"type": "object", "description": "Additional error details (for validation errors)"}, "success": {"type": "boolean", "description": "Always false for error responses", "example": false}, "message": {"type": "string", "description": "Human-readable error message"}}}, "GetMessagesResponse": {"type": "object", "properties": {"messages": {"type": "array", "description": "Array of all messages in the chat including system messages", "items": {"$ref": "#/components/schemas/UIMessage"}}, "success": {"type": "boolean", "description": "Whether the messages were retrieved successfully", "example": true}, "error": {"type": "string", "description": "Error message if retrieval failed"}}}, "UIMessage": {"type": "object", "required": ["role", "content"], "properties": {"role": {"type": "string", "enum": ["user", "assistant", "system"], "description": "The role of the message sender"}, "content": {"type": "string", "description": "The text content of the message", "example": "Hello, can you help me with my query?"}, "id": {"type": "string", "description": "Optional unique identifier for the message"}, "createdAt": {"type": "string", "format": "date-time", "description": "Optional timestamp when the message was created"}, "parts": {"type": "array", "description": "Optional array of message parts for complex messages", "items": {"type": "object", "description": "Message part (text, tool invocation, etc.)"}}, "annotations": {"type": "array", "description": "Optional annotations for the message", "items": {"type": "object", "description": "Message annotation"}}}}, "ChatRequest": {"type": "object", "required": ["messages", "chatId", "miniId"], "properties": {"messages": {"type": "array", "description": "Array of chat messages in the conversation", "items": {"$ref": "#/components/schemas/UIMessage"}, "minItems": 1}, "chatId": {"type": "string", "description": "Unique identifier for the chat session (GUID format)", "example": "550e8400-e29b-41d4-a716-************"}, "miniId": {"type": "string", "description": "Unique identifier for the Mini (AI assistant configuration)", "example": "123"}, "timezone": {"type": "string", "description": "Optional timezone for the user", "example": "Australia/Sydney"}}}, "SendMessageResponse": {"type": "object", "properties": {"message": {"$ref": "#/components/schemas/UIMessage"}, "success": {"type": "boolean", "description": "Whether the message was processed successfully", "example": true}, "usage": {"$ref": "#/components/schemas/TokenUsage"}, "error": {"type": "string", "description": "Error message if processing failed"}}}, "TokenUsage": {"type": "object", "properties": {"promptTokens": {"type": "integer", "description": "Number of tokens used in the prompt", "example": 150}, "completionTokens": {"type": "integer", "description": "Number of tokens used in the completion", "example": 75}, "totalTokens": {"type": "integer", "description": "Total number of tokens used", "example": 225}}}}}, "security": [{"BearerAuth": []}], "paths": {"/evals/create-chat": {"post": {"summary": "Create Chat for Evaluation", "description": "Create a new chat session for evaluation purposes", "operationId": "createEvalChat", "tags": ["Evals"], "security": [{"BearerAuth": []}], "requestBody": {"description": "Chat creation request", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatRequest"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatResponse"}}}}, "400": {"description": "Bad Request - Invalid request format or missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing JWT token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/evals/get-messages": {"get": {"summary": "Get All Chat Messages for Evaluation", "description": "Retrieve all messages for a chat session including system messages for evaluation purposes", "operationId": "getEvalMessages", "tags": ["Evals"], "security": [{"BearerAuth": []}], "parameters": [{"name": "chatId", "in": "query", "required": true, "description": "Unique identifier for the chat session (GUID format)", "schema": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}}], "responses": {"200": {"description": "All messages in the chat session", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMessagesResponse"}}}}, "400": {"description": "Bad Request - Missing or invalid chat ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing JWT token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/evals/send-message": {"post": {"summary": "Send Message for Evaluation", "description": "Send a message to AI chat and receive complete response (non-streaming) with token usage for evaluation", "operationId": "sendEvalMessage", "tags": ["Evals"], "security": [{"BearerAuth": []}], "requestBody": {"description": "Chat request containing messages and context", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequest"}}}}, "responses": {"200": {"description": "Complete chat response with token usage", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageResponse"}}}}, "400": {"description": "Bad Request - Invalid request format or missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing JWT token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "tags": []}