{"openapi": "3.0.1", "info": {"title": "Mixpanel", "description": "", "version": "1.0"}, "servers": [{"url": "https://api.dev.minikai.com/mixpanel"}], "paths": {"/{*path}": {"get": {"summary": "Mixpanel Get", "operationId": "mixpanel-get", "parameters": [{"name": "*path", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "post": {"summary": "Mixpanel Post", "operationId": "mixpanel-post", "parameters": [{"name": "*path", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"securitySchemes": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}]}