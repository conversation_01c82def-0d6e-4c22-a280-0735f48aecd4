openapi: 3.1.1
info:
  title: OpenAI
  version: '1.0'
servers:
  - url: https://api.dev.minikai.com/openai
paths:
  '/deployments/{deployment-id}/completions':
    post:
      summary: 'Creates a completion for the provided prompt, parameters and chosen model.'
      description: 'Creates a completion for the provided prompt, parameters and chosen model.'
      operationId: Completions_Create
      parameters:
        - name: deployment-id
          in: path
          required: true
          schema:
            type: string
            description: Deployment id of the model which was deployed.
            example: davinci
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                prompt:
                  oneOf:
                    - type: string
                      default: ''
                      nullable: true
                      example: This is a test.
                    - type: array
                      items:
                        type: string
                        default: ''
                        example: This is a test.
                      description: Array size minimum of 1 and maximum of 2048
                  description:
                    "The prompt(s) to generate completions for, encoded as a string or array of strings.\nNote that
                    <|endoftext|> is the document separator that the model sees during training, so if a prompt is not
                    specified the model will generate as if from the beginning of a new document. Maximum allowed size
                    of string list is 2048."
                max_tokens:
                  type: integer
                  description:
                    "The token count of your prompt plus max_tokens cannot exceed the model's context length. Most
                    models have a context length of 2048 tokens (except for the newest models, which support 4096). Has
                    minimum of 0."
                  default: 16
                  nullable: true
                  example: 16
                temperature:
                  type: number
                  description:
                    "What sampling temperature to use. Higher values means the model will take more risks. Try 0.9 for
                    more creative applications, and 0 (argmax sampling) for ones with a well-defined answer.\nWe
                    generally recommend altering this or top_p but not both."
                  default: 1
                  nullable: true
                  example: 1
                top_p:
                  type: number
                  description:
                    "An alternative to sampling with temperature, called nucleus sampling, where the model considers the
                    results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top
                    10% probability mass are considered.\nWe generally recommend altering this or temperature but not
                    both."
                  default: 1
                  nullable: true
                  example: 1
                logit_bias:
                  type: object
                  description:
                    'Defaults to null. Modify the likelihood of specified tokens appearing in the completion. Accepts a
                    json object that maps tokens (specified by their token ID in the GPT tokenizer) to an associated
                    bias value from -100 to 100. You can use this tokenizer tool (which works for both GPT-2 and GPT-3)
                    to convert text to token IDs. Mathematically, the bias is added to the logits generated by the model
                    prior to sampling. The exact effect will vary per model, but values between -1 and 1 should decrease
                    or increase likelihood of selection; values like -100 or 100 should result in a ban or exclusive
                    selection of the relevant token. As an example, you can pass {"50256" &#58; -100} to prevent the
                    <|endoftext|> token from being generated.'
                user:
                  type: string
                  description:
                    'A unique identifier representing your end-user, which can help monitoring and detecting abuse'
                n:
                  type: integer
                  description:
                    "How many completions to generate for each prompt. Minimum of 1 and maximum of 128 allowed.\nNote:
                    Because this parameter generates many completions, it can quickly consume your token quota. Use
                    carefully and ensure that you have reasonable settings for max_tokens and stop."
                  default: 1
                  nullable: true
                  example: 1
                stream:
                  type: boolean
                  description:
                    'Whether to stream back partial progress. If set, tokens will be sent as data-only server-sent
                    events as they become available, with the stream terminated by a data: [DONE] message.'
                  default: false
                  nullable: true
                logprobs:
                  type: integer
                  description:
                    "Include the log probabilities on the logprobs most likely tokens, as well the chosen tokens. For
                    example, if logprobs is 5, the API will return a list of the 5 most likely tokens. The API will
                    always return the logprob of the sampled token, so there may be up to logprobs+1 elements in the
                    response.\nMinimum of 0 and maximum of 5 allowed."
                  default:
                  nullable: true
                suffix:
                  type: string
                  description: The suffix that comes after a completion of inserted text.
                  nullable: true
                echo:
                  type: boolean
                  description: Echo back the prompt in addition to the completion
                  default: false
                  nullable: true
                stop:
                  oneOf:
                    - type: string
                      default: <|endoftext|>
                      nullable: true
                      example: "\n"
                    - type: array
                      items:
                        type: string
                        example: "\n"
                      description: Array minimum size of 1 and maximum of 4
                  description:
                    Up to 4 sequences where the API will stop generating further tokens. The returned text will not
                    contain the stop sequence.
                completion_config:
                  type: string
                  nullable: true
                presence_penalty:
                  type: number
                  description:
                    "Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in
                    the text so far, increasing the model's likelihood to talk about new topics."
                  default: 0
                frequency_penalty:
                  type: number
                  description:
                    "Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency
                    in the text so far, decreasing the model's likelihood to repeat the same line verbatim."
                  default: 0
                best_of:
                  type: integer
                  description:
                    "Generates best_of completions server-side and returns the \"best\" (the one with the highest log
                    probability per token). Results cannot be streamed.\nWhen used with n, best_of controls the number
                    of candidate completions and n specifies how many to return - best_of must be greater than n.\nNote:
                    Because this parameter generates many completions, it can quickly consume your token quota. Use
                    carefully and ensure that you have reasonable settings for max_tokens and stop. Has maximum value of
                    128."
            example:
              prompt:
                "Negate the following sentence.The price for bubblegum increased on thursday.\n\n Negated Sentence:"
              max_tokens: 50
      responses:
        '200':
          description: OK
          headers:
            apim-request-id:
              description: Request ID for troubleshooting purposes
              schema:
                type: string
          content:
            application/json:
              schema:
                required:
                  - id
                  - object
                  - created
                  - model
                  - choices
                type: object
                properties:
                  id:
                    type: string
                  object:
                    type: string
                  created:
                    type: integer
                  model:
                    type: string
                  prompt_filter_results:
                    $ref: '#/components/schemas/promptFilterResults'
                  choices:
                    type: array
                    items:
                      type: object
                      properties:
                        text:
                          type: string
                        index:
                          type: integer
                        logprobs:
                          type: object
                          properties:
                            tokens:
                              type: array
                              items:
                                type: string
                            token_logprobs:
                              type: array
                              items:
                                type: number
                            top_logprobs:
                              type: array
                              items:
                                type: object
                                additionalProperties:
                                  type: number
                            text_offset:
                              type: array
                              items:
                                type: integer
                          nullable: true
                        finish_reason:
                          type: string
                        content_filter_results:
                          $ref: '#/components/schemas/contentFilterChoiceResults'
                  usage:
                    required:
                      - prompt_tokens
                      - total_tokens
                      - completion_tokens
                    type: object
                    properties:
                      completion_tokens:
                        type: number
                        format: int32
                      prompt_tokens:
                        type: number
                        format: int32
                      total_tokens:
                        type: number
                        format: int32
              example:
                model: davinci
                object: text_completion
                id: cmpl-4509KAos68kxOqpE2uYGw81j6m7uo
                created: 1637097562
                choices:
                  - index: 0
                    text: The price for bubblegum decreased on thursday.
                    logprobs:
                    finish_reason: stop
        '400':
          description: Service unavailable
          headers:
            apim-request-id:
              description: Request ID for troubleshooting purposes
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
              example:
                error:
                  param: string
                  type: string
                  inner_error:
                    code: ResponsibleAIPolicyViolation
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      custom_blocklists:
                        - filtered: true
                          id: string
                      error:
                        code: string
                        message: string
                      jailbreak:
                        filtered: true
                        detected: true
                  code: string
                  message: string
        '500':
          description: Service unavailable
          headers:
            apim-request-id:
              description: Request ID for troubleshooting purposes
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
              example:
                error:
                  param: string
                  type: string
                  inner_error:
                    code: ResponsibleAIPolicyViolation
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      custom_blocklists:
                        - filtered: true
                          id: string
                      error:
                        code: string
                        message: string
                      jailbreak:
                        filtered: true
                        detected: true
                  code: string
                  message: string
  '/deployments/{deployment-id}/embeddings':
    post:
      summary:
        Get a vector representation of a given input that can be easily consumed by machine learning models and
        algorithms.
      description:
        Get a vector representation of a given input that can be easily consumed by machine learning models and
        algorithms.
      operationId: embeddings_create
      parameters:
        - name: deployment-id
          in: path
          description: The deployment id of the model which was deployed.
          required: true
          schema:
            type: string
            example: ada-search-index-v1
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              required:
                - input
              type: object
              properties:
                input:
                  oneOf:
                    - type: string
                      default: ''
                      nullable: true
                      example: This is a test.
                    - maxItems: 2048
                      minItems: 1
                      type: array
                      items:
                        minLength: 1
                        type: string
                        example: This is a test.
                  description:
                    "Input text to get embeddings for, encoded as a string. To get embeddings for multiple inputs in a
                    single request, pass an array of strings. Each input must not exceed 2048 tokens in length.\nUnless
                    you are embedding code, we suggest replacing newlines (\\n) in your input with a single space, as we
                    have observed inferior results when newlines are present."
                user:
                  type: string
                  description:
                    'A unique identifier representing your end-user, which can help monitoring and detecting abuse.'
                input_type:
                  type: string
                  description: input type of embedding search to use
                  example: query
                encoding_format:
                  type: string
                  description:
                    The format to return the embeddings in. Can be either `float` or `base64`. Defaults to `float`.
                  nullable: true
                  example: float
                dimensions:
                  type: integer
                  description:
                    The number of dimensions the resulting output embeddings should have. Only supported in
                    `text-embedding-3` and later models.
                  nullable: true
                  example: 1
            example:
              input: {}
              user: string
              input_type: query
              encoding_format: float
              dimensions: 1
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                required:
                  - object
                  - model
                  - data
                  - usage
                type: object
                properties:
                  object:
                    type: string
                  model:
                    type: string
                  data:
                    type: array
                    items:
                      required:
                        - index
                        - object
                        - embedding
                      type: object
                      properties:
                        index:
                          type: integer
                        object:
                          type: string
                        embedding:
                          type: array
                          items:
                            type: number
                  usage:
                    required:
                      - prompt_tokens
                      - total_tokens
                    type: object
                    properties:
                      prompt_tokens:
                        type: integer
                      total_tokens:
                        type: integer
              example:
                object: string
                model: string
                data:
                  - index: 0
                    object: string
                    embedding:
                      - 0
                usage:
                  prompt_tokens: 0
                  total_tokens: 0
  '/deployments/{deployment-id}/chat/completions':
    post:
      summary: Creates a completion for the chat message
      description: Creates a completion for the chat message
      operationId: ChatCompletions_Create
      parameters:
        - name: deployment-id
          in: path
          required: true
          schema:
            type: string
            description: Deployment id of the model which was deployed.
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createChatCompletionRequest'
            example:
              temperature: 1
              top_p: 1
              stream: false
              stop:
              max_tokens: 4096
              presence_penalty: 0
              frequency_penalty: 0
              logit_bias: {}
              user: user-1234
              messages:
                - role: system
              data_sources:
                - type: azure_search
              enhancements:
                grounding:
                  enabled: false
                ocr:
                  enabled: false
              n: 1
              seed: 0
              logprobs: false
              top_logprobs: 0
              response_format:
                type: text
              tools:
                - type: function
                  function:
                    description: string
                    name: string
                    parameters: {}
              tool_choice: {}
              functions:
                - name: string
                  description: string
                  parameters: {}
              function_call: {}
      responses:
        '200':
          description: OK
          headers:
            apim-request-id:
              description: Request ID for troubleshooting purposes
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/createChatCompletionResponse'
              example:
                id: string
                object: chat.completion
                created: 0
                model: string
                usage:
                  prompt_tokens: 0
                  completion_tokens: 0
                  total_tokens: 0
                system_fingerprint: string
                prompt_filter_results:
                  - prompt_index: 0
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      custom_blocklists:
                        - filtered: true
                          id: string
                      error:
                        code: string
                        message: string
                      jailbreak:
                        filtered: true
                        detected: true
                choices:
                  - index: 0
                    finish_reason: string
                    message:
                      role: assistant
                      content: string
                      tool_calls:
                        - id: string
                          type: function
                          function:
                            name: string
                            arguments: string
                      function_call:
                        name: string
                        arguments: string
                      context:
                        citations:
                          - content: string
                            title: string
                            url: string
                            filepath: string
                            chunk_id: string
                        intent: string
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      custom_blocklists:
                        - filtered: true
                          id: string
                      error:
                        code: string
                        message: string
                      protected_material_text:
                        filtered: true
                        detected: true
                      protected_material_code:
                        filtered: true
                        detected: true
                        citation:
                          URL: string
                          license: string
                    logprobs:
                      content:
                        - token: string
                          logprob: 0
                          bytes:
                            - 0
                          top_logprobs:
                            - token: string
                              logprob: 0
                              bytes:
                                - 0
                    enhancements:
                      grounding:
                        lines:
                          - text: string
                            spans:
                              - text: string
                                offset: 0
                                length: 0
                                polygon:
                                  - x: 0
                                    y: 0
        '400':
          description: Service unavailable
          headers:
            apim-request-id:
              description: Request ID for troubleshooting purposes
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
              example:
                error:
                  param: string
                  type: string
                  inner_error:
                    code: ResponsibleAIPolicyViolation
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      custom_blocklists:
                        - filtered: true
                          id: string
                      error:
                        code: string
                        message: string
                      jailbreak:
                        filtered: true
                        detected: true
                  code: string
                  message: string
        '500':
          description: Service unavailable
          headers:
            apim-request-id:
              description: Request ID for troubleshooting purposes
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
              example:
                error:
                  param: string
                  type: string
                  inner_error:
                    code: ResponsibleAIPolicyViolation
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      custom_blocklists:
                        - filtered: true
                          id: string
                      error:
                        code: string
                        message: string
                      jailbreak:
                        filtered: true
                        detected: true
                  code: string
                  message: string
  '/deployments/{deployment-id}/audio/transcriptions':
    post:
      summary: Transcribes audio into the input language.
      description: Transcribes audio into the input language.
      operationId: Transcriptions_Create
      parameters:
        - name: deployment-id
          in: path
          required: true
          schema:
            type: string
            description: Deployment id of the whisper model.
            example: whisper
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  format: binary
                prompt:
                  type: string
                response_format:
                  $ref: '#/components/schemas/audioResponseFormat'
                temperature:
                  type: number
                  default: 0
                language:
                  type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/audioResponse'
                  - $ref: '#/components/schemas/audioVerboseResponse'
              example:
                text: string
            text/plain:
              schema:
                type: string
                description: 'Transcribed text in the output format (when response_format was one of text, vtt or srt).'
              examples:
                default:
                  value:
  '/deployments/{deployment-id}/audio/translations':
    post:
      summary: Transcribes and translates input audio into English text.
      description: Transcribes and translates input audio into English text.
      operationId: Translations_Create
      parameters:
        - name: deployment-id
          in: path
          required: true
          schema:
            type: string
            description: Deployment id of the whisper model which was deployed.
            example: whisper
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  format: binary
                prompt:
                  type: string
                response_format:
                  $ref: '#/components/schemas/audioResponseFormat'
                temperature:
                  type: number
                  default: 0
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/audioResponse'
                  - $ref: '#/components/schemas/audioVerboseResponse'
              example:
                text: string
            text/plain:
              schema:
                type: string
                description: 'Transcribed text in the output format (when response_format was one of text, vtt or srt).'
              examples:
                default:
                  value:
  '/deployments/{deployment-id}/audio/speech':
    post:
      summary: Generates audio from the input text.
      description: Generates audio from the input text.
      operationId: Speech_Create
      parameters:
        - name: deployment-id
          in: path
          required: true
          schema:
            type: string
            description: Deployment id of the tts model which was deployed.
            example: tts-1
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                input:
                  type: string
                voice:
                  enum:
                    - alloy
                    - echo
                    - fable
                    - onyx
                    - nova
                    - shimmer
                  type: string
                response_format:
                  enum:
                    - mp3
                    - opus
                    - aac
                    - flac
                  type: string
                speed:
                  type: number
                  default: 1
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
              examples:
                default:
                  value:
  '/deployments/{deployment-id}/images/generations':
    post:
      summary: Generates a batch of images from a text caption on a given DALLE model deployment
      description: Generates a batch of images from a text caption on a given DALLE model deployment
      operationId: ImageGenerations_Create
      parameters:
        - name: deployment-id
          in: path
          required: true
          schema:
            type: string
            description: Deployment id of the dalle model which was deployed.
            example: dalle-deployment
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/imageGenerationsRequest'
            example:
              prompt: a corgi in a field
              n: 1
              size: 1024x1024
              response_format: url
              user: user123456
              quality: standard
              style: vivid
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/generateImagesResponse'
              example:
                created: '1676540381'
                data:
                  - url: https://www.contoso.com
                    b64_json: string
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                    revised_prompt: string
                    prompt_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      jailbreak:
                        filtered: true
                        detected: true
        '400':
          description: An error occurred.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/dalleErrorResponse'
              example:
                error:
                  param: string
                  type: string
                  inner_error:
                    code: ResponsibleAIPolicyViolation
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      jailbreak:
                        filtered: true
                        detected: true
                    revised_prompt: string
                  code: string
                  message: string
        '500':
          description: An error occurred.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/dalleErrorResponse'
              example:
                error:
                  param: string
                  type: string
                  inner_error:
                    code: ResponsibleAIPolicyViolation
                    content_filter_results:
                      sexual:
                        filtered: true
                        severity: safe
                      violence:
                        filtered: true
                        severity: safe
                      hate:
                        filtered: true
                        severity: safe
                      self_harm:
                        filtered: true
                        severity: safe
                      profanity:
                        filtered: true
                        detected: true
                      jailbreak:
                        filtered: true
                        detected: true
                    revised_prompt: string
                  code: string
                  message: string
  /assistants:
    get:
      tags:
        - Assistants
      summary: Returns a list of assistants.
      description: Returns a list of assistants.
      operationId: List_Assistants
      parameters:
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
        - name: limit
          in: query
          description:
            "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is
            20.\n"
          schema:
            type: integer
            default: 20
        - name: order
          in: query
          description:
            "Sort order by the `created_at` timestamp of the objects. `asc` for ascending order and `desc` for
            descending order.\n"
          schema:
            enum:
              - asc
              - desc
            type: string
            default: desc
            x-ms-enum:
              name: ListAssistantsOrder
              modelAsString: true
              values:
                - value: asc
                  description: Order results in ascending order
                - value: desc
                  description: Order results in descending order
        - name: after
          in: query
          description:
            "A cursor for use in pagination. `after` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            after=obj_foo in order to fetch the next page of the list.\n"
          schema:
            type: string
        - name: before
          in: query
          description:
            "A cursor for use in pagination. `before` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            before=obj_foo in order to fetch the previous page of the list.\n"
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listAssistantsResponse'
              example:
                object: list
                data:
                  - id: string
                    object: assistant
                    created_at: 0
                    name: string
                    description: string
                    model: string
                    instructions: string
                    tools:
                      - type: code_interpreter
                    file_ids:
                      - string
                    metadata: {}
                first_id: string
                last_id: string
                has_more: false
    post:
      tags:
        - Assistants
      summary: Create an assistant with a model and instructions.
      description: Create an assistant with a model and instructions.
      operationId: Create_Assistant
      parameters:
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createAssistantRequest'
            example:
              model: {}
              name: string
              description: string
              instructions: string
              tools:
                - type: code_interpreter
              file_ids:
                - string
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/assistantObject'
              example:
                id: string
                object: assistant
                created_at: 0
                name: string
                description: string
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
  '/assistants/{assistant_id}':
    get:
      tags:
        - Assistants
      summary: Retrieves an assistant.
      description: Retrieves an assistant.
      operationId: Get_Assistant
      parameters:
        - name: assistant_id
          in: path
          description: The ID of the assistant to retrieve.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/assistantObject'
              example:
                id: string
                object: assistant
                created_at: 0
                name: string
                description: string
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
    post:
      tags:
        - Assistant
      summary: Modifies an assistant.
      description: Modifies an assistant.
      operationId: Modify_Assistant
      parameters:
        - name: assistant_id
          in: path
          description: The ID of the assistant to modify.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/modifyAssistantRequest'
            example:
              model: {}
              name: string
              description: string
              instructions: string
              tools:
                - type: code_interpreter
              file_ids:
                - string
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/assistantObject'
              example:
                id: string
                object: assistant
                created_at: 0
                name: string
                description: string
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
    delete:
      tags:
        - Assistants
      summary: Delete an assistant.
      description: Delete an assistant.
      operationId: Delete_Assistant
      parameters:
        - name: assistant_id
          in: path
          description: The ID of the assistant to delete.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/deleteAssistantResponse'
              example:
                id: string
                deleted: true
                object: assistant.deleted
  /threads:
    post:
      tags:
        - Assistants
      summary: Create a thread.
      description: Create a thread.
      operationId: Create_Thread
      parameters:
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createThreadRequest'
            example:
              messages:
                - role: user
                  content: string
                  file_ids:
                    - string
                  metadata: {}
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/threadObject'
              example:
                id: string
                object: thread
                created_at: 0
                metadata: {}
  '/threads/{thread_id}':
    get:
      tags:
        - Assistants
      summary: Retrieves a thread.
      description: Retrieves a thread.
      operationId: Get_Thread
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to retrieve.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/threadObject'
              example:
                id: string
                object: thread
                created_at: 0
                metadata: {}
    post:
      tags:
        - Assistants
      summary: Modifies a thread.
      description: Modifies a thread.
      operationId: Modify_Thread
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to modify. Only the `metadata` can be modified.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/modifyThreadRequest'
            example:
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/threadObject'
              example:
                id: string
                object: thread
                created_at: 0
                metadata: {}
    delete:
      tags:
        - Assistants
      summary: Delete a thread.
      description: Delete a thread.
      operationId: Delete_Thread
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to delete.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/deleteThreadResponse'
              example:
                id: string
                deleted: true
                object: thread.deleted
  '/threads/{thread_id}/messages':
    get:
      tags:
        - Assistants
      summary: Returns a list of messages for a given thread.
      description: Returns a list of messages for a given thread.
      operationId: List_Messages
      parameters:
        - name: thread_id
          in: path
          description: 'The ID of the [thread](/docs/api-reference/threads) the messages belong to.'
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
        - name: limit
          in: query
          description:
            "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is
            20.\n"
          schema:
            type: integer
            default: 20
        - name: order
          in: query
          description:
            "Sort order by the `created_at` timestamp of the objects. `asc` for ascending order and `desc` for
            descending order.\n"
          schema:
            enum:
              - asc
              - desc
            type: string
            default: desc
            x-ms-enum:
              name: ListMessagesOrder
              modelAsString: true
              values:
                - value: asc
                  description: Order results in ascending order
                - value: desc
                  description: Order results in descending order
        - name: after
          in: query
          description:
            "A cursor for use in pagination. `after` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            after=obj_foo in order to fetch the next page of the list.\n"
          schema:
            type: string
        - name: before
          in: query
          description:
            "A cursor for use in pagination. `before` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            before=obj_foo in order to fetch the previous page of the list.\n"
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listMessagesResponse'
              example:
                object: list
                data:
                  - id: string
                    object: thread.message
                    created_at: 0
                    thread_id: string
                    role: user
                    content:
                      - type: image_file
                        image_file:
                          file_id: string
                    assistant_id: string
                    run_id: string
                    file_ids:
                      - string
                    metadata: {}
                first_id: string
                last_id: string
                has_more: false
    post:
      tags:
        - Assistants
      summary: Create a message.
      description: Create a message.
      operationId: Create_Message
      parameters:
        - name: thread_id
          in: path
          description: 'The ID of the [thread](/docs/api-reference/threads) to create a message for.'
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createMessageRequest'
            example:
              role: user
              content: string
              file_ids:
                - string
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/messageObject'
              example:
                id: string
                object: thread.message
                created_at: 0
                thread_id: string
                role: user
                content:
                  - type: image_file
                    image_file:
                      file_id: string
                assistant_id: string
                run_id: string
                file_ids:
                  - string
                metadata: {}
  '/threads/{thread_id}/messages/{message_id}':
    get:
      tags:
        - Assistants
      summary: Retrieve a message.
      description: Retrieve a message.
      operationId: Get_Message
      parameters:
        - name: thread_id
          in: path
          description: 'The ID of the [thread](/docs/api-reference/threads) to which this message belongs.'
          required: true
          schema:
            type: string
        - name: message_id
          in: path
          description: The ID of the message to retrieve.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/messageObject'
              example:
                id: string
                object: thread.message
                created_at: 0
                thread_id: string
                role: user
                content:
                  - type: image_file
                    image_file:
                      file_id: string
                assistant_id: string
                run_id: string
                file_ids:
                  - string
                metadata: {}
    post:
      tags:
        - Assistants
      summary: Modifies a message.
      description: Modifies a message.
      operationId: Modify_Message
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to which this message belongs.
          required: true
          schema:
            type: string
        - name: message_id
          in: path
          description: The ID of the message to modify.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/modifyMessageRequest'
            example:
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/messageObject'
              example:
                id: string
                object: thread.message
                created_at: 0
                thread_id: string
                role: user
                content:
                  - type: image_file
                    image_file:
                      file_id: string
                assistant_id: string
                run_id: string
                file_ids:
                  - string
                metadata: {}
  /threads/runs:
    post:
      tags:
        - Assistants
      summary: Create a thread and run it in one request.
      description: Create a thread and run it in one request.
      operationId: Create_Thread_And_Run
      parameters:
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createThreadAndRunRequest'
            example:
              assistant_id: string
              thread:
                messages:
                  - role: user
                    content: string
                    file_ids:
                      - string
                    metadata: {}
                metadata: {}
              model: string
              instructions: string
              tools:
                - type: code_interpreter
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runObject'
              example:
                id: string
                object: thread.run
                created_at: 0
                thread_id: string
                assistant_id: string
                status: queued
                required_action:
                  type: submit_tool_outputs
                  submit_tool_outputs:
                    tool_calls:
                      - id: string
                        type: function
                        function:
                          name: string
                          arguments: string
                last_error:
                  code: server_error
                  message: string
                expires_at: 0
                started_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
  '/threads/{thread_id}/runs':
    get:
      tags:
        - Assistants
      summary: Returns a list of runs belonging to a thread.
      description: Returns a list of runs belonging to a thread.
      operationId: List_Runs
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread the run belongs to.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
        - name: limit
          in: query
          description:
            "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is
            20.\n"
          schema:
            type: integer
            default: 20
        - name: order
          in: query
          description:
            "Sort order by the `created_at` timestamp of the objects. `asc` for ascending order and `desc` for
            descending order.\n"
          schema:
            enum:
              - asc
              - desc
            type: string
            default: desc
            x-ms-enum:
              name: ListRunsOrder
              modelAsString: true
              values:
                - value: asc
                  description: Order results in ascending order
                - value: desc
                  description: Order results in descending order
        - name: after
          in: query
          description:
            "A cursor for use in pagination. `after` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            after=obj_foo in order to fetch the next page of the list.\n"
          schema:
            type: string
        - name: before
          in: query
          description:
            "A cursor for use in pagination. `before` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            before=obj_foo in order to fetch the previous page of the list.\n"
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listRunsResponse'
              example:
                object: list
                data:
                  - id: string
                    object: thread.run
                    created_at: 0
                    thread_id: string
                    assistant_id: string
                    status: queued
                    required_action:
                      type: submit_tool_outputs
                      submit_tool_outputs:
                        tool_calls:
                          - id: string
                            type: function
                            function:
                              name: string
                              arguments: string
                    last_error:
                      code: server_error
                      message: string
                    expires_at: 0
                    started_at: 0
                    cancelled_at: 0
                    failed_at: 0
                    completed_at: 0
                    model: string
                    instructions: string
                    tools:
                      - type: code_interpreter
                    file_ids:
                      - string
                    metadata: {}
                first_id: string
                last_id: string
                has_more: false
    post:
      tags:
        - Assistants
      summary: Create a run.
      description: Create a run.
      operationId: Create_Run
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to run.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createRunRequest'
            example:
              assistant_id: string
              model: string
              instructions: string
              tools:
                - type: code_interpreter
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runObject'
              example:
                id: string
                object: thread.run
                created_at: 0
                thread_id: string
                assistant_id: string
                status: queued
                required_action:
                  type: submit_tool_outputs
                  submit_tool_outputs:
                    tool_calls:
                      - id: string
                        type: function
                        function:
                          name: string
                          arguments: string
                last_error:
                  code: server_error
                  message: string
                expires_at: 0
                started_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
  '/threads/{thread_id}/runs/{run_id}':
    get:
      tags:
        - Assistants
      summary: Retrieves a run.
      description: Retrieves a run.
      operationId: Get_Run
      parameters:
        - name: thread_id
          in: path
          description: 'The ID of the [thread](/docs/api-reference/threads) that was run.'
          required: true
          schema:
            type: string
        - name: run_id
          in: path
          description: The ID of the run to retrieve.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runObject'
              example:
                id: string
                object: thread.run
                created_at: 0
                thread_id: string
                assistant_id: string
                status: queued
                required_action:
                  type: submit_tool_outputs
                  submit_tool_outputs:
                    tool_calls:
                      - id: string
                        type: function
                        function:
                          name: string
                          arguments: string
                last_error:
                  code: server_error
                  message: string
                expires_at: 0
                started_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
    post:
      tags:
        - Assistants
      summary: Modifies a run.
      description: Modifies a run.
      operationId: Modify_Run
      parameters:
        - name: thread_id
          in: path
          description: 'The ID of the [thread](/docs/api-reference/threads) that was run.'
          required: true
          schema:
            type: string
        - name: run_id
          in: path
          description: The ID of the run to modify.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/modifyRunRequest'
            example:
              metadata: {}
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runObject'
              example:
                id: string
                object: thread.run
                created_at: 0
                thread_id: string
                assistant_id: string
                status: queued
                required_action:
                  type: submit_tool_outputs
                  submit_tool_outputs:
                    tool_calls:
                      - id: string
                        type: function
                        function:
                          name: string
                          arguments: string
                last_error:
                  code: server_error
                  message: string
                expires_at: 0
                started_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
  '/threads/{thread_id}/runs/{run_id}/submit_tool_outputs':
    post:
      tags:
        - Assistants
      summary:
        "When a run has the `status: \"requires_action\"` and `required_action.type` is `submit_tool_outputs`, this
        endpoint can be used to submit the outputs from the tool calls once they're all completed. All outputs must be
        submitted in a single request.\n"
      description:
        "When a run has the `status: \"requires_action\"` and `required_action.type` is `submit_tool_outputs`, this
        endpoint can be used to submit the outputs from the tool calls once they're all completed. All outputs must be
        submitted in a single request.\n"
      operationId: Submit_Tool_Outputs_To_Run
      parameters:
        - name: thread_id
          in: path
          description: 'The ID of the [thread](/docs/api-reference/threads) to which this run belongs.'
          required: true
          schema:
            type: string
        - name: run_id
          in: path
          description: The ID of the run that requires the tool output submission.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/submitToolOutputsRunRequest'
            example:
              tool_outputs:
                - tool_call_id: string
                  output: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runObject'
              example:
                id: string
                object: thread.run
                created_at: 0
                thread_id: string
                assistant_id: string
                status: queued
                required_action:
                  type: submit_tool_outputs
                  submit_tool_outputs:
                    tool_calls:
                      - id: string
                        type: function
                        function:
                          name: string
                          arguments: string
                last_error:
                  code: server_error
                  message: string
                expires_at: 0
                started_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
  '/threads/{thread_id}/runs/{run_id}/cancel':
    post:
      tags:
        - Assistants
      summary: Cancels a run that is `in_progress`.
      description: Cancels a run that is `in_progress`.
      operationId: Cancel_Run
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to which this run belongs.
          required: true
          schema:
            type: string
        - name: run_id
          in: path
          description: The ID of the run to cancel.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runObject'
              example:
                id: string
                object: thread.run
                created_at: 0
                thread_id: string
                assistant_id: string
                status: queued
                required_action:
                  type: submit_tool_outputs
                  submit_tool_outputs:
                    tool_calls:
                      - id: string
                        type: function
                        function:
                          name: string
                          arguments: string
                last_error:
                  code: server_error
                  message: string
                expires_at: 0
                started_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                model: string
                instructions: string
                tools:
                  - type: code_interpreter
                file_ids:
                  - string
                metadata: {}
  '/threads/{thread_id}/runs/{run_id}/steps':
    get:
      tags:
        - Assistants
      summary: Returns a list of run steps belonging to a run.
      description: Returns a list of run steps belonging to a run.
      operationId: List_Run_Steps
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread the run and run steps belong to.
          required: true
          schema:
            type: string
        - name: run_id
          in: path
          description: The ID of the run the run steps belong to.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
        - name: limit
          in: query
          description:
            "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is
            20.\n"
          schema:
            type: integer
            default: 20
        - name: order
          in: query
          description:
            "Sort order by the `created_at` timestamp of the objects. `asc` for ascending order and `desc` for
            descending order.\n"
          schema:
            enum:
              - asc
              - desc
            type: string
            default: desc
            x-ms-enum:
              name: ListRunStepsOrder
              modelAsString: true
              values:
                - value: asc
                  description: Order results in ascending order
                - value: desc
                  description: Order results in descending order
        - name: after
          in: query
          description:
            "A cursor for use in pagination. `after` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            after=obj_foo in order to fetch the next page of the list.\n"
          schema:
            type: string
        - name: before
          in: query
          description:
            "A cursor for use in pagination. `before` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            before=obj_foo in order to fetch the previous page of the list.\n"
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listRunStepsResponse'
              example:
                object: list
                data:
                  - id: string
                    object: assistant.run.step
                    created_at: 0
                    assistant_id: string
                    thread_id: string
                    run_id: string
                    type: message_creation
                    status: in_progress
                    step_details:
                      type: message_creation
                      message_creation:
                        message_id: string
                    last_error:
                      code: server_error
                      message: string
                    expired_at: 0
                    cancelled_at: 0
                    failed_at: 0
                    completed_at: 0
                    metadata: {}
                first_id: string
                last_id: string
                has_more: false
  '/threads/{thread_id}/runs/{run_id}/steps/{step_id}':
    get:
      tags:
        - Assistants
      summary: Retrieves a run step.
      description: Retrieves a run step.
      operationId: Get_Run_Step
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to which the run and run step belongs.
          required: true
          schema:
            type: string
        - name: run_id
          in: path
          description: The ID of the run to which the run step belongs.
          required: true
          schema:
            type: string
        - name: step_id
          in: path
          description: The ID of the run step to retrieve.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/runStepObject'
              example:
                id: string
                object: assistant.run.step
                created_at: 0
                assistant_id: string
                thread_id: string
                run_id: string
                type: message_creation
                status: in_progress
                step_details:
                  type: message_creation
                  message_creation:
                    message_id: string
                last_error:
                  code: server_error
                  message: string
                expired_at: 0
                cancelled_at: 0
                failed_at: 0
                completed_at: 0
                metadata: {}
  '/assistants/{assistant_id}/files':
    get:
      tags:
        - Assistants
      summary: Returns a list of assistant files.
      description: Returns a list of assistant files.
      operationId: List_Assistant_Files
      parameters:
        - name: assistant_id
          in: path
          description: The ID of the assistant the file belongs to.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
        - name: limit
          in: query
          description:
            "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is
            20.\n"
          schema:
            type: integer
            default: 20
        - name: order
          in: query
          description:
            "Sort order by the `created_at` timestamp of the objects. `asc` for ascending order and `desc` for
            descending order.\n"
          schema:
            enum:
              - asc
              - desc
            type: string
            default: desc
            x-ms-enum:
              name: ListAssistantFilesOrder
              modelAsString: true
              values:
                - value: asc
                  description: Order results in ascending order
                - value: desc
                  description: Order results in descending order
        - name: after
          in: query
          description:
            "A cursor for use in pagination. `after` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            after=obj_foo in order to fetch the next page of the list.\n"
          schema:
            type: string
        - name: before
          in: query
          description:
            "A cursor for use in pagination. `before` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            before=obj_foo in order to fetch the previous page of the list.\n"
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listAssistantFilesResponse'
              example:
                object: list
                data:
                  - id: string
                    object: assistant.file
                    created_at: 0
                    assistant_id: string
                first_id: string
                last_id: string
                has_more: false
    post:
      tags:
        - Assistants
      summary:
        'Create an assistant file by attaching a [File](/docs/api-reference/files) to an
        [assistant](/docs/api-reference/assistants).'
      description:
        'Create an assistant file by attaching a [File](/docs/api-reference/files) to an
        [assistant](/docs/api-reference/assistants).'
      operationId: Create_Assistant_File
      parameters:
        - name: assistant_id
          in: path
          description: "The ID of the assistant for which to create a File.\n"
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createAssistantFileRequest'
            example:
              file_id: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/assistantFileObject'
              example:
                id: string
                object: assistant.file
                created_at: 0
                assistant_id: string
  '/assistants/{assistant_id}/files/{file_id}':
    get:
      tags:
        - Assistants
      summary: Retrieves an AssistantFile.
      description: Retrieves an AssistantFile.
      operationId: Get_Assistant_File
      parameters:
        - name: assistant_id
          in: path
          description: The ID of the assistant who the file belongs to.
          required: true
          schema:
            type: string
        - name: file_id
          in: path
          description: The ID of the file we're getting.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/assistantFileObject'
              example:
                id: string
                object: assistant.file
                created_at: 0
                assistant_id: string
    delete:
      tags:
        - Assistants
      summary: Delete an assistant file.
      description: Delete an assistant file.
      operationId: Delete_Assistant_File
      parameters:
        - name: assistant_id
          in: path
          description: The ID of the assistant that the file belongs to.
          required: true
          schema:
            type: string
        - name: file_id
          in: path
          description: The ID of the file to delete.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/deleteAssistantFileResponse'
              example:
                id: string
                deleted: true
                object: assistant.file.deleted
  '/threads/{thread_id}/messages/{message_id}/files':
    get:
      tags:
        - Assistants
      summary: Returns a list of message files.
      description: Returns a list of message files.
      operationId: List_Message_Files
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread that the message and files belong to.
          required: true
          schema:
            type: string
        - name: message_id
          in: path
          description: The ID of the message that the files belongs to.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
        - name: limit
          in: query
          description:
            "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is
            20.\n"
          schema:
            type: integer
            default: 20
        - name: order
          in: query
          description:
            "Sort order by the `created_at` timestamp of the objects. `asc` for ascending order and `desc` for
            descending order.\n"
          schema:
            enum:
              - asc
              - desc
            type: string
            default: desc
            x-ms-enum:
              name: ListMessageFilesOrder
              modelAsString: true
              values:
                - value: asc
                  description: Order results in ascending order
                - value: desc
                  description: Order results in descending order
        - name: after
          in: query
          description:
            "A cursor for use in pagination. `after` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            after=obj_foo in order to fetch the next page of the list.\n"
          schema:
            type: string
        - name: before
          in: query
          description:
            "A cursor for use in pagination. `before` is an object ID that defines your place in the list. For instance,
            if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include
            before=obj_foo in order to fetch the previous page of the list.\n"
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listMessageFilesResponse'
              example:
                object: list
                data:
                  - id: string
                    object: thread.message.file
                    created_at: 0
                    message_id: string
                first_id: string
                last_id: string
                has_more: false
  '/threads/{thread_id}/messages/{message_id}/files/{file_id}':
    get:
      tags:
        - Assistants
      summary: Retrieves a message file.
      description: Retrieves a message file.
      operationId: Get_Message_File
      parameters:
        - name: thread_id
          in: path
          description: The ID of the thread to which the message and File belong.
          required: true
          schema:
            type: string
        - name: message_id
          in: path
          description: The ID of the message the file belongs to.
          required: true
          schema:
            type: string
        - name: file_id
          in: path
          description: The ID of the file being retrieved.
          required: true
          schema:
            type: string
        - name: api-version
          in: query
          required: true
          schema:
            type: string
            description: api version
            example: 2024-03-01-preview
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/messageFileObject'
              example:
                id: string
                object: thread.message.file
                created_at: 0
                message_id: string
components:
  schemas:
    errorResponse:
      type: object
      properties:
        error:
          $ref: '#/components/schemas/error'
    errorBase:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
    error:
      type: object
      allOf:
        - $ref: '#/components/schemas/errorBase'
      properties:
        param:
          type: string
        type:
          type: string
        inner_error:
          $ref: '#/components/schemas/innerError'
    innerError:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/innerErrorCode'
        content_filter_results:
          $ref: '#/components/schemas/contentFilterPromptResults'
      description: Inner error with additional details.
    innerErrorCode:
      enum:
        - ResponsibleAIPolicyViolation
      type: string
      description: Error codes for the inner error object.
      x-ms-enum:
        name: InnerErrorCode
        modelAsString: true
        values:
          - value: ResponsibleAIPolicyViolation
            description: The prompt violated one of more content filter rules.
    dalleErrorResponse:
      type: object
      properties:
        error:
          $ref: '#/components/schemas/dalleError'
    dalleError:
      type: object
      allOf:
        - $ref: '#/components/schemas/errorBase'
      properties:
        param:
          type: string
        type:
          type: string
        inner_error:
          $ref: '#/components/schemas/dalleInnerError'
    dalleInnerError:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/innerErrorCode'
        content_filter_results:
          $ref: '#/components/schemas/dalleFilterResults'
        revised_prompt:
          type: string
          description: 'The prompt that was used to generate the image, if there was any revision to the prompt.'
      description: Inner error with additional details.
    contentFilterResultBase:
      required:
        - filtered
      type: object
      properties:
        filtered:
          type: boolean
    contentFilterSeverityResult:
      required:
        - severity
        - filtered
      type: object
      allOf:
        - $ref: '#/components/schemas/contentFilterResultBase'
        - properties:
            severity:
              enum:
                - safe
                - low
                - medium
                - high
              type: string
              x-ms-enum:
                name: ContentFilterSeverity
                modelAsString: true
                values:
                  - value: safe
                    description: General content or related content in generic or non-harmful contexts.
                  - value: low
                    description: Harmful content at a low intensity and risk level.
                  - value: medium
                    description: Harmful content at a medium intensity and risk level.
                  - value: high
                    description: Harmful content at a high intensity and risk level.
    contentFilterDetectedResult:
      required:
        - detected
        - filtered
      type: object
      allOf:
        - $ref: '#/components/schemas/contentFilterResultBase'
        - properties:
            detected:
              type: boolean
    contentFilterDetectedWithCitationResult:
      required:
        - detected
        - filtered
      type: object
      allOf:
        - $ref: '#/components/schemas/contentFilterDetectedResult'
        - properties:
            citation:
              type: object
              properties:
                URL:
                  type: string
                license:
                  type: string
    contentFilterIdResult:
      required:
        - id
        - filtered
      type: object
      allOf:
        - $ref: '#/components/schemas/contentFilterResultBase'
        - properties:
            id:
              type: string
    contentFilterResultsBase:
      type: object
      properties:
        sexual:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        violence:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        hate:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        self_harm:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        profanity:
          $ref: '#/components/schemas/contentFilterDetectedResult'
        custom_blocklists:
          type: array
          items:
            $ref: '#/components/schemas/contentFilterIdResult'
        error:
          $ref: '#/components/schemas/errorBase'
      description: Information about the content filtering results.
    contentFilterPromptResults:
      type: object
      allOf:
        - $ref: '#/components/schemas/contentFilterResultsBase'
        - properties:
            jailbreak:
              $ref: '#/components/schemas/contentFilterDetectedResult'
      description:
        'Information about the content filtering category (hate, sexual, violence, self_harm), if it has been detected,
        as well as the severity level (very_low, low, medium, high-scale that determines the intensity and risk level of
        harmful content) and if it has been filtered or not. Information about jailbreak content and profanity, if it
        has been detected, and if it has been filtered or not. And information about customer block list, if it has been
        filtered and its id.'
    contentFilterChoiceResults:
      type: object
      allOf:
        - $ref: '#/components/schemas/contentFilterResultsBase'
        - properties:
            protected_material_text:
              $ref: '#/components/schemas/contentFilterDetectedResult'
        - properties:
            protected_material_code:
              $ref: '#/components/schemas/contentFilterDetectedWithCitationResult'
      description:
        'Information about the content filtering category (hate, sexual, violence, self_harm), if it has been detected,
        as well as the severity level (very_low, low, medium, high-scale that determines the intensity and risk level of
        harmful content) and if it has been filtered or not. Information about third party text and profanity, if it has
        been detected, and if it has been filtered or not. And information about customer block list, if it has been
        filtered and its id.'
    promptFilterResult:
      type: object
      properties:
        prompt_index:
          type: integer
        content_filter_results:
          $ref: '#/components/schemas/contentFilterPromptResults'
      description: Content filtering results for a single prompt in the request.
    promptFilterResults:
      type: array
      items:
        $ref: '#/components/schemas/promptFilterResult'
      description:
        'Content filtering results for zero or more prompts in the request. In a streaming request, results for
        different prompts may arrive at different times or in different orders.'
    dalleContentFilterResults:
      type: object
      properties:
        sexual:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        violence:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        hate:
          $ref: '#/components/schemas/contentFilterSeverityResult'
        self_harm:
          $ref: '#/components/schemas/contentFilterSeverityResult'
      description: Information about the content filtering results.
    dalleFilterResults:
      type: object
      allOf:
        - $ref: '#/components/schemas/dalleContentFilterResults'
        - properties:
            profanity:
              $ref: '#/components/schemas/contentFilterDetectedResult'
            jailbreak:
              $ref: '#/components/schemas/contentFilterDetectedResult'
      description:
        'Information about the content filtering category (hate, sexual, violence, self_harm), if it has been detected,
        as well as the severity level (very_low, low, medium, high-scale that determines the intensity and risk level of
        harmful content) and if it has been filtered or not. Information about jailbreak content and profanity, if it
        has been detected, and if it has been filtered or not. And information about customer block list, if it has been
        filtered and its id.'
    chatCompletionsRequestCommon:
      type: object
      properties:
        temperature:
          maximum: 2.0
          minimum: 0.0
          type: number
          description:
            "What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random,
            while lower values like 0.2 will make it more focused and deterministic.\nWe generally recommend altering
            this or `top_p` but not both."
          default: 1
          nullable: true
          example: 1
        top_p:
          maximum: 1.0
          minimum: 0.0
          type: number
          description:
            "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results
            of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability
            mass are considered.\nWe generally recommend altering this or `temperature` but not both."
          default: 1
          nullable: true
          example: 1
        stream:
          type: boolean
          description:
            'If set, partial message deltas will be sent, like in ChatGPT. Tokens will be sent as data-only server-sent
            events as they become available, with the stream terminated by a `data: [DONE]` message.'
          default: false
          nullable: true
        stop:
          oneOf:
            - type: string
              nullable: true
            - maxItems: 4
              minItems: 1
              type: array
              items:
                type: string
              description: Array minimum size of 1 and maximum of 4
          description: Up to 4 sequences where the API will stop generating further tokens.
          default:
        max_tokens:
          type: integer
          description:
            'The maximum number of tokens allowed for the generated answer. By default, the number of tokens the model
            can return will be (4096 - prompt tokens).'
          default: 4096
        presence_penalty:
          maximum: 2.0
          minimum: -2.0
          type: number
          description:
            "Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text
            so far, increasing the model's likelihood to talk about new topics."
          default: 0
        frequency_penalty:
          maximum: 2.0
          minimum: -2.0
          type: number
          description:
            "Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the
            text so far, decreasing the model's likelihood to repeat the same line verbatim."
          default: 0
        logit_bias:
          type: object
          description:
            'Modify the likelihood of specified tokens appearing in the completion. Accepts a json object that maps
            tokens (specified by their token ID in the tokenizer) to an associated bias value from -100 to 100.
            Mathematically, the bias is added to the logits generated by the model prior to sampling. The exact effect
            will vary per model, but values between -1 and 1 should decrease or increase likelihood of selection; values
            like -100 or 100 should result in a ban or exclusive selection of the relevant token.'
          nullable: true
        user:
          type: string
          description:
            'A unique identifier representing your end-user, which can help Azure OpenAI to monitor and detect abuse.'
          example: user-1234
    createChatCompletionRequest:
      required:
        - messages
      type: object
      allOf:
        - $ref: '#/components/schemas/chatCompletionsRequestCommon'
        - properties:
            messages:
              minItems: 1
              type: array
              items:
                $ref: '#/components/schemas/chatCompletionRequestMessage'
              description:
                'A list of messages comprising the conversation so far. [Example Python
                code](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_format_inputs_to_ChatGPT_models.ipynb).'
            data_sources:
              type: array
              items:
                $ref: '#/components/schemas/azureChatExtensionConfiguration'
              description:
                "  The configuration entries for Azure OpenAI chat extensions that use them.\n  This additional
                specification is only compatible with Azure OpenAI."
            enhancements:
              type: object
              properties:
                grounding:
                  type: object
                  properties:
                    enabled:
                      type: boolean
                      default: false
                  description: Request object to specify if grounding enhancement is needed.
                ocr:
                  type: object
                  properties:
                    enabled:
                      type: boolean
                      default: false
                  description: Request object to specify if ocr enhancement is needed.
              description: The type of enhancements needed.
            n:
              maximum: 128.0
              minimum: 1.0
              type: integer
              description: How many chat completion choices to generate for each input message.
              default: 1
              nullable: true
              example: 1
            seed:
              maximum: 9223372036854775800
              minimum: -9223372036854775800
              type: integer
              description:
                'If specified, our system will make a best effort to sample deterministically, such that repeated
                requests with the same `seed` and parameters should return the same result.Determinism is not
                guaranteed, and you should refer to the `system_fingerprint` response parameter to monitor changes in
                the backend.'
              default: 0
              nullable: true
              example: 1
            logprobs:
              type: boolean
              description:
                'Whether to return log probabilities of the output tokens or not. If true, returns the log probabilities
                of each output token returned in the `content` of `message`. This option is currently not available on
                the `gpt-4-vision-preview` model.'
              default: false
              nullable: true
            top_logprobs:
              maximum: 5.0
              minimum: 0.0
              type: integer
              description:
                'An integer between 0 and 5 specifying the number of most likely tokens to return at each token
                position, each with an associated log probability. `logprobs` must be set to `true` if this parameter is
                used.'
              nullable: true
            response_format:
              type: object
              properties:
                type:
                  $ref: '#/components/schemas/chatCompletionResponseFormat'
              description: An object specifying the format that the model must output. Used to enable JSON mode.
            tools:
              minItems: 1
              type: array
              items:
                $ref: '#/components/schemas/chatCompletionTool'
              description:
                'A list of tools the model may call. Currently, only functions are supported as a tool. Use this to
                provide a list of functions the model may generate JSON inputs for.'
            tool_choice:
              $ref: '#/components/schemas/chatCompletionToolChoiceOption'
            functions:
              maxItems: 128
              minItems: 1
              type: array
              items:
                $ref: '#/components/schemas/chatCompletionFunction'
              description: Deprecated in favor of `tools`. A list of functions the model may generate JSON inputs for.
            function_call:
              oneOf:
                - enum:
                    - none
                    - auto
                  type: string
                  description:
                    '`none` means the model will not call a function and instead generates a message. `auto` means the
                    model can pick between generating a message or calling a function.'
                - required:
                    - name
                  type: object
                  properties:
                    name:
                      type: string
                      description: The name of the function to call.
                  description:
                    'Specifying a particular function via `{"name": "my_function"}` forces the model to call that
                    function.'
              description:
                'Deprecated in favor of `tool_choice`. Controls how the model responds to function calls. "none" means
                the model does not call a function, and responds to the end-user. "auto" means the model can pick
                between an end-user or calling a function.  Specifying a particular function via `{"name":\
                "my_function"}` forces the model to call that function. "none" is the default when no functions are
                present. "auto" is the default if functions are present.'
    chatCompletionResponseFormat:
      enum:
        - text
        - json_object
      type: string
      description:
        Setting to `json_object` enables JSON mode. This guarantees that the message the model generates is valid JSON.
      default: text
      nullable: true
      example: json_object
      x-ms-enum:
        name: ChatCompletionResponseFormat
        modelAsString: true
        values:
          - value: text
            description: Response format is a plain text string.
          - value: json_object
            description: Response format is a JSON object.
    chatCompletionFunction:
      required:
        - name
      type: object
      properties:
        name:
          type: string
          description:
            'The name of the function to be called. Must be a-z, A-Z, 0-9, or contain underscores and dashes, with a
            maximum length of 64.'
        description:
          type: string
          description: The description of what the function does.
        parameters:
          $ref: '#/components/schemas/chatCompletionFunctionParameters'
    chatCompletionFunctionParameters:
      type: object
      description:
        'The parameters the functions accepts, described as a JSON Schema object. See the
        [guide](/docs/guides/gpt/function-calling) for examples, and the [JSON Schema
        reference](https://json-schema.org/understanding-json-schema/) for documentation about the format.'
    chatCompletionRequestMessage:
      required:
        - role
      type: object
      properties:
        role:
          $ref: '#/components/schemas/chatCompletionRequestMessageRole'
      discriminator:
        propertyName: role
        mapping:
          system: '#/components/schemas/chatCompletionRequestMessageSystem'
          user: '#/components/schemas/chatCompletionRequestMessageUser'
          assistant: '#/components/schemas/chatCompletionRequestMessageAssistant'
          tool: '#/components/schemas/chatCompletionRequestMessageTool'
          function: '#/components/schemas/chatCompletionRequestMessageFunction'
    chatCompletionRequestMessageRole:
      enum:
        - system
        - user
        - assistant
        - tool
        - function
      type: string
      description: The role of the messages author.
      x-ms-enum:
        name: ChatCompletionRequestMessageRole
        modelAsString: true
        values:
          - value: system
            description: The message author role is system.
          - value: user
            description: The message author role is user.
          - value: assistant
            description: The message author role is assistant.
          - value: tool
            description: The message author role is tool.
          - value: function
            description: Deprecated. The message author role is function.
    chatCompletionRequestMessageSystem:
      required:
        - content
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessage'
        - type: object
          properties:
            content:
              type: string
              description: The contents of the message.
              nullable: true
    chatCompletionRequestMessageUser:
      required:
        - content
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessage'
        - type: object
          properties:
            content:
              oneOf:
                - type: string
                  description: The contents of the message.
                - minimum: 1.0
                  type: array
                  items:
                    $ref: '#/components/schemas/chatCompletionRequestMessageContentPart'
                  description:
                    'An array of content parts with a defined type, each can be of type `text` or `image_url` when
                    passing in images. You can pass multiple images by adding multiple `image_url` content parts. Image
                    input is only supported when using the `gpt-4-visual-preview` model.'
              nullable: true
    chatCompletionRequestMessageContentPart:
      required:
        - type
      type: object
      properties:
        type:
          $ref: '#/components/schemas/chatCompletionRequestMessageContentPartType'
      discriminator:
        propertyName: type
        mapping:
          text: '#/components/schemas/chatCompletionRequestMessageContentPartText'
          image_url: '#/components/schemas/chatCompletionRequestMessageContentPartImage'
    chatCompletionRequestMessageContentPartType:
      enum:
        - text
        - image_url
      type: string
      description: The type of the content part.
      x-ms-enum:
        name: ChatCompletionRequestMessageContentPartType
        modelAsString: true
        values:
          - value: text
            description: The content part type is text.
          - value: image_url
            description: The content part type is image_url.
    chatCompletionRequestMessageContentPartText:
      required:
        - text
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessageContentPart'
        - type: object
          properties:
            text:
              type: string
              description: The text content.
    chatCompletionRequestMessageContentPartImage:
      required:
        - url
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessageContentPart'
        - type: object
          properties:
            url:
              type: string
              description: Either a URL of the image or the base64 encoded image data.
              format: uri
            detail:
              $ref: '#/components/schemas/imageDetailLevel'
    imageDetailLevel:
      enum:
        - auto
        - low
        - high
      type: string
      description: Specifies the detail level of the image.
      default: auto
      x-ms-enum:
        name: ImageDetailLevel
        modelAsString: true
        values:
          - value: auto
            description: The image detail level is auto.
          - value: low
            description: The image detail level is low.
          - value: high
            description: The image detail level is high.
    chatCompletionRequestMessageAssistant:
      required:
        - content
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessage'
        - type: object
          properties:
            content:
              type: string
              description: The contents of the message.
              nullable: true
            tool_calls:
              type: array
              items:
                $ref: '#/components/schemas/chatCompletionMessageToolCall'
              description: 'The tool calls generated by the model, such as function calls.'
            context:
              $ref: '#/components/schemas/azureChatExtensionsMessageContext'
    azureChatExtensionConfiguration:
      required:
        - type
      type: object
      properties:
        type:
          $ref: '#/components/schemas/azureChatExtensionType'
      description:
        "  A representation of configuration data for a single Azure OpenAI chat extension. This will be used by a
        chat\n  completions request that should use Azure OpenAI chat extensions to augment the response
        behavior.\n  The use of this configuration is compatible only with Azure OpenAI."
      discriminator:
        propertyName: type
        mapping:
          azure_search: '#/components/schemas/azureSearchChatExtensionConfiguration'
          azure_ml_index: '#/components/schemas/azureMachineLearningIndexChatExtensionConfiguration'
          azure_cosmos_db: '#/components/schemas/azureCosmosDBChatExtensionConfiguration'
          elasticsearch: '#/components/schemas/elasticsearchChatExtensionConfiguration'
          pinecone: '#/components/schemas/pineconeChatExtensionConfiguration'
    azureChatExtensionType:
      enum:
        - azure_search
        - azure_ml_index
        - azure_cosmos_db
        - elasticsearch
        - pinecone
      type: string
      description:
        "  A representation of configuration data for a single Azure OpenAI chat extension. This will be used by a
        chat\n  completions request that should use Azure OpenAI chat extensions to augment the response
        behavior.\n  The use of this configuration is compatible only with Azure OpenAI."
      x-ms-enum:
        name: AzureChatExtensionType
        modelAsString: true
        values:
          - name: azureSearch
            value: azure_search
            description: Represents the use of Azure Search as an Azure OpenAI chat extension.
          - name: azureMachineLearningIndex
            value: azure_ml_index
            description: Represents the use of Azure Machine Learning index as an Azure OpenAI chat extension.
          - name: azureCosmosDB
            value: azure_cosmos_db
            description: Represents the use of Azure Cosmos DB as an Azure OpenAI chat extension.
          - name: elasticsearch
            value: elasticsearch
            description: Represents the use of Elasticsearch® index as an Azure OpenAI chat extension.
          - name: pinecone
            value: pinecone
            description: Represents the use of Pinecone index as an Azure OpenAI chat extension.
    azureSearchChatExtensionConfiguration:
      required:
        - parameters
      allOf:
        - $ref: '#/components/schemas/azureChatExtensionConfiguration'
        - properties:
            parameters:
              $ref: '#/components/schemas/azureSearchChatExtensionParameters'
      description:
        "A specific representation of configurable options for Azure Search when using it as an Azure OpenAI
        chat\nextension."
      x-ms-discriminator-value: azure_search
    azureSearchChatExtensionParameters:
      required:
        - authentication
        - endpoint
        - index_name
      type: object
      properties:
        authentication:
          oneOf:
            - $ref: '#/components/schemas/onYourDataApiKeyAuthenticationOptions'
            - $ref: '#/components/schemas/onYourDataSystemAssignedManagedIdentityAuthenticationOptions'
            - $ref: '#/components/schemas/onYourDataUserAssignedManagedIdentityAuthenticationOptions'
        top_n_documents:
          type: integer
          description: The configured top number of documents to feature for the configured query.
          format: int32
        in_scope:
          type: boolean
          description: Whether queries should be restricted to use of indexed data.
        strictness:
          maximum: 5.0
          minimum: 1.0
          type: integer
          description:
            'The configured strictness of the search relevance filtering. The higher of strictness, the higher of the
            precision but lower recall of the answer.'
          format: int32
        role_information:
          type: string
          description:
            "Give the model instructions about how it should behave and any context it should reference when generating
            a response. You can describe the assistant's personality and tell it how to format responses. There's a 100
            token limit for it, and it counts against the overall token limit."
        endpoint:
          type: string
          description: The absolute endpoint path for the Azure Search resource to use.
          format: uri
        index_name:
          type: string
          description: The name of the index to use as available in the referenced Azure Search resource.
        fields_mapping:
          $ref: '#/components/schemas/azureSearchIndexFieldMappingOptions'
        query_type:
          $ref: '#/components/schemas/azureSearchQueryType'
        semantic_configuration:
          type: string
          description: The additional semantic configuration for the query.
        filter:
          type: string
          description: Search filter.
        embedding_dependency:
          oneOf:
            - $ref: '#/components/schemas/onYourDataEndpointVectorizationSource'
            - $ref: '#/components/schemas/onYourDataDeploymentNameVectorizationSource'
      description: Parameters for Azure Search when used as an Azure OpenAI chat extension.
    azureSearchIndexFieldMappingOptions:
      type: object
      properties:
        title_field:
          type: string
          description: The name of the index field to use as a title.
        url_field:
          type: string
          description: The name of the index field to use as a URL.
        filepath_field:
          type: string
          description: The name of the index field to use as a filepath.
        content_fields:
          type: array
          items:
            type: string
          description: The names of index fields that should be treated as content.
        content_fields_separator:
          type: string
          description: The separator pattern that content fields should use.
        vector_fields:
          type: array
          items:
            type: string
          description: The names of fields that represent vector data.
        image_vector_fields:
          type: array
          items:
            type: string
          description: The names of fields that represent image vector data.
      description: Optional settings to control how fields are processed when using a configured Azure Search resource.
    azureSearchQueryType:
      enum:
        - simple
        - semantic
        - vector
        - vector_simple_hybrid
        - vector_semantic_hybrid
      type: string
      description:
        The type of Azure Search retrieval query that should be executed when using it as an Azure OpenAI chat
        extension.
      x-ms-enum:
        name: AzureSearchQueryType
        modelAsString: true
        values:
          - name: simple
            value: simple
            description: 'Represents the default, simple query parser.'
          - name: semantic
            value: semantic
            description: Represents the semantic query parser for advanced semantic modeling.
          - name: vector
            value: vector
            description: Represents vector search over computed data.
          - name: vectorSimpleHybrid
            value: vector_simple_hybrid
            description: Represents a combination of the simple query strategy with vector data.
          - name: vectorSemanticHybrid
            value: vector_semantic_hybrid
            description: Represents a combination of semantic search and vector data querying.
    azureMachineLearningIndexChatExtensionConfiguration:
      required:
        - parameters
      allOf:
        - $ref: '#/components/schemas/azureChatExtensionConfiguration'
        - properties:
            parameters:
              $ref: '#/components/schemas/azureMachineLearningIndexChatExtensionParameters'
      description:
        "A specific representation of configurable options for Azure Machine Learning vector index when using it as an
        Azure\nOpenAI chat extension."
      x-ms-discriminator-value: azure_ml_index
    azureMachineLearningIndexChatExtensionParameters:
      required:
        - authentication
        - name
        - project_resource_id
        - version
      type: object
      properties:
        authentication:
          oneOf:
            - $ref: '#/components/schemas/onYourDataAccessTokenAuthenticationOptions'
            - $ref: '#/components/schemas/onYourDataSystemAssignedManagedIdentityAuthenticationOptions'
            - $ref: '#/components/schemas/onYourDataUserAssignedManagedIdentityAuthenticationOptions'
        top_n_documents:
          type: integer
          description: The configured top number of documents to feature for the configured query.
          format: int32
        in_scope:
          type: boolean
          description: Whether queries should be restricted to use of indexed data.
        strictness:
          maximum: 5.0
          minimum: 1.0
          type: integer
          description:
            'The configured strictness of the search relevance filtering. The higher of strictness, the higher of the
            precision but lower recall of the answer.'
          format: int32
        role_information:
          type: string
          description:
            "Give the model instructions about how it should behave and any context it should reference when generating
            a response. You can describe the assistant's personality and tell it how to format responses. There's a 100
            token limit for it, and it counts against the overall token limit."
        project_resource_id:
          type: string
          description: The resource ID of the Azure Machine Learning project.
        name:
          type: string
          description: The Azure Machine Learning vector index name.
        version:
          type: string
          description: The version of the Azure Machine Learning vector index.
        filter:
          type: string
          description: Search filter. Only supported if the Azure Machine Learning vector index is of type AzureSearch.
      description: Parameters for the Azure Machine Learning vector index chat extension.
    azureCosmosDBChatExtensionConfiguration:
      required:
        - parameters
      allOf:
        - $ref: '#/components/schemas/azureChatExtensionConfiguration'
        - properties:
            parameters:
              $ref: '#/components/schemas/azureCosmosDBChatExtensionParameters'
      description:
        "A specific representation of configurable options for Azure Cosmos DB when using it as an Azure OpenAI
        chat\nextension."
      x-ms-discriminator-value: azure_cosmos_db
    azureCosmosDBChatExtensionParameters:
      required:
        - authentication
        - container_name
        - database_name
        - embedding_dependency
        - fields_mapping
        - index_name
      type: object
      properties:
        authentication:
          $ref: '#/components/schemas/onYourDataConnectionStringAuthenticationOptions'
        top_n_documents:
          type: integer
          description: The configured top number of documents to feature for the configured query.
          format: int32
        in_scope:
          type: boolean
          description: Whether queries should be restricted to use of indexed data.
        strictness:
          maximum: 5.0
          minimum: 1.0
          type: integer
          description:
            'The configured strictness of the search relevance filtering. The higher of strictness, the higher of the
            precision but lower recall of the answer.'
          format: int32
        role_information:
          type: string
          description:
            "Give the model instructions about how it should behave and any context it should reference when generating
            a response. You can describe the assistant's personality and tell it how to format responses. There's a 100
            token limit for it, and it counts against the overall token limit."
        database_name:
          type: string
          description: The MongoDB vCore database name to use with Azure Cosmos DB.
        container_name:
          type: string
          description: The name of the Azure Cosmos DB resource container.
        index_name:
          type: string
          description: The MongoDB vCore index name to use with Azure Cosmos DB.
        fields_mapping:
          $ref: '#/components/schemas/azureCosmosDBFieldMappingOptions'
        embedding_dependency:
          oneOf:
            - $ref: '#/components/schemas/onYourDataEndpointVectorizationSource'
            - $ref: '#/components/schemas/onYourDataDeploymentNameVectorizationSource'
      description:
        "Parameters to use when configuring Azure OpenAI On Your Data chat extensions when using Azure Cosmos DB
        for\nMongoDB vCore."
    azureCosmosDBFieldMappingOptions:
      required:
        - content_fields
        - vector_fields
      type: object
      properties:
        title_field:
          type: string
          description: The name of the index field to use as a title.
        url_field:
          type: string
          description: The name of the index field to use as a URL.
        filepath_field:
          type: string
          description: The name of the index field to use as a filepath.
        content_fields:
          type: array
          items:
            type: string
          description: The names of index fields that should be treated as content.
        content_fields_separator:
          type: string
          description: The separator pattern that content fields should use.
        vector_fields:
          type: array
          items:
            type: string
          description: The names of fields that represent vector data.
      description:
        Optional settings to control how fields are processed when using a configured Azure Cosmos DB resource.
    elasticsearchChatExtensionConfiguration:
      required:
        - parameters
      allOf:
        - $ref: '#/components/schemas/azureChatExtensionConfiguration'
        - properties:
            parameters:
              $ref: '#/components/schemas/elasticsearchChatExtensionParameters'
      description:
        "A specific representation of configurable options for Elasticsearch when using it as an Azure OpenAI
        chat\nextension."
      x-ms-discriminator-value: elasticsearch
    elasticsearchChatExtensionParameters:
      required:
        - authentication
        - endpoint
        - index_name
      type: object
      properties:
        authentication:
          oneOf:
            - $ref: '#/components/schemas/onYourDataKeyAndKeyIdAuthenticationOptions'
            - $ref: '#/components/schemas/onYourDataEncodedApiKeyAuthenticationOptions'
        top_n_documents:
          type: integer
          description: The configured top number of documents to feature for the configured query.
          format: int32
        in_scope:
          type: boolean
          description: Whether queries should be restricted to use of indexed data.
        strictness:
          maximum: 5.0
          minimum: 1.0
          type: integer
          description:
            'The configured strictness of the search relevance filtering. The higher of strictness, the higher of the
            precision but lower recall of the answer.'
          format: int32
        role_information:
          type: string
          description:
            "Give the model instructions about how it should behave and any context it should reference when generating
            a response. You can describe the assistant's personality and tell it how to format responses. There's a 100
            token limit for it, and it counts against the overall token limit."
        endpoint:
          type: string
          description: The endpoint of Elasticsearch®.
          format: uri
        index_name:
          type: string
          description: The index name of Elasticsearch®.
        fields_mapping:
          $ref: '#/components/schemas/elasticsearchIndexFieldMappingOptions'
        query_type:
          $ref: '#/components/schemas/elasticsearchQueryType'
        embedding_dependency:
          oneOf:
            - $ref: '#/components/schemas/onYourDataEndpointVectorizationSource'
            - $ref: '#/components/schemas/onYourDataDeploymentNameVectorizationSource'
            - $ref: '#/components/schemas/onYourDataModelIdVectorizationSource'
      description: 'Parameters to use when configuring Elasticsearch® as an Azure OpenAI chat extension. '
    elasticsearchIndexFieldMappingOptions:
      type: object
      properties:
        title_field:
          type: string
          description: The name of the index field to use as a title.
        url_field:
          type: string
          description: The name of the index field to use as a URL.
        filepath_field:
          type: string
          description: The name of the index field to use as a filepath.
        content_fields:
          type: array
          items:
            type: string
          description: The names of index fields that should be treated as content.
        content_fields_separator:
          type: string
          description: The separator pattern that content fields should use.
        vector_fields:
          type: array
          items:
            type: string
          description: The names of fields that represent vector data.
      description:
        Optional settings to control how fields are processed when using a configured Elasticsearch® resource.
    elasticsearchQueryType:
      enum:
        - simple
        - vector
      type: string
      description:
        The type of Elasticsearch® retrieval query that should be executed when using it as an Azure OpenAI chat
        extension.
      x-ms-enum:
        name: ElasticsearchQueryType
        modelAsString: true
        values:
          - name: simple
            value: simple
            description: 'Represents the default, simple query parser.'
          - name: vector
            value: vector
            description: Represents vector search over computed data.
    pineconeChatExtensionConfiguration:
      required:
        - parameters
      allOf:
        - $ref: '#/components/schemas/azureChatExtensionConfiguration'
        - properties:
            parameters:
              $ref: '#/components/schemas/pineconeChatExtensionParameters'
      description:
        "A specific representation of configurable options for Pinecone when using it as an Azure OpenAI
        chat\nextension."
      x-ms-discriminator-value: pinecone
    pineconeChatExtensionParameters:
      required:
        - authentication
        - embedding_dependency
        - environment
        - fields_mapping
        - index_name
      type: object
      properties:
        authentication:
          $ref: '#/components/schemas/onYourDataApiKeyAuthenticationOptions'
        top_n_documents:
          type: integer
          description: The configured top number of documents to feature for the configured query.
          format: int32
        in_scope:
          type: boolean
          description: Whether queries should be restricted to use of indexed data.
        strictness:
          maximum: 5.0
          minimum: 1.0
          type: integer
          description:
            'The configured strictness of the search relevance filtering. The higher of strictness, the higher of the
            precision but lower recall of the answer.'
          format: int32
        role_information:
          type: string
          description:
            "Give the model instructions about how it should behave and any context it should reference when generating
            a response. You can describe the assistant's personality and tell it how to format responses. There's a 100
            token limit for it, and it counts against the overall token limit."
        environment:
          type: string
          description: The environment name of Pinecone.
        index_name:
          type: string
          description: The name of the Pinecone database index.
        fields_mapping:
          $ref: '#/components/schemas/pineconeFieldMappingOptions'
        embedding_dependency:
          $ref: '#/components/schemas/onYourDataDeploymentNameVectorizationSource'
      description: Parameters for configuring Azure OpenAI Pinecone chat extensions.
    pineconeFieldMappingOptions:
      required:
        - content_fields
      type: object
      properties:
        title_field:
          type: string
          description: The name of the index field to use as a title.
        url_field:
          type: string
          description: The name of the index field to use as a URL.
        filepath_field:
          type: string
          description: The name of the index field to use as a filepath.
        content_fields:
          type: array
          items:
            type: string
          description: The names of index fields that should be treated as content.
        content_fields_separator:
          type: string
          description: The separator pattern that content fields should use.
      description: Optional settings to control how fields are processed when using a configured Pinecone resource.
    onYourDataAuthenticationOptions:
      required:
        - type
      type: object
      properties:
        type:
          $ref: '#/components/schemas/onYourDataAuthenticationType'
      description: The authentication options for Azure OpenAI On Your Data.
      discriminator:
        propertyName: type
        mapping:
          api_key: '#/components/schemas/onYourDataApiKeyAuthenticationOptions'
          connection_string: '#/components/schemas/onYourDataConnectionStringAuthenticationOptions'
          key_and_key_id: '#/components/schemas/onYourDataKeyAndKeyIdAuthenticationOptions'
          encoded_api_key: '#/components/schemas/onYourDataEncodedApiKeyAuthenticationOptions'
          access_token: '#/components/schemas/onYourDataAccessTokenAuthenticationOptions'
          system_assigned_managed_identity: '#/components/schemas/onYourDataSystemAssignedManagedIdentityAuthenticationOptions'
          user_assigned_managed_identity: '#/components/schemas/onYourDataUserAssignedManagedIdentityAuthenticationOptions'
    onYourDataAuthenticationType:
      enum:
        - api_key
        - connection_string
        - key_and_key_id
        - encoded_api_key
        - access_token
        - system_assigned_managed_identity
        - user_assigned_managed_identity
      type: string
      description: The authentication types supported with Azure OpenAI On Your Data.
      x-ms-enum:
        name: OnYourDataAuthenticationType
        modelAsString: true
        values:
          - name: apiKey
            value: api_key
            description: Authentication via API key.
          - name: connectionString
            value: connection_string
            description: Authentication via connection string.
          - name: keyAndKeyId
            value: key_and_key_id
            description: Authentication via key and key ID pair.
          - name: encodedApiKey
            value: encoded_api_key
            description: Authentication via encoded API key.
          - name: accessToken
            value: access_token
            description: Authentication via access token.
          - name: systemAssignedManagedIdentity
            value: system_assigned_managed_identity
            description: Authentication via system-assigned managed identity.
          - name: userAssignedManagedIdentity
            value: user_assigned_managed_identity
            description: Authentication via user-assigned managed identity.
    onYourDataApiKeyAuthenticationOptions:
      required:
        - key
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
        - properties:
            key:
              type: string
              description: The API key to use for authentication.
      description: The authentication options for Azure OpenAI On Your Data when using an API key.
      x-ms-discriminator-value: api_key
    onYourDataConnectionStringAuthenticationOptions:
      required:
        - connection_string
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
        - properties:
            connection_string:
              type: string
              description: The connection string to use for authentication.
      description: The authentication options for Azure OpenAI On Your Data when using a connection string.
      x-ms-discriminator-value: connection_string
    onYourDataKeyAndKeyIdAuthenticationOptions:
      required:
        - key
        - key_id
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
        - properties:
            key:
              type: string
              description: The Elasticsearch key to use for authentication.
            key_id:
              type: string
              description: The Elasticsearch key ID to use for authentication.
      description:
        The authentication options for Azure OpenAI On Your Data when using an Elasticsearch key and key ID pair.
      x-ms-discriminator-value: key_and_key_id
    onYourDataEncodedApiKeyAuthenticationOptions:
      required:
        - encoded_api_key
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
        - properties:
            encoded_api_key:
              type: string
              description: The Elasticsearch encoded API key to use for authentication.
      description: The authentication options for Azure OpenAI On Your Data when using an Elasticsearch encoded API key.
      x-ms-discriminator-value: encoded_api_key
    onYourDataAccessTokenAuthenticationOptions:
      required:
        - access_token
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
        - properties:
            access_token:
              type: string
              description: The access token to use for authentication.
      description: The authentication options for Azure OpenAI On Your Data when using access token.
      x-ms-discriminator-value: access_token
    onYourDataSystemAssignedManagedIdentityAuthenticationOptions:
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
      description:
        The authentication options for Azure OpenAI On Your Data when using a system-assigned managed identity.
      x-ms-discriminator-value: system_assigned_managed_identity
    onYourDataUserAssignedManagedIdentityAuthenticationOptions:
      required:
        - managed_identity_resource_id
      allOf:
        - $ref: '#/components/schemas/onYourDataAuthenticationOptions'
        - properties:
            managed_identity_resource_id:
              type: string
              description: The resource ID of the user-assigned managed identity to use for authentication.
      description: The authentication options for Azure OpenAI On Your Data when using a user-assigned managed identity.
      x-ms-discriminator-value: user_assigned_managed_identity
    onYourDataVectorizationSource:
      required:
        - type
      type: object
      properties:
        type:
          $ref: '#/components/schemas/onYourDataVectorizationSourceType'
      description:
        An abstract representation of a vectorization source for Azure OpenAI On Your Data with vector search.
      discriminator:
        propertyName: type
        mapping:
          endpoint: '#/components/schemas/onYourDataEndpointVectorizationSource'
          deployment_name: '#/components/schemas/onYourDataDeploymentNameVectorizationSource'
          model_id: '#/components/schemas/onYourDataModelIdVectorizationSource'
    onYourDataVectorizationSourceType:
      enum:
        - endpoint
        - deployment_name
        - model_id
      type: string
      description:
        "Represents the available sources Azure OpenAI On Your Data can use to configure vectorization of data for use
        with\nvector search."
      x-ms-enum:
        name: OnYourDataVectorizationSourceType
        modelAsString: true
        values:
          - name: endpoint
            value: endpoint
            description: Represents vectorization performed by public service calls to an Azure OpenAI embedding model.
          - name: deploymentName
            value: deployment_name
            description:
              "Represents an Ada model deployment name to use. This model deployment must be in the same Azure OpenAI
              resource, but\nOn Your Data will use this model deployment via an internal call rather than a public one,
              which enables vector\nsearch even in private networks."
          - name: modelId
            value: model_id
            description:
              "Represents a specific embedding model ID as defined in the search service.\nCurrently only supported by
              Elasticsearch®."
    onYourDataEndpointVectorizationSource:
      required:
        - authentication
        - endpoint
      allOf:
        - $ref: '#/components/schemas/onYourDataVectorizationSource'
        - properties:
            endpoint:
              type: string
              description:
                Specifies the resource endpoint URL from which embeddings should be retrieved. It should be in the
                format of
                https://YOUR_RESOURCE_NAME.openai.azure.com/openai/deployments/YOUR_DEPLOYMENT_NAME/embeddings. The
                api-version query parameter is not allowed.
              format: uri
            authentication:
              $ref: '#/components/schemas/onYourDataApiKeyAuthenticationOptions'
      description:
        "The details of a a vectorization source, used by Azure OpenAI On Your Data when applying vector search, that is
        based\non a public Azure OpenAI endpoint call for embeddings."
      x-ms-discriminator-value: endpoint
    onYourDataDeploymentNameVectorizationSource:
      required:
        - deployment_name
      allOf:
        - $ref: '#/components/schemas/onYourDataVectorizationSource'
        - properties:
            deployment_name:
              type: string
              description:
                'Specifies the name of the model deployment to use for vectorization. This model deployment must be in
                the same Azure OpenAI resource, but On Your Data will use this model deployment via an internal call
                rather than a public one, which enables vector search even in private networks.'
      description:
        "The details of a a vectorization source, used by Azure OpenAI On Your Data when applying vector search, that is
        based\non an internal embeddings model deployment name in the same Azure OpenAI resource."
      x-ms-discriminator-value: deployment_name
    onYourDataModelIdVectorizationSource:
      required:
        - model_id
      allOf:
        - $ref: '#/components/schemas/onYourDataVectorizationSource'
        - properties:
            model_id:
              type: string
              description:
                Specifies the model ID to use for vectorization. This model ID must be defined in the search service.
      description:
        "The details of a a vectorization source, used by Azure OpenAI On Your Data when applying vector search, that is
        based\non a search service model ID. Currently only supported by Elasticsearch®."
      x-ms-discriminator-value: model_id
    azureChatExtensionsMessageContext:
      type: object
      properties:
        citations:
          type: array
          items:
            $ref: '#/components/schemas/citation'
          description: 'The data source retrieval result, used to generate the assistant message in the response.'
          x-ms-identifiers: []
        intent:
          type: string
          description:
            'The detected intent from the chat history, used to pass to the next turn to carry over the context.'
      description:
        "  A representation of the additional context information available when Azure OpenAI chat extensions are
        involved\n  in the generation of a corresponding chat completions response. This context information is only
        populated when\n  using an Azure OpenAI request configured to use a matching extension."
    citation:
      required:
        - content
      type: object
      properties:
        content:
          type: string
          description: The content of the citation.
        title:
          type: string
          description: The title of the citation.
        url:
          type: string
          description: The URL of the citation.
        filepath:
          type: string
          description: The file path of the citation.
        chunk_id:
          type: string
          description: The chunk ID of the citation.
      description: citation information for a chat completions response message.
    chatCompletionMessageToolCall:
      required:
        - id
        - type
        - function
      type: object
      properties:
        id:
          type: string
          description: The ID of the tool call.
        type:
          $ref: '#/components/schemas/toolCallType'
        function:
          required:
            - name
            - arguments
          type: object
          properties:
            name:
              type: string
              description: The name of the function to call.
            arguments:
              type: string
              description:
                'The arguments to call the function with, as generated by the model in JSON format. Note that the model
                does not always generate valid JSON, and may hallucinate parameters not defined by your function schema.
                Validate the arguments in your code before calling your function.'
          description: The function that the model called.
    toolCallType:
      enum:
        - function
      type: string
      description: 'The type of the tool call, in this case `function`.'
      x-ms-enum:
        name: ToolCallType
        modelAsString: true
        values:
          - value: function
            description: The tool call type is function.
    chatCompletionRequestMessageTool:
      required:
        - tool_call_id
        - content
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessage'
        - type: object
          properties:
            tool_call_id:
              type: string
              description: Tool call that this message is responding to.
            content:
              type: string
              description: The contents of the message.
              nullable: true
          nullable: true
    chatCompletionRequestMessageFunction:
      required:
        - function_call_id
        - content
      allOf:
        - $ref: '#/components/schemas/chatCompletionRequestMessage'
        - type: object
          properties:
            role:
              enum:
                - function
              type: string
              description: 'The role of the messages author, in this case `function`.'
            name:
              type: string
              description: The contents of the message.
            content:
              type: string
              description: The contents of the message.
              nullable: true
          description: Deprecated. Message that represents a function.
          nullable: true
    createChatCompletionResponse:
      required:
        - id
        - object
        - created
        - model
        - choices
      type: object
      allOf:
        - $ref: '#/components/schemas/chatCompletionsResponseCommon'
        - properties:
            prompt_filter_results:
              $ref: '#/components/schemas/promptFilterResults'
            choices:
              type: array
              items:
                type: object
                allOf:
                  - $ref: '#/components/schemas/chatCompletionChoiceCommon'
                  - properties:
                      message:
                        $ref: '#/components/schemas/chatCompletionResponseMessage'
                      content_filter_results:
                        $ref: '#/components/schemas/contentFilterChoiceResults'
                      logprobs:
                        $ref: '#/components/schemas/chatCompletionChoiceLogProbs'
                      enhancements:
                        $ref: '#/components/schemas/enhancement'
    chatCompletionChoiceLogProbs:
      required:
        - content
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/chatCompletionTokenLogprob'
          description: A list of message content tokens with log probability information.
          nullable: true
      description: Log probability information for the choice.
      nullable: true
    chatCompletionTokenLogprob:
      required:
        - token
        - logprob
        - bytes
        - top_logprobs
      type: object
      properties:
        token:
          type: string
          description: The token.
        logprob:
          type: number
          description: The log probability of this token.
        bytes:
          type: array
          items:
            type: integer
          description:
            A list of integers representing the UTF-8 bytes representation of the token. Useful in instances where
            characters are represented by multiple tokens and their byte representations must be combined to generate
            the correct text representation. Can be `null` if there is no bytes representation for the token.
          nullable: true
        top_logprobs:
          type: array
          items:
            required:
              - token
              - logprob
              - bytes
            type: object
            properties:
              token:
                type: string
                description: The token.
              logprob:
                type: number
                description: The log probability of this token.
              bytes:
                type: array
                items:
                  type: integer
                description:
                  A list of integers representing the UTF-8 bytes representation of the token. Useful in instances where
                  characters are represented by multiple tokens and their byte representations must be combined to
                  generate the correct text representation. Can be `null` if there is no bytes representation for the
                  token.
                nullable: true
          description:
            'List of the most likely tokens and their log probability, at this token position. In rare cases, there may
            be fewer than the number of requested `top_logprobs` returned.'
    chatCompletionResponseMessage:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/chatCompletionResponseMessageRole'
        content:
          type: string
          description: The contents of the message.
          nullable: true
        tool_calls:
          type: array
          items:
            $ref: '#/components/schemas/chatCompletionMessageToolCall'
          description: 'The tool calls generated by the model, such as function calls.'
        function_call:
          $ref: '#/components/schemas/chatCompletionFunctionCall'
        context:
          $ref: '#/components/schemas/azureChatExtensionsMessageContext'
      description: A chat completion message generated by the model.
    chatCompletionResponseMessageRole:
      enum:
        - assistant
      type: string
      description: The role of the author of the response message.
    chatCompletionToolChoiceOption:
      oneOf:
        - enum:
            - none
            - auto
          type: string
          description:
            '`none` means the model will not call a function and instead generates a message. `auto` means the model can
            pick between generating a message or calling a function.'
        - $ref: '#/components/schemas/chatCompletionNamedToolChoice'
      description:
        'Controls which (if any) function is called by the model. `none` means the model will not call a function and
        instead generates a message. `auto` means the model can pick between generating a message or calling a function.
        Specifying a particular function via `{"type": "function", "function": {"name": "my_function"}}` forces the
        model to call that function.'
    chatCompletionNamedToolChoice:
      type: object
      properties:
        type:
          enum:
            - function
          type: string
          description: 'The type of the tool. Currently, only `function` is supported.'
        function:
          required:
            - name
          type: object
          properties:
            name:
              type: string
              description: The name of the function to call.
      description: Specifies a tool the model should use. Use to force the model to call a specific function.
    chatCompletionFunctionCall:
      required:
        - name
        - arguments
      type: object
      properties:
        name:
          type: string
          description: The name of the function to call.
        arguments:
          type: string
          description:
            'The arguments to call the function with, as generated by the model in JSON format. Note that the model does
            not always generate valid JSON, and may hallucinate parameters not defined by your function schema. Validate
            the arguments in your code before calling your function.'
      description:
        'Deprecated and replaced by `tool_calls`. The name and arguments of a function that should be called, as
        generated by the model.'
    chatCompletionsResponseCommon:
      required:
        - id
        - object
        - created
        - model
      type: object
      properties:
        id:
          type: string
          description: A unique identifier for the chat completion.
        object:
          $ref: '#/components/schemas/chatCompletionResponseObject'
        created:
          type: integer
          description: The Unix timestamp (in seconds) of when the chat completion was created.
          format: unixtime
        model:
          type: string
          description: The model used for the chat completion.
        usage:
          $ref: '#/components/schemas/completionUsage'
        system_fingerprint:
          type: string
          description:
            Can be used in conjunction with the `seed` request parameter to understand when backend changes have been
            made that might impact determinism.
    chatCompletionResponseObject:
      enum:
        - chat.completion
      type: string
      description: The object type.
      x-ms-enum:
        name: ChatCompletionResponseObject
        modelAsString: true
        values:
          - value: chat.completion
            description: The object type is chat completion.
    completionUsage:
      required:
        - prompt_tokens
        - completion_tokens
        - total_tokens
      type: object
      properties:
        prompt_tokens:
          type: integer
          description: Number of tokens in the prompt.
        completion_tokens:
          type: integer
          description: Number of tokens in the generated completion.
        total_tokens:
          type: integer
          description: Total number of tokens used in the request (prompt + completion).
      description: Usage statistics for the completion request.
    chatCompletionTool:
      required:
        - type
        - function
      type: object
      properties:
        type:
          $ref: '#/components/schemas/chatCompletionToolType'
        function:
          required:
            - name
            - parameters
          type: object
          properties:
            description:
              type: string
              description:
                'A description of what the function does, used by the model to choose when and how to call the function.'
            name:
              type: string
              description:
                'The name of the function to be called. Must be a-z, A-Z, 0-9, or contain underscores and dashes, with a
                maximum length of 64.'
            parameters:
              $ref: '#/components/schemas/chatCompletionFunctionParameters'
    chatCompletionToolType:
      enum:
        - function
      type: string
      description: 'The type of the tool. Currently, only `function` is supported.'
      x-ms-enum:
        name: ChatCompletionToolType
        modelAsString: true
        values:
          - value: function
            description: The tool type is function.
    chatCompletionChoiceCommon:
      type: object
      properties:
        index:
          type: integer
        finish_reason:
          type: string
    createTranslationRequest:
      required:
        - file
      type: object
      properties:
        file:
          type: string
          description: The audio file to translate.
          format: binary
        prompt:
          type: string
          description:
            An optional text to guide the model's style or continue a previous audio segment. The prompt should be in
            English.
        response_format:
          $ref: '#/components/schemas/audioResponseFormat'
        temperature:
          type: number
          description:
            'The sampling temperature, between 0 and 1. Higher values like 0.8 will make the output more random, while
            lower values like 0.2 will make it more focused and deterministic. If set to 0, the model will use log
            probability to automatically increase the temperature until certain thresholds are hit.'
          default: 0
      description: Translation request.
    audioResponse:
      required:
        - text
      type: object
      properties:
        text:
          type: string
          description: Translated or transcribed text.
      description: Translation or transcription response when response_format was json
    audioVerboseResponse:
      required:
        - text
      type: object
      allOf:
        - $ref: '#/components/schemas/audioResponse'
        - properties:
            task:
              enum:
                - transcribe
                - translate
              type: string
              description: Type of audio task.
              x-ms-enum:
                modelAsString: true
            language:
              type: string
              description: Language.
            duration:
              type: number
              description: Duration.
            segments:
              type: array
              items:
                $ref: '#/components/schemas/audioSegment'
      description: Translation or transcription response when response_format was verbose_json
    audioResponseFormat:
      title: AudioResponseFormat
      enum:
        - json
        - text
        - srt
        - verbose_json
        - vtt
      type: string
      description: Defines the format of the output.
      x-ms-enum:
        modelAsString: true
    createTranscriptionRequest:
      required:
        - file
      type: object
      properties:
        file:
          type: string
          description: The audio file object to transcribe.
          format: binary
        prompt:
          type: string
          description:
            An optional text to guide the model's style or continue a previous audio segment. The prompt should match
            the audio language.
        response_format:
          $ref: '#/components/schemas/audioResponseFormat'
        temperature:
          type: number
          description:
            'The sampling temperature, between 0 and 1. Higher values like 0.8 will make the output more random, while
            lower values like 0.2 will make it more focused and deterministic. If set to 0, the model will use log
            probability to automatically increase the temperature until certain thresholds are hit.'
          default: 0
        language:
          type: string
          description:
            The language of the input audio. Supplying the input language in ISO-639-1 format will improve accuracy and
            latency.
      description: Transcription request.
    audioSegment:
      type: object
      properties:
        id:
          type: integer
          description: Segment identifier.
        seek:
          type: number
          description: Offset of the segment.
        start:
          type: number
          description: Segment start offset.
        end:
          type: number
          description: Segment end offset.
        text:
          type: string
          description: Segment text.
        tokens:
          type: array
          items:
            type: number
          description: Tokens of the text.
        temperature:
          type: number
          description: Temperature.
        avg_logprob:
          type: number
          description: Average log probability.
        compression_ratio:
          type: number
          description: Compression ratio.
        no_speech_prob:
          type: number
          description: Probability of 'no speech'.
      description: Transcription or translation segment.
    createSpeechRequest:
      required:
        - input
        - voice
      type: object
      properties:
        input:
          maxLength: 4096
          type: string
          description: The text to synthesize audio for. The maximum length is 4096 characters.
        voice:
          enum:
            - alloy
            - echo
            - fable
            - onyx
            - nova
            - shimmer
          type: string
          description: The voice to use for speech synthesis.
        response_format:
          enum:
            - mp3
            - opus
            - aac
            - flac
          type: string
          description: The format to synthesize the audio in.
        speed:
          maximum: 4.0
          minimum: 0.25
          type: number
          description: The speed of the synthesize audio. Select a value from `0.25` to `4.0`. `1.0` is the default.
          default: 1
      description: Speech request.
    imageQuality:
      enum:
        - standard
        - hd
      type: string
      description: The quality of the image that will be generated.
      default: standard
      x-ms-enum:
        name: Quality
        modelAsString: true
        values:
          - value: standard
            description: Standard quality creates images with standard quality.
            name: Standard
          - value: hd
            description: HD quality creates images with finer details and greater consistency across the image.
            name: HD
    imagesResponseFormat:
      enum:
        - url
        - b64_json
      type: string
      description: The format in which the generated images are returned.
      default: url
      x-ms-enum:
        name: ImagesResponseFormat
        modelAsString: true
        values:
          - value: url
            description: The URL that provides temporary access to download the generated images.
            name: Url
          - value: b64_json
            description: The generated images are returned as base64 encoded string.
            name: Base64Json
    imageSize:
      enum:
        - 1792x1024
        - 1024x1792
        - 1024x1024
      type: string
      description: The size of the generated images.
      default: 1024x1024
      x-ms-enum:
        name: Size
        modelAsString: true
        values:
          - value: 1792x1024
            description: The desired size of the generated image is 1792x1024 pixels.
            name: Size1792x1024
          - value: 1024x1792
            description: The desired size of the generated image is 1024x1792 pixels.
            name: Size1024x1792
          - value: 1024x1024
            description: The desired size of the generated image is 1024x1024 pixels.
            name: Size1024x1024
    imageStyle:
      enum:
        - vivid
        - natural
      type: string
      description: The style of the generated images.
      default: vivid
      x-ms-enum:
        name: Style
        modelAsString: true
        values:
          - value: vivid
            description: Vivid creates images that are hyper-realistic and dramatic.
            name: Vivid
          - value: natural
            description: Natural creates images that are more natural and less hyper-realistic.
            name: Natural
    imageGenerationsRequest:
      required:
        - prompt
      type: object
      properties:
        prompt:
          minLength: 1
          type: string
          description: A text description of the desired image(s). The maximum length is 4000 characters.
          format: string
          example: a corgi in a field
        n:
          maximum: 1.0
          minimum: 1.0
          type: integer
          description: The number of images to generate.
          default: 1
        size:
          $ref: '#/components/schemas/imageSize'
        response_format:
          $ref: '#/components/schemas/imagesResponseFormat'
        user:
          type: string
          description: 'A unique identifier representing your end-user, which can help to monitor and detect abuse.'
          format: string
          example: user123456
        quality:
          $ref: '#/components/schemas/imageQuality'
        style:
          $ref: '#/components/schemas/imageStyle'
    generateImagesResponse:
      required:
        - created
        - data
      type: object
      properties:
        created:
          type: integer
          description: The unix timestamp when the operation was created.
          format: unixtime
          example: '1676540381'
        data:
          type: array
          items:
            $ref: '#/components/schemas/imageResult'
          description: 'The result data of the operation, if successful'
    imageResult:
      type: object
      properties:
        url:
          type: string
          description: The image url.
          example: https://www.contoso.com
        b64_json:
          type: string
          description: The base64 encoded image
        content_filter_results:
          $ref: '#/components/schemas/dalleContentFilterResults'
        revised_prompt:
          type: string
          description: 'The prompt that was used to generate the image, if there was any revision to the prompt.'
        prompt_filter_results:
          $ref: '#/components/schemas/dalleFilterResults'
      description: 'The image url or encoded image if successful, and an error otherwise.'
    enhancement:
      type: object
      properties:
        grounding:
          required:
            - lines
          type: object
          properties:
            lines:
              type: array
              items:
                $ref: '#/components/schemas/line'
          description: The grounding enhancement that returns the bounding box of the objects detected in the image.
    line:
      required:
        - text
        - spans
      type: object
      properties:
        text:
          type: string
        spans:
          type: array
          items:
            $ref: '#/components/schemas/span'
          description: An array of spans that represent detected objects and its bounding box information.
      description:
        'A content line object consisting of an adjacent sequence of content elements, such as words and selection
        marks.'
    span:
      required:
        - text
        - offset
        - length
        - polygon
      type: object
      properties:
        text:
          type: string
          description: The text content of the span that represents the detected object.
        offset:
          type: integer
          description:
            'The character offset within the text where the span begins. This offset is defined as the position of the
            first character of the span, counting from the start of the text as Unicode codepoints.'
        length:
          type: integer
          description: 'The length of the span in characters, measured in Unicode codepoints.'
        polygon:
          type: array
          items:
            type: object
            properties:
              x:
                type: number
                description: The x-coordinate of the point.
              y:
                type: number
                description: The y-coordinate of the point.
          description: An array of objects representing points in the polygon that encloses the detected object.
      description: A span object that represents a detected object and its bounding box information.
    assistantObject:
      title: Assistant
      required:
        - id
        - object
        - created_at
        - name
        - description
        - model
        - instructions
        - tools
        - file_ids
        - metadata
      type: object
      properties:
        id:
          type: string
          description: 'The identifier, which can be referenced in API endpoints.'
        object:
          enum:
            - assistant
          type: string
          description: 'The object type, which is always `assistant`.'
          x-ms-enum:
            name: AssistantObjectType
            modelAsString: true
            values:
              - value: assistant
                description: 'The object type, which is always assistant'
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the assistant was created.
        name:
          maxLength: 256
          type: string
          description: "The name of the assistant. The maximum length is 256 characters.\n"
          nullable: true
        description:
          maxLength: 512
          type: string
          description: "The description of the assistant. The maximum length is 512 characters.\n"
          nullable: true
        model:
          type: string
          description:
            "ID of the model to use. You can use the [List models](/docs/api-reference/models/list) API to see all of
            your available models, or see our [Model overview](/docs/models/overview) for descriptions of them.\n"
        instructions:
          maxLength: 32768
          type: string
          description: "The system instructions that the assistant uses. The maximum length is 32768 characters.\n"
          nullable: true
        tools:
          maxItems: 128
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/assistantToolsCode'
              - $ref: '#/components/schemas/assistantToolsRetrieval'
              - $ref: '#/components/schemas/assistantToolsFunction'
          description:
            "A list of tool enabled on the assistant. There can be a maximum of 128 tools per assistant. Tools can be of
            types `code_interpreter`, `retrieval`, or `function`.\n"
        file_ids:
          maxItems: 20
          type: array
          items:
            type: string
          description:
            "A list of [file](/docs/api-reference/files) IDs attached to this assistant. There can be a maximum of 20
            files attached to the assistant. Files are ordered by their creation date in ascending order.\n"
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      description: Represents an `assistant` that can call the model and use tools.
    createAssistantRequest:
      required:
        - model
      type: object
      properties:
        model:
          anyOf:
            - type: string
          description:
            "ID of the model to use. You can use the [List models](/docs/api-reference/models/list) API to see all of
            your available models, or see our [Model overview](/docs/models/overview) for descriptions of them.\n"
        name:
          maxLength: 256
          type: string
          description: "The name of the assistant. The maximum length is 256 characters.\n"
          nullable: true
        description:
          maxLength: 512
          type: string
          description: "The description of the assistant. The maximum length is 512 characters.\n"
          nullable: true
        instructions:
          maxLength: 32768
          type: string
          description: "The system instructions that the assistant uses. The maximum length is 32768 characters.\n"
          nullable: true
        tools:
          maxItems: 128
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/assistantToolsCode'
              - $ref: '#/components/schemas/assistantToolsRetrieval'
              - $ref: '#/components/schemas/assistantToolsFunction'
          description:
            "A list of tool enabled on the assistant. There can be a maximum of 128 tools per assistant. Tools can be of
            types `code_interpreter`, `retrieval`, or `function`.\n"
        file_ids:
          maxItems: 20
          type: array
          items:
            type: string
          description:
            "A list of [file](/docs/api-reference/files) IDs attached to this assistant. There can be a maximum of 20
            files attached to the assistant. Files are ordered by their creation date in ascending order.\n"
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    modifyAssistantRequest:
      type: object
      properties:
        model:
          anyOf:
            - type: string
          description:
            "ID of the model to use. You can use the [List models](/docs/api-reference/models/list) API to see all of
            your available models, or see our [Model overview](/docs/models/overview) for descriptions of them.\n"
        name:
          maxLength: 256
          type: string
          description: "The name of the assistant. The maximum length is 256 characters.\n"
          nullable: true
        description:
          maxLength: 512
          type: string
          description: "The description of the assistant. The maximum length is 512 characters.\n"
          nullable: true
        instructions:
          maxLength: 32768
          type: string
          description: "The system instructions that the assistant uses. The maximum length is 32768 characters.\n"
          nullable: true
        tools:
          maxItems: 128
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/assistantToolsCode'
              - $ref: '#/components/schemas/assistantToolsRetrieval'
              - $ref: '#/components/schemas/assistantToolsFunction'
          description:
            "A list of tool enabled on the assistant. There can be a maximum of 128 tools per assistant. Tools can be of
            types `code_interpreter`, `retrieval`, or `function`.\n"
        file_ids:
          maxItems: 20
          type: array
          items:
            type: string
          description:
            "A list of [File](/docs/api-reference/files) IDs attached to this assistant. There can be a maximum of 20
            files attached to the assistant. Files are ordered by their creation date in ascending order. If a file was
            previously attached to the list but does not show up in the list, it will be deleted from the assistant.\n"
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    deleteAssistantResponse:
      required:
        - id
        - object
        - deleted
      type: object
      properties:
        id:
          type: string
        deleted:
          type: boolean
        object:
          enum:
            - assistant.deleted
          type: string
          x-ms-enum:
            name: DeleteAssistantResponseState
            modelAsString: true
            values:
              - value: assistant.deleted
                description: The assistant is deleted
    listAssistantsResponse:
      required:
        - object
        - data
        - first_id
        - last_id
        - has_more
      type: object
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/assistantObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
    assistantToolsCode:
      title: Code interpreter tool
      required:
        - type
      type: object
      properties:
        type:
          enum:
            - code_interpreter
          type: string
          description: 'The type of tool being defined: `code_interpreter`'
          x-ms-enum:
            name: assistantToolsCodeType
            modelAsString: true
            values:
              - value: code_interpreter
                description: code_interpreter as type of tool being defined
    assistantToolsRetrieval:
      title: Retrieval tool
      required:
        - type
      type: object
      properties:
        type:
          enum:
            - retrieval
          type: string
          description: 'The type of tool being defined: `retrieval`'
          x-ms-enum:
            name: assistantToolsRetrievalType
            modelAsString: true
            values:
              - value: retrieval
                description: retrieval as type of tool being defined
    assistantToolsFunction:
      title: Function tool
      required:
        - type
        - function
      type: object
      properties:
        type:
          enum:
            - function
          type: string
          description: 'The type of tool being defined: `function`'
          x-ms-enum:
            name: assistantToolsFunction
            modelAsString: true
            values:
              - value: retrieval
                description: retrieval as type of tool being defined
        function:
          required:
            - name
            - parameters
            - description
          type: object
          properties:
            description:
              type: string
              description:
                'A description of what the function does, used by the model to choose when and how to call the function.'
            name:
              type: string
              description:
                'The name of the function to be called. Must be a-z, A-Z, 0-9, or contain underscores and dashes, with a
                maximum length of 64.'
            parameters:
              $ref: '#/components/schemas/chatCompletionFunctionParameters'
          description: The function definition.
    runObject:
      title: A run on a thread
      required:
        - id
        - object
        - created_at
        - thread_id
        - assistant_id
        - status
        - required_action
        - last_error
        - expires_at
        - started_at
        - cancelled_at
        - failed_at
        - completed_at
        - model
        - instructions
        - tools
        - file_ids
        - metadata
      type: object
      properties:
        id:
          type: string
          description: 'The identifier, which can be referenced in API endpoints.'
        object:
          enum:
            - thread.run
          type: string
          description: 'The object type, which is always `thread.run`.'
          x-ms-enum:
            name: runObjectType
            modelAsString: true
            values:
              - value: thread.run
                description: The run object type which is always thread.run
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run was created.
        thread_id:
          type: string
          description: 'The ID of the [thread](/docs/api-reference/threads) that was executed on as a part of this run.'
        assistant_id:
          type: string
          description: 'The ID of the [assistant](/docs/api-reference/assistants) used for execution of this run.'
        status:
          enum:
            - queued
            - in_progress
            - requires_action
            - cancelling
            - cancelled
            - failed
            - completed
            - expired
          type: string
          description:
            'The status of the run, which can be either `queued`, `in_progress`, `requires_action`, `cancelling`,
            `cancelled`, `failed`, `completed`, or `expired`.'
          x-ms-enum:
            name: RunObjectStatus
            modelAsString: true
            values:
              - value: queued
                description: The queued state
              - value: in_progress
                description: The in_progress state
              - value: requires_action
                description: The required_action state
              - value: cancelling
                description: The cancelling state
              - value: cancelled
                description: The cancelled state
              - value: failed
                description: The failed state
              - value: completed
                description: The completed state
              - value: expired
                description: The expired state
        required_action:
          required:
            - type
            - submit_tool_outputs
          type: object
          properties:
            type:
              enum:
                - submit_tool_outputs
              type: string
              description: 'For now, this is always `submit_tool_outputs`.'
            submit_tool_outputs:
              required:
                - tool_calls
              type: object
              properties:
                tool_calls:
                  type: array
                  items:
                    $ref: '#/components/schemas/runToolCallObject'
                  description: A list of the relevant tool calls.
              description: Details on the tool outputs needed for this run to continue.
          description: Details on the action required to continue the run. Will be `null` if no action is required.
          nullable: true
        last_error:
          required:
            - code
            - message
          type: object
          properties:
            code:
              enum:
                - server_error
                - rate_limit_exceeded
              type: string
              description: One of `server_error` or `rate_limit_exceeded`.
              x-ms-enum:
                name: LastErrorCode
                modelAsString: true
                values:
                  - value: server_error
                    description: The server failed to respond to request due to server error
                  - value: rate_limit_exceeded
                    description: The server failed to respond to request due to rate limit exceeded
            message:
              type: string
              description: A human-readable description of the error.
          description: The last error associated with this run. Will be `null` if there are no errors.
          nullable: true
        expires_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run will expire.
        started_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run was started.
          nullable: true
        cancelled_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run was cancelled.
          nullable: true
        failed_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run failed.
          nullable: true
        completed_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run was completed.
          nullable: true
        model:
          type: string
          description: 'The model that the [assistant](/docs/api-reference/assistants) used for this run.'
        instructions:
          type: string
          description: 'The instructions that the [assistant](/docs/api-reference/assistants) used for this run.'
        tools:
          maxItems: 20
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/assistantToolsCode'
              - $ref: '#/components/schemas/assistantToolsRetrieval'
              - $ref: '#/components/schemas/assistantToolsFunction'
          description: 'The list of tools that the [assistant](/docs/api-reference/assistants) used for this run.'
        file_ids:
          type: array
          items:
            type: string
          description:
            'The list of [File](/docs/api-reference/files) IDs the [assistant](/docs/api-reference/assistants) used for
            this run.'
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      description: 'Represents an execution run on a [thread](/docs/api-reference/threads).'
    createRunRequest:
      required:
        - thread_id
        - assistant_id
      type: object
      properties:
        assistant_id:
          type: string
          description: 'The ID of the [assistant](/docs/api-reference/assistants) to use to execute this run.'
        model:
          type: string
          description:
            'The ID of the [Model](/docs/api-reference/models) to be used to execute this run. If a value is provided
            here, it will override the model associated with the assistant. If not, the model associated with the
            assistant will be used.'
          nullable: true
        instructions:
          type: string
          description:
            Override the default system message of the assistant. This is useful for modifying the behavior on a per-run
            basis.
          nullable: true
        tools:
          maxItems: 20
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/assistantToolsCode'
              - $ref: '#/components/schemas/assistantToolsRetrieval'
              - $ref: '#/components/schemas/assistantToolsFunction'
          description:
            Override the tools the assistant can use for this run. This is useful for modifying the behavior on a
            per-run basis.
          nullable: true
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    listRunsResponse:
      required:
        - object
        - data
        - first_id
        - last_id
        - has_more
      type: object
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/runObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
    modifyRunRequest:
      type: object
      properties:
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    submitToolOutputsRunRequest:
      required:
        - tool_outputs
      type: object
      properties:
        tool_outputs:
          type: array
          items:
            type: object
            properties:
              tool_call_id:
                type: string
                description:
                  The ID of the tool call in the `required_action` object within the run object the output is being
                  submitted for.
              output:
                type: string
                description: The output of the tool call to be submitted to continue the run.
          description: A list of tools for which the outputs are being submitted.
      additionalProperties: false
    runToolCallObject:
      required:
        - id
        - type
        - function
      type: object
      properties:
        id:
          type: string
          description:
            'The ID of the tool call. This ID must be referenced when you submit the tool outputs in using the [Submit
            tool outputs to run](/docs/api-reference/runs/submitToolOutputs) endpoint.'
        type:
          enum:
            - function
          type: string
          description: 'The type of tool call the output is required for. For now, this is always `function`.'
          x-ms-enum:
            name: RunToolCallObjectType
            modelAsString: true
            values:
              - value: function
                description: The type of tool call the output is required for which is always `function` for now
              - value: rate_limit_exceeded
                description: The server failed to respond to request due to rate limit exceeded
        function:
          required:
            - name
            - arguments
          type: object
          properties:
            name:
              type: string
              description: The name of the function.
            arguments:
              type: string
              description: The arguments that the model expects you to pass to the function.
          description: The function definition.
      description: Tool call objects
    createThreadAndRunRequest:
      required:
        - thread_id
        - assistant_id
      type: object
      properties:
        assistant_id:
          type: string
          description: 'The ID of the [assistant](/docs/api-reference/assistants) to use to execute this run.'
        thread:
          $ref: '#/components/schemas/createThreadRequest'
        model:
          type: string
          description:
            'The ID of the [Model](/docs/api-reference/models) to be used to execute this run. If a value is provided
            here, it will override the model associated with the assistant. If not, the model associated with the
            assistant will be used.'
          nullable: true
        instructions:
          type: string
          description:
            Override the default system message of the assistant. This is useful for modifying the behavior on a per-run
            basis.
          nullable: true
        tools:
          maxItems: 20
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/assistantToolsCode'
              - $ref: '#/components/schemas/assistantToolsRetrieval'
              - $ref: '#/components/schemas/assistantToolsFunction'
          description:
            Override the tools the assistant can use for this run. This is useful for modifying the behavior on a
            per-run basis.
          nullable: true
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    threadObject:
      title: Thread
      required:
        - id
        - object
        - created_at
        - metadata
      type: object
      properties:
        id:
          type: string
          description: 'The identifier, which can be referenced in API endpoints.'
        object:
          enum:
            - thread
          type: string
          description: 'The object type, which is always `thread`.'
          x-ms-enum:
            name: ThreadObjectType
            modelAsString: true
            values:
              - value: thread
                description: The type of thread object which is always `thread`
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the thread was created.
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      description: 'Represents a thread that contains [messages](/docs/api-reference/messages).'
    createThreadRequest:
      type: object
      properties:
        messages:
          type: array
          items:
            $ref: '#/components/schemas/createMessageRequest'
          description: 'A list of [messages](/docs/api-reference/messages) to start the thread with.'
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    modifyThreadRequest:
      type: object
      properties:
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    deleteThreadResponse:
      required:
        - id
        - object
        - deleted
      type: object
      properties:
        id:
          type: string
        deleted:
          type: boolean
        object:
          enum:
            - thread.deleted
          type: string
          x-ms-enum:
            name: DeleteThreadResponseObjectState
            modelAsString: true
            values:
              - value: thread.deleted
                description: The delete thread response object state which is `thread.deleted`
    listThreadsResponse:
      required:
        - object
        - data
        - first_id
        - last_id
        - has_more
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/threadObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
    messageObject:
      title: The message object
      required:
        - id
        - object
        - created_at
        - thread_id
        - role
        - content
        - assistant_id
        - run_id
        - file_ids
        - metadata
      type: object
      properties:
        id:
          type: string
          description: 'The identifier, which can be referenced in API endpoints.'
        object:
          enum:
            - thread.message
          type: string
          description: 'The object type, which is always `thread.message`.'
          x-ms-enum:
            name: MessageObjectType
            modelAsString: true
            values:
              - value: thread.message
                description: The message object type which is `thread.message`
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the message was created.
        thread_id:
          type: string
          description: 'The [thread](/docs/api-reference/threads) ID that this message belongs to.'
        role:
          enum:
            - user
            - assistant
          type: string
          description: The entity that produced the message. One of `user` or `assistant`.
          x-ms-enum:
            name: MessageObjectRole
            modelAsString: true
            values:
              - value: user
                description: Message object role as `user`
              - value: assistant
                description: Message object role as `assistant`
        content:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/messageContentImageFileObject'
              - $ref: '#/components/schemas/messageContentTextObject'
          description: The content of the message in array of text and/or images.
        assistant_id:
          type: string
          description:
            'If applicable, the ID of the [assistant](/docs/api-reference/assistants) that authored this message.'
          nullable: true
        run_id:
          type: string
          description:
            'If applicable, the ID of the [run](/docs/api-reference/runs) associated with the authoring of this message.'
          nullable: true
        file_ids:
          maxItems: 10
          type: array
          items:
            type: string
          description:
            'A list of [file](/docs/api-reference/files) IDs that the assistant should use. Useful for tools like
            retrieval and code_interpreter that can access files. A maximum of 10 files can be attached to a message.'
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      description: 'Represents a message within a [thread](/docs/api-reference/threads).'
    createMessageRequest:
      required:
        - role
        - content
      type: object
      properties:
        role:
          enum:
            - user
          type: string
          description: The role of the entity that is creating the message. Currently only `user` is supported.
          x-ms-enum:
            name: CreateMessageRequestRole
            modelAsString: true
            values:
              - value: user
                description: The create message role as `user`
        content:
          maxLength: 32768
          minLength: 1
          type: string
          description: The content of the message.
        file_ids:
          maxItems: 10
          minItems: 1
          type: array
          items:
            type: string
          description:
            'A list of [File](/docs/api-reference/files) IDs that the message should use. There can be a maximum of 10
            files attached to a message. Useful for tools like `retrieval` and `code_interpreter` that can access and
            use files.'
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    modifyMessageRequest:
      type: object
      properties:
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      additionalProperties: false
    deleteMessageResponse:
      required:
        - id
        - object
        - deleted
      type: object
      properties:
        id:
          type: string
        deleted:
          type: boolean
        object:
          enum:
            - thread.message.deleted
          type: string
          x-ms-enum:
            name: DeleteMessageResponseObject
            modelAsString: true
            values:
              - value: thread.message.deleted
                description: The delete message response object state
    listMessagesResponse:
      required:
        - object
        - data
        - first_id
        - last_id
        - has_more
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/messageObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
    messageContentImageFileObject:
      title: Image file
      required:
        - type
        - image_file
      type: object
      properties:
        type:
          enum:
            - image_file
          type: string
          description: Always `image_file`.
          x-ms-enum:
            name: MessageContentImageFileObjectType
            modelAsString: true
            values:
              - value: image_file
                description: The message content image file type
        image_file:
          required:
            - file_id
          type: object
          properties:
            file_id:
              type: string
              description: 'The [File](/docs/api-reference/files) ID of the image in the message content.'
      description: 'References an image [File](/docs/api-reference/files) in the content of a message.'
    messageContentTextObject:
      title: Text
      required:
        - type
        - text
      type: object
      properties:
        type:
          enum:
            - text
          type: string
          description: Always `text`.
          x-ms-enum:
            name: messageContentTextObjectType
            modelAsString: true
            values:
              - value: text
                description: The message content text Object type
        text:
          required:
            - value
            - annotations
          type: object
          properties:
            value:
              type: string
              description: The data that makes up the text.
            annotations:
              type: array
              items:
                oneOf:
                  - $ref: '#/components/schemas/messageContentTextAnnotationsFileCitationObject'
                  - $ref: '#/components/schemas/messageContentTextAnnotationsFilePathObject'
      description: The text content that is part of a message.
    messageContentTextAnnotationsFileCitationObject:
      title: File citation
      required:
        - type
        - text
        - file_citation
        - start_index
        - end_index
      type: object
      properties:
        type:
          enum:
            - file_citation
          type: string
          description: Always `file_citation`.
          x-ms-enum:
            name: FileCitationObjectType
            modelAsString: true
            values:
              - value: file_citation
                description: The file citation object type
        text:
          type: string
          description: The text in the message content that needs to be replaced.
        file_citation:
          required:
            - file_id
            - quote
          type: object
          properties:
            file_id:
              type: string
              description: The ID of the specific File the citation is from.
            quote:
              type: string
              description: The specific quote in the file.
        start_index:
          minimum: 0.0
          type: integer
        end_index:
          minimum: 0.0
          type: integer
      description:
        A citation within the message that points to a specific quote from a specific File associated with the assistant
        or the message. Generated when the assistant uses the "retrieval" tool to search files.
    messageContentTextAnnotationsFilePathObject:
      title: File path
      required:
        - type
        - text
        - file_path
        - start_index
        - end_index
      type: object
      properties:
        type:
          enum:
            - file_path
          type: string
          description: Always `file_path`.
          x-ms-enum:
            name: FilePathObjectType
            modelAsString: true
            values:
              - value: file_path
                description: The file path object type
        text:
          type: string
          description: The text in the message content that needs to be replaced.
        file_path:
          required:
            - file_id
          type: object
          properties:
            file_id:
              type: string
              description: The ID of the file that was generated.
        start_index:
          minimum: 0.0
          type: integer
        end_index:
          minimum: 0.0
          type: integer
      description:
        A URL for the file that's generated when the assistant used the `code_interpreter` tool to generate a file.
    runStepObject:
      title: Run steps
      required:
        - id
        - object
        - created_at
        - assistant_id
        - thread_id
        - run_id
        - type
        - status
        - step_details
        - last_error
        - expired_at
        - cancelled_at
        - failed_at
        - completed_at
        - metadata
      type: object
      properties:
        id:
          type: string
          description: 'The identifier of the run step, which can be referenced in API endpoints.'
        object:
          enum:
            - assistant.run.step
          type: string
          description: 'The object type, which is always `assistant.run.step``.'
          x-ms-enum:
            name: RunStepObjectType
            modelAsString: true
            values:
              - value: assistant.run.step
                description: 'The object type, which is always `assistant.run.step`'
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run step was created.
        assistant_id:
          type: string
          description: 'The ID of the [assistant](/docs/api-reference/assistants) associated with the run step.'
        thread_id:
          type: string
          description: 'The ID of the [thread](/docs/api-reference/threads) that was run.'
        run_id:
          type: string
          description: 'The ID of the [run](/docs/api-reference/runs) that this run step is a part of.'
        type:
          enum:
            - message_creation
            - tool_calls
          type: string
          description: 'The type of run step, which can be either `message_creation` or `tool_calls`.'
          x-ms-enum:
            name: RunStepObjectType
            modelAsString: true
            values:
              - value: message_creation
                description: The message_creation run step
              - value: tool_calls
                description: The tool_calls run step
        status:
          enum:
            - in_progress
            - cancelled
            - failed
            - completed
            - expired
          type: string
          description:
            'The status of the run, which can be either `in_progress`, `cancelled`, `failed`, `completed`, or `expired`.'
          x-ms-enum:
            name: RunStepObjectStatus
            modelAsString: true
            values:
              - value: in_progress
                description: The in_progress run status
              - value: cancelled
                description: The cancelled run status
              - value: failed
                description: The cancelled run status
              - value: completed
                description: The cancelled run status
              - value: expired
                description: The cancelled run status
        step_details:
          type: object
          oneOf:
            - $ref: '#/components/schemas/runStepDetailsMessageCreationObject'
            - $ref: '#/components/schemas/runStepDetailsToolCallsObject'
          description: The details of the run step.
        last_error:
          required:
            - code
            - message
          type: object
          properties:
            code:
              enum:
                - server_error
                - rate_limit_exceeded
              type: string
              description: One of `server_error` or `rate_limit_exceeded`.
              x-ms-enum:
                name: LastErrorCode
                modelAsString: true
                values:
                  - value: server_error
                    description: The server_error
                  - value: rate_limit_exceeded
                    description: The rate_limit_exceeded status
            message:
              type: string
              description: A human-readable description of the error.
          description: The last error associated with this run step. Will be `null` if there are no errors.
          nullable: true
        expired_at:
          type: integer
          description:
            The Unix timestamp (in seconds) for when the run step expired. A step is considered expired if the parent
            run is expired.
          nullable: true
        cancelled_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run step was cancelled.
          nullable: true
        failed_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run step failed.
          nullable: true
        completed_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the run step completed.
          nullable: true
        metadata:
          type: object
          description:
            "Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional
            information about the object in a structured format. Keys can be a maximum of 64 characters long and values
            can be a maximum of 512 characters long.\n"
          nullable: true
      description: "Represents a step in execution of a run.\n"
    listRunStepsResponse:
      required:
        - object
        - data
        - first_id
        - last_id
        - has_more
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/runStepObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
    runStepDetailsMessageCreationObject:
      title: Message creation
      required:
        - type
        - message_creation
      type: object
      properties:
        type:
          enum:
            - message_creation
          type: string
          description: Always `message_creation``.
          x-ms-enum:
            name: RunStepDetailsMessageCreationObjectType
            modelAsString: true
            values:
              - value: message_creation
        message_creation:
          required:
            - message_id
          type: object
          properties:
            message_id:
              type: string
              description: The ID of the message that was created by this run step.
      description: Details of the message creation by the run step.
    runStepDetailsToolCallsObject:
      title: Tool calls
      required:
        - type
        - tool_calls
      type: object
      properties:
        type:
          enum:
            - tool_calls
          type: string
          description: Always `tool_calls`.
          x-ms-enum:
            name: RunStepDetailsToolCallsObjectType
            modelAsString: true
            values:
              - value: tool_calls
        tool_calls:
          type: array
          items:
            type: object
            oneOf:
              - $ref: '#/components/schemas/runStepDetailsToolCallsCodeObject'
              - $ref: '#/components/schemas/runStepDetailsToolCallsRetrievalObject'
              - $ref: '#/components/schemas/runStepDetailsToolCallsFunctionObject'
          description:
            "An array of tool calls the run step was involved in. These can be associated with one of three types of
            tools: `code_interpreter`, `retrieval`, or `function`.\n"
      description: Details of the tool call.
    runStepDetailsToolCallsCodeObject:
      title: Code interpreter tool call
      required:
        - id
        - type
        - code_interpreter
      type: object
      properties:
        id:
          type: string
          description: The ID of the tool call.
        type:
          enum:
            - code_interpreter
          type: string
          description: The type of tool call. This is always going to be `code_interpreter` for this type of tool call.
          x-ms-enum:
            name: RunStepDetailsToolCallsCodeObjectType
            modelAsString: true
            values:
              - value: code_interpreter
        code_interpreter:
          required:
            - input
            - outputs
          type: object
          properties:
            input:
              type: string
              description: The input to the Code Interpreter tool call.
            outputs:
              type: array
              items:
                type: object
                oneOf:
                  - $ref: '#/components/schemas/runStepDetailsToolCallsCodeOutputLogsObject'
                  - $ref: '#/components/schemas/runStepDetailsToolCallsCodeOutputImageObject'
              description:
                'The outputs from the Code Interpreter tool call. Code Interpreter can output one or more items,
                including text (`logs`) or images (`image`). Each of these are represented by a different object type.'
          description: The Code Interpreter tool call definition.
      description: Details of the Code Interpreter tool call the run step was involved in.
    runStepDetailsToolCallsCodeOutputLogsObject:
      title: Code interpreter log output
      required:
        - type
        - logs
      type: object
      properties:
        type:
          enum:
            - logs
          type: string
          description: Always `logs`.
          x-ms-enum:
            name: RunStepDetailsToolCallsCodeOutputLogsObjectType
            modelAsString: true
            values:
              - value: code_interpreter
        logs:
          type: string
          description: The text output from the Code Interpreter tool call.
      description: Text output from the Code Interpreter tool call as part of a run step.
    runStepDetailsToolCallsCodeOutputImageObject:
      title: Code interpreter image output
      required:
        - type
        - image
      type: object
      properties:
        type:
          enum:
            - image
          type: string
          description: Always `image`.
          x-ms-enum:
            name: RunStepDetailsToolCallsCodeOutputImageObjectType
            modelAsString: true
            values:
              - value: image
        image:
          required:
            - file_id
          type: object
          properties:
            file_id:
              type: string
              description: 'The [file](/docs/api-reference/files) ID of the image.'
    runStepDetailsToolCallsRetrievalObject:
      title: Retrieval tool call
      required:
        - id
        - type
        - retrieval
      type: object
      properties:
        id:
          type: string
          description: The ID of the tool call object.
        type:
          enum:
            - retrieval
          type: string
          description: The type of tool call. This is always going to be `retrieval` for this type of tool call.
          x-ms-enum:
            name: RunStepDetailsToolCallsRetrievalObjectType
            modelAsString: true
            values:
              - value: retrieval
        retrieval:
          type: object
          description: 'For now, this is always going to be an empty object.'
    runStepDetailsToolCallsFunctionObject:
      title: Function tool call
      required:
        - id
        - type
        - function
      type: object
      properties:
        id:
          type: string
          description: The ID of the tool call object.
        type:
          enum:
            - function
          type: string
          description: The type of tool call. This is always going to be `function` for this type of tool call.
          x-ms-enum:
            name: RunStepDetailsToolCallsFunctionObjectType
            modelAsString: true
            values:
              - value: function
        function:
          required:
            - name
            - arguments
            - output
          type: object
          properties:
            name:
              type: string
              description: The name of the function.
            arguments:
              type: string
              description: The arguments passed to the function.
            output:
              type: string
              description:
                'The output of the function. This will be `null` if the outputs have not been
                [submitted](/docs/api-reference/runs/submitToolOutputs) yet.'
              nullable: true
          description: The definition of the function that was called.
    assistantFileObject:
      title: Assistant files
      required:
        - id
        - object
        - created_at
        - assistant_id
      type: object
      properties:
        id:
          type: string
          description: 'The identifier, which can be referenced in API endpoints.'
        object:
          enum:
            - assistant.file
          type: string
          description: 'The object type, which is always `assistant.file`.'
          x-ms-enum:
            name: AssistantFileObjectType
            modelAsString: true
            values:
              - value: assistant.file
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the assistant file was created.
        assistant_id:
          type: string
          description: The assistant ID that the file is attached to.
      description: 'A list of [Files](/docs/api-reference/files) attached to an `assistant`.'
    createAssistantFileRequest:
      required:
        - file_id
      type: object
      properties:
        file_id:
          type: string
          description:
            'A [File](/docs/api-reference/files) ID (with `purpose="assistants"`) that the assistant should use. Useful
            for tools like `retrieval` and `code_interpreter` that can access files.'
      additionalProperties: false
    deleteAssistantFileResponse:
      required:
        - id
        - object
        - deleted
      type: object
      properties:
        id:
          type: string
        deleted:
          type: boolean
        object:
          enum:
            - assistant.file.deleted
          type: string
          x-ms-enum:
            name: DeleteAssistantFileResponseType
            modelAsString: true
            values:
              - value: assistant.file.deleted
      description:
        'Deletes the association between the assistant and the file, but does not delete the
        [File](/docs/api-reference/files) object itself.'
    listAssistantFilesResponse:
      required:
        - object
        - data
        - items
        - first_id
        - last_id
        - has_more
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/assistantFileObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
    messageFileObject:
      title: Message files
      required:
        - id
        - object
        - created_at
        - message_id
      type: object
      properties:
        id:
          type: string
          description: 'The identifier, which can be referenced in API endpoints.'
        object:
          enum:
            - thread.message.file
          type: string
          description: 'The object type, which is always `thread.message.file`.'
          x-ms-enum:
            name: MessageFileObjectType
            modelAsString: true
            values:
              - value: thread.message.file
        created_at:
          type: integer
          description: The Unix timestamp (in seconds) for when the message file was created.
        message_id:
          type: string
          description:
            'The ID of the [message](/docs/api-reference/messages) that the [File](/docs/api-reference/files) is
            attached to.'
      description: A list of files attached to a `message`.
    listMessageFilesResponse:
      required:
        - object
        - data
        - items
        - first_id
        - last_id
        - has_more
      properties:
        object:
          type: string
          example: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/messageFileObject'
        first_id:
          type: string
        last_id:
          type: string
        has_more:
          type: boolean
          example: false
  securitySchemes:
    apiKeyHeader:
      type: apiKey
      name: api-key
      in: header
    apiKeyQuery:
      type: apiKey
      name: subscription-key
      in: query
security:
  - apiKeyHeader: []
  - apiKeyQuery: []
