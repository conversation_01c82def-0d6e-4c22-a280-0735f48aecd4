{"openapi": "3.0.1", "info": {"title": "iCare Integration API", "description": "API for iCare integration", "version": "1.0"}, "servers": [{"url": "https://func-icare-sync-engine-{projectName}-{env}.azurewebsites.net/api"}], "paths": {"/ResidentProcessingHttpTrigger": {"post": {"summary": "Process Residents", "description": "Processes residents from iCare system and creates corresponding Minis", "operationId": "processResidents", "requestBody": {"description": "Request parameters for resident processing", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResidentProcessingRequest"}}}}, "responses": {"202": {"description": "Accepted - Resident processing started", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResidentProcessingResponse"}}}}, "400": {"description": "Bad Request - Missing required parameters"}, "500": {"description": "Internal Server Error"}}}}}, "components": {"schemas": {"ResidentProcessingRequest": {"type": "object", "properties": {"facilityId": {"type": "string", "description": "The ID of the facility"}, "locationId": {"type": "string", "description": "The ID of the location within the facility"}, "statusId": {"type": "string", "description": "Optional status ID to filter residents", "nullable": true}}, "required": ["facilityId", "locationId"]}, "ResidentProcessingResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the orchestration instance"}, "statusQueryGetUri": {"type": "string", "description": "The URI to query the status of the orchestration"}, "sendEventPostUri": {"type": "string", "description": "The URI to post events to the orchestration"}, "terminatePostUri": {"type": "string", "description": "The URI to terminate the orchestration"}, "purgeHistoryDeleteUri": {"type": "string", "description": "The URI to purge the orchestration history"}}}}}}