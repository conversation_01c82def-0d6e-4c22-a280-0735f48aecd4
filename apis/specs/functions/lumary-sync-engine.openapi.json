{"openapi": "3.0.1", "info": {"title": "Lumary Sync Engine API", "description": "API for Lumary integration with Salesforce", "version": "1.0"}, "servers": [{"url": "https://func-lumary-sync-{projectName}-{env}.azurewebsites.net/api"}], "paths": {"/sync": {"post": {"summary": "Manual Sync Trigger", "description": "Manually trigger synchronization for specified Lumary IDs and schemas", "operationId": "manualSyncTrigger", "requestBody": {"description": "Request parameters for manual sync", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManualSyncRequest"}}}}, "responses": {"202": {"description": "Accepted - Sync operation started", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncResponse"}}}}, "400": {"description": "Bad Request - Missing required parameters"}, "500": {"description": "Internal Server Error"}}}}, "/status/{instance_id}": {"get": {"summary": "Get Sync Status", "description": "Get the status of a sync operation by instance ID", "operationId": "getSyncStatus", "parameters": [{"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The orchestration instance ID"}], "responses": {"200": {"description": "Sync status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncStatusResponse"}}}}, "404": {"description": "Instance not found"}, "500": {"description": "Internal Server Error"}}}}, "/health": {"get": {"summary": "Health Check", "description": "Check the health status of the Lumary integration service", "operationId": "healthCheck", "responses": {"200": {"description": "Service is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "500": {"description": "Service is unhealthy"}}}}}, "components": {"schemas": {"ManualSyncRequest": {"type": "object", "properties": {"lumary_ids": {"type": "array", "items": {"type": "string"}, "description": "List of Lumary customer IDs to sync (e.g., ['EC-139', 'EC-140'])"}, "schemas": {"type": "array", "items": {"type": "string"}, "description": "Optional list of schemas to sync (e.g., ['bowel_records', 'health_conditions']). If not provided, all schemas will be synced.", "nullable": true}}, "required": ["lumary_ids"]}, "SyncResponse": {"type": "object", "properties": {"instance_id": {"type": "string", "description": "The ID of the orchestration instance"}, "status": {"type": "string", "description": "The status of the sync operation"}, "lumary_ids": {"type": "array", "items": {"type": "string"}, "description": "The Lumary IDs being synced"}, "schemas": {"type": "array", "items": {"type": "string"}, "description": "The schemas being synced", "nullable": true}, "status_query_uri": {"type": "string", "description": "The URI to query the status of the orchestration"}}}, "SyncStatusResponse": {"type": "object", "properties": {"instance_id": {"type": "string", "description": "The orchestration instance ID"}, "runtime_status": {"type": "string", "description": "The runtime status of the orchestration"}, "created_time": {"type": "string", "format": "date-time", "description": "When the orchestration was created", "nullable": true}, "last_updated_time": {"type": "string", "format": "date-time", "description": "When the orchestration was last updated", "nullable": true}, "input": {"type": "object", "description": "The input provided to the orchestration", "nullable": true}, "output": {"type": "object", "description": "The output from the orchestration", "nullable": true}, "custom_status": {"type": "object", "description": "Custom status information", "nullable": true}}}, "HealthResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "The health status of the service", "enum": ["healthy", "unhealthy"]}, "service": {"type": "string", "description": "The name of the service"}, "timestamp": {"type": "string", "format": "date-time", "description": "When the health check was performed"}, "error": {"type": "string", "description": "Error message if the service is unhealthy", "nullable": true}}}}}}