{"openapi": "3.0.1", "info": {"title": "Minikai Tools", "description": "API for various utility functions including SDA (Specialist Disability Accommodation) pricing data", "version": "1.0.0", "contact": {"name": "Platform Team"}}, "servers": [{"url": "https://{functionAppName}.azurewebsites.net/api", "description": "Azure Function App", "variables": {"functionAppName": {"default": "minikai-tools", "description": "The name of your Azure Function App"}}}], "paths": {"/datetime": {"get": {"summary": "Get current datetime", "description": "Returns the current datetime in the specified timezone and format.\nIf no timezone is specified, UTC is used.\nIf no format is specified, ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ) is used.", "operationId": "getDateTime", "tags": ["Utilities"], "parameters": [{"name": "timezone", "in": "query", "description": "The timezone to use (e.g., 'Australia/Sydney', 'America/New_York', 'UTC')", "required": false, "schema": {"type": "string"}, "example": "Australia/Sydney"}, {"name": "format", "in": "query", "description": "The format to use for the datetime (e.g., 'YYYY-MM-DD HH:mm:ss', 'DD/MM/YYYY')", "required": false, "schema": {"type": "string"}, "example": "YYYY-MM-DDTHH:mm:ssZ"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateTimeResponse"}, "example": {"datetime": "2025-05-22T12:34:56+10:00", "timezone": "Australia/Sydney", "utc_offset": "+10:00"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Invalid timezone or format"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Internal server error"}}}}}}}, "/sda-price-calculator": {"post": {"summary": "Query SDA pricing data", "description": "Queries the SDA pricing data to find matches based on provided filters.\nReturns up to 5 matching locations.\nIf no exact match is found for numeric values, returns the closest match.\nIf no date is specified, uses the latest available pricing tool date.", "operationId": "querySdaPricing", "tags": ["Pricing"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PricingRequest"}, "example": {"sda_funding_amount": 23654, "pricing_tool_date": "16 April 2024", "build_type": "New Build", "dwelling_category": "Villa", "number_of_residents": 3, "design_category": "Improved Liveability", "input_tax_credits": "No", "ooa": "Metropolitan", "sprinklers": "No"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PricingResponse"}, "example": {"pricing_tool_date": "16 April 2024", "filters_applied": {"sda_funding_amount": 23654, "build_type": "New Build", "dwelling_category": "Villa"}, "exact_matches": {"sda_funding_amount": true, "build_type": true, "dwelling_category": true}, "results": [{"Pricing Tool Date": "16 April 2024", "Build Type": "New Build", "Dwelling Category": "Villa", "Number of residents": 3, "SDA Funding Value": 23654, "Design Category": "Improved Liveability", "Input Tax Credits": "No", "OOA": "Metropolitan", "Sprinklers": "No", "Locations": ["Sydney", "Melbourne"]}]}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Invalid parameter value"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Internal server error"}}}}}}}}, "components": {"schemas": {"DateTimeResponse": {"type": "object", "properties": {"datetime": {"type": "string", "description": "The formatted datetime", "example": "2025-05-22T12:34:56+10:00"}, "timezone": {"type": "string", "description": "The timezone used", "example": "Australia/Sydney"}, "utc_offset": {"type": "string", "description": "The UTC offset for the timezone", "example": "+10:00"}}}, "PricingRequest": {"type": "object", "properties": {"sda_funding_amount": {"type": "number", "description": "The SDA funding amount to search for", "example": 23654}, "pricing_tool_date": {"type": "string", "description": "Optional specific pricing tool date to search in (e.g., \"16 April 2024\")", "example": "16 April 2024"}, "build_type": {"type": "string", "description": "Type of build (e.g., New Build, Existing)", "example": "New Build"}, "dwelling_category": {"type": "string", "description": "Category of dwelling", "example": "Villa"}, "number_of_residents": {"type": "integer", "description": "Number of residents", "example": 3}, "design_category": {"type": "string", "description": "Design category", "example": "Improved Liveability"}, "input_tax_credits": {"type": "string", "description": "Input tax credits status", "example": "No"}, "ooa": {"type": "string", "description": "Location classification", "example": "Metropolitan"}, "sprinklers": {"type": "string", "description": "Sprinkler system status", "example": "No"}}}, "PricingResponse": {"type": "object", "properties": {"pricing_tool_date": {"type": "string", "description": "The pricing tool date used for the search"}, "filters_applied": {"type": "object", "description": "The filters that were applied in the search", "additionalProperties": true}, "exact_matches": {"type": "object", "description": "Whether each filter found an exact match", "additionalProperties": {"type": "boolean"}}, "results": {"type": "array", "description": "Array of up to 5 matching pricing records", "items": {"$ref": "#/components/schemas/PricingResult"}}}}, "PricingResult": {"type": "object", "properties": {"Pricing Tool Date": {"type": "string", "description": "Date of the pricing tool"}, "Build Type": {"type": "string", "description": "Type of build (e.g., New Build, Existing)"}, "Dwelling Category": {"type": "string", "description": "Category of dwelling"}, "Number of residents": {"type": "integer", "description": "Number of residents"}, "SDA Funding Value": {"type": "number", "description": "SDA funding value"}, "Design Category": {"type": "string", "description": "Design category"}, "Input Tax Credits": {"type": "string", "description": "Input tax credits status"}, "OOA": {"type": "string", "description": "Location classification"}, "Sprinklers": {"type": "string", "description": "Sprinkler system status"}, "Locations": {"type": "array", "description": "List of locations where this pricing applies", "items": {"type": "string"}}}}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}}}}}}