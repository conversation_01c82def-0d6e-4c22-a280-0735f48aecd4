{"x-generator": "NSwag v14.0.8.0 (NJsonSchema v11.0.1.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "Minikai API", "version": "1.0.0"}, "paths": {"/api/Chats": {"post": {"tags": ["Chats"], "operationId": "PostApiChats", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChatCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string", "format": "guid"}}}}}}, "get": {"tags": ["Chats"], "operationId": "GetApiChatsAll", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatDto"}}}}}}}}, "/api/Chats/{chatId}": {"put": {"tags": ["Chats"], "operationId": "PutApiChats", "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChatCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Chats"], "operationId": "DeleteApiChats", "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": ""}}}, "get": {"tags": ["Chats"], "operationId": "GetApiChats", "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatDto"}}}}}}}, "/api/Chats/{chatId}/messages": {"get": {"tags": ["Chats"], "operationId": "GetApiChatsMessages", "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "roles", "in": "query", "style": "form", "explode": true, "schema": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ChatRole"}}, "x-position": 2}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 4}, {"name": "sortDescending", "in": "query", "schema": {"type": "boolean", "default": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfChatMessage"}}}}}}, "post": {"tags": ["Chats"], "operationId": "PostApiChatsMessages", "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "messages", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessage"}}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/DocumentItems": {"get": {"tags": ["DocumentItems"], "operationId": "GetDocumentItemsWithPagination", "parameters": [{"name": "ListId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "PageNumber", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}, {"name": "PageSize", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 3}, {"name": "GroupId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfDocumentItemDto"}}}}}}, "post": {"tags": ["DocumentItems"], "operationId": "CreateDocumentItem", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDocumentItemCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string", "format": "guid"}}}}}}}, "/api/DocumentItems/{id}": {"get": {"tags": ["DocumentItems"], "operationId": "GetDocumentItem", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentItemDto"}}}}}}}, "/api/DocumentItems/{documentId}": {"put": {"tags": ["DocumentItems"], "operationId": "UpdateDocumentItem", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDocumentItemCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["DocumentItems"], "operationId": "DeleteDocumentItem", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/DocumentItems/UpdateDetail/{documentId}": {"put": {"tags": ["DocumentItems"], "operationId": "UpdateDocumentItemDetail", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDocumentItemDetailCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/DocumentItems/{documentId}/files": {"post": {"tags": ["DocumentItems"], "operationId": "AddFiles", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 2}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "nullable": true, "items": {}}}}}}}, "responses": {"200": {"description": ""}}}}, "/api/DocumentItems/{documentId}/files/{fileId}": {"delete": {"tags": ["DocumentItems"], "operationId": "RemoveFile", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/DocumentItems/{documentId}/createFromDraft": {"post": {"tags": ["DocumentItems"], "operationId": "CreateDocumentItemFromDraft", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "type", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/DocumentItemType"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/DocumentItems/{documentId}/exportToPdf": {"post": {"tags": ["DocumentItems"], "operationId": "ExportToPdf", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportToPdfCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/DocumentLists": {"get": {"tags": ["DocumentLists"], "operationId": "GetDocumentLists", "parameters": [{"name": "groupId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentListDto"}}}}}}}, "post": {"tags": ["DocumentLists"], "operationId": "CreateDocumentList", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDocumentListCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/DocumentLists/{id}": {"put": {"tags": ["DocumentLists"], "operationId": "UpdateDocumentList", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDocumentListCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["DocumentLists"], "operationId": "DeleteDocumentList", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/Files/download/{docFileId}": {"get": {"tags": ["Files"], "operationId": "DownloadFile", "parameters": [{"name": "docFileId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Files/{id}": {"get": {"tags": ["Files"], "operationId": "GetDocumentFile", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/Files/names": {"get": {"tags": ["Files"], "operationId": "GetDocumentFileNames", "parameters": [{"name": "fileIds", "in": "query", "style": "form", "explode": true, "required": true, "schema": {"type": "array", "items": {"type": "string"}}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/Files/{externalSource}/{externalId}": {"get": {"tags": ["Files"], "operationId": "GetDocumentFileByExternalId", "parameters": [{"name": "externalSource", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "externalId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}, "put": {"tags": ["Files"], "operationId": "UpdateFileByExternalId", "parameters": [{"name": "externalSource", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "externalId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}, {"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 3}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "nullable": true}}}}}}, "responses": {"200": {"description": ""}}}}, "/api/Files/{externalSource}/files": {"post": {"tags": ["Files"], "operationId": "GetDocumentFilesByExternalIds", "parameters": [{"name": "externalSource", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "externalIds", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/Files": {"post": {"tags": ["Files"], "operationId": "UploadFiles", "parameters": [{"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "X-External-Ids", "x-originalName": "externalIdsJson", "in": "header", "schema": {"type": "string", "nullable": true}, "x-position": 3}, {"name": "X-External-Source", "x-originalName": "externalSource", "in": "header", "schema": {"type": "string", "nullable": true}, "x-position": 4}, {"name": "X-Tenant-Id", "x-originalName": "tenantId", "in": "header", "schema": {"type": "string", "nullable": true}, "x-position": 5}, {"name": "X-Allowed-Users", "x-originalName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "header", "schema": {"type": "string", "nullable": true}, "x-position": 6}, {"name": "X-Fields", "x-originalName": "<PERSON><PERSON><PERSON>", "in": "header", "schema": {"type": "string", "nullable": true}, "x-position": 7}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {}}}}}}}, "responses": {"200": {"description": ""}}}}, "/api/Files/files": {"delete": {"tags": ["Files"], "operationId": "RemoveFiles", "parameters": [{"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 3}], "requestBody": {"x-name": "fileIds", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/Forms": {"get": {"tags": ["Forms"], "operationId": "GetFormsWithPagination", "parameters": [{"name": "groupId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 3}, {"name": "type", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 5}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 6}, {"name": "sortDescending", "in": "query", "schema": {"type": "boolean", "default": true}, "x-position": 7}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfFormDto"}}}}}}, "post": {"tags": ["Forms"], "operationId": "CreateForm", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFormCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}}}}}}}, "/api/Forms/{formId}": {"get": {"tags": ["Forms"], "operationId": "GetForm", "parameters": [{"name": "formId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}}}}}}, "put": {"tags": ["Forms"], "operationId": "UpdateForm", "parameters": [{"name": "formId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFormCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}}}}}}, "delete": {"tags": ["Forms"], "operationId": "DeleteForm", "parameters": [{"name": "formId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "groupId", "in": "query", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/Forms/external/{externalSource}/{externalId}": {"get": {"tags": ["Forms"], "operationId": "GetFormByExternal", "parameters": [{"name": "externalSource", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "externalId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}}}}}}}, "/api/Forms/external/{externalSource}": {"post": {"tags": ["Forms"], "operationId": "GetFormsByExternal", "parameters": [{"name": "externalSource", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 4}, {"name": "type", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 6}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 7}, {"name": "sortDescending", "in": "query", "schema": {"type": "boolean", "default": true}, "x-position": 8}], "requestBody": {"x-name": "externalIds", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfFormDto"}}}}}}}, "/api/Groups": {"get": {"tags": ["Groups"], "operationId": "GetGroups", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GroupDto"}}}}}}}, "post": {"tags": ["Groups"], "operationId": "CreateGroup", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroupCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/Groups/{groupId}": {"get": {"tags": ["Groups"], "operationId": "GetGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupDto"}}}}}}, "put": {"tags": ["Groups"], "operationId": "UpdateGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGroupCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Groups"], "operationId": "DeleteGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/Groups/{groupId}/minis": {"get": {"tags": ["Groups"], "operationId": "GetGroupMinis", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupMinisDto"}}}}}}, "put": {"tags": ["Groups"], "operationId": "UpdateGroupMinis", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGroupMinisCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/Groups/{groupId}/users": {"get": {"tags": ["Groups"], "operationId": "GetGroupUsers", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GroupUserDto"}}}}}}}}, "/api/Groups/{groupId}/minis/{miniId}": {"post": {"tags": ["Groups"], "operationId": "AddMiniToGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Groups"], "operationId": "RemoveMiniFromGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/Groups/{groupId}/owners/{userId}": {"post": {"tags": ["Groups"], "operationId": "AddGroupOwner", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Groups"], "operationId": "RemoveGroupOwner", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/Groups/{groupId}/members/{userId}": {"delete": {"tags": ["Groups"], "operationId": "RemoveGroupMember", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}, "post": {"tags": ["Groups"], "operationId": "AddGroupMember", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/Groups/{groupId}/invite": {"post": {"tags": ["Groups"], "operationId": "InviteGroupMember", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InviteGroupMemberCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/MiniGroups/{groupId}/minigroups": {"get": {"tags": ["MiniGroups"], "operationId": "GetMiniGroups", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MiniGroupDto"}}}}}}}, "post": {"tags": ["MiniGroups"], "operationId": "CreateMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMiniGroupCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/MiniGroups/{groupId}/minigroups/{miniGroupId}": {"get": {"tags": ["MiniGroups"], "operationId": "GetMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniGroupDto"}}}}}}, "put": {"tags": ["MiniGroups"], "operationId": "UpdateMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMiniGroupCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["MiniGroups"], "operationId": "DeleteMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/MiniGroups/{groupId}/minigroups/{miniGroupId}/minis": {"get": {"tags": ["MiniGroups"], "operationId": "GetMiniGroupMinis", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SlimMiniDto"}}}}}}}, "post": {"tags": ["MiniGroups"], "operationId": "AddMiniToMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddMiniToMiniGroupCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": ""}}}}, "/api/MiniGroups/{groupId}/minigroups/{miniGroupId}/minis/{miniId}": {"delete": {"tags": ["MiniGroups"], "operationId": "RemoveMiniFromMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}, {"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 3}], "responses": {"200": {"description": ""}}}}, "/api/MiniGroups/{groupId}/minigroups/{miniGroupId}/usergroups": {"get": {"tags": ["MiniGroups"], "operationId": "GetMiniGroupUserGroups", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupDto"}}}}}}}, "post": {"tags": ["MiniGroups"], "operationId": "AddUserGroupToMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddUserGroupToMiniGroupCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": ""}}}}, "/api/MiniGroups/{groupId}/minigroups/{miniGroupId}/usergroups/{userGroupId}": {"delete": {"tags": ["MiniGroups"], "operationId": "RemoveUserGroupFromMiniGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 3}], "responses": {"200": {"description": ""}}}}, "/api/Minis": {"get": {"tags": ["Minis"], "operationId": "GetMinis", "parameters": [{"name": "groupId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SlimMiniDto"}}}}}}}, "post": {"tags": ["Minis"], "operationId": "CreateMini", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMiniCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/Minis/{externalSource}/{externalId}": {"get": {"tags": ["Minis"], "operationId": "GetExternalMini", "parameters": [{"name": "externalSource", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "externalId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SlimMiniDto"}}}}}}}, "/api/Minis/{id}": {"get": {"tags": ["Minis"], "operationId": "GetMini", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniDto"}}}}}}, "put": {"tags": ["Minis"], "operationId": "UpdateMini", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMiniCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Minis"], "operationId": "DeleteMini", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/Minis/{id}/slim": {"get": {"tags": ["Minis"], "operationId": "GetSlimMini", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SlimMiniDto"}}}}}}}, "/api/Minis/{id}/groups": {"put": {"tags": ["Minis"], "operationId": "UpdateMiniGroups", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMiniGroupsRequest"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/Minis/{id}/files": {"delete": {"tags": ["Minis"], "operationId": "RemoveAllMiniFiles", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/Minis/{miniId}/profiles": {"get": {"tags": ["Minis"], "operationId": "GetMiniProfiles", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfMiniProfileDto"}}}}}}, "post": {"tags": ["Minis"], "operationId": "CreateMiniProfile", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMiniProfileRequest"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/Minis/{miniId}/profiles/{version}": {"get": {"tags": ["Minis"], "operationId": "GetMiniProfile", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniProfileDto"}}}}}}}, "/api/Minis/{miniId}/profile/latest": {"get": {"tags": ["Minis"], "operationId": "GetLatestMiniProfile", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniProfileDto"}}}}}}}, "/api/MiniTemplates": {"get": {"tags": ["MiniTemplates"], "operationId": "GetMiniTemplates", "parameters": [{"name": "groupId", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MiniTemplateDto"}}}}}}}, "post": {"tags": ["MiniTemplates"], "operationId": "CreateMiniTemplate", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMiniTemplateCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string", "format": "guid"}}}}}}}, "/api/MiniTemplates/{id}": {"get": {"tags": ["MiniTemplates"], "operationId": "GetMiniTemplate", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "miniId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniTemplateDto"}}}}}}, "delete": {"tags": ["MiniTemplates"], "operationId": "DeleteMiniTemplate", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/MiniTemplates/{miniTemplateId}/tools/{toolId}": {"post": {"tags": ["MiniTemplates"], "operationId": "AddToolToMiniTemplate", "parameters": [{"name": "miniTemplateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "toolId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}, "delete": {"tags": ["MiniTemplates"], "operationId": "RemoveToolFromMiniTemplate", "parameters": [{"name": "miniTemplateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "toolId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/MiniTemplates/{miniTemplateId}/groups": {"put": {"tags": ["MiniTemplates"], "operationId": "UpdateMiniTemplateGroups", "parameters": [{"name": "miniTemplateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMiniTemplateGroupsCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/MiniTemplates/{templateId}/versions": {"get": {"tags": ["MiniTemplates"], "operationId": "GetMiniTemplateVersions", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfMiniTemplateVersionDto"}}}}}}, "post": {"tags": ["MiniTemplates"], "operationId": "CreateMiniTemplateVersion", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMiniTemplateVersionRequest"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/MiniTemplates/{templateId}/versions/{version}": {"get": {"tags": ["MiniTemplates"], "operationId": "GetMiniTemplateVersion", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniTemplateVersionDto"}}}}}}}, "/api/MiniTemplates/{templateId}/versions/latest": {"get": {"tags": ["MiniTemplates"], "operationId": "GetLatestMiniTemplateVersion", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniTemplateVersionDto"}}}}}}}, "/api/Search/text": {"post": {"tags": ["Search"], "operationId": "SearchText", "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchTextRequest"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResultDto"}}}}}}}, "/api/Search/vector": {"post": {"tags": ["Search"], "operationId": "SearchVector", "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchVectorRequest"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResultDto"}}}}}}}, "/api/Search/hybrid": {"post": {"tags": ["Search"], "operationId": "SearchHybrid", "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchHybridRequest"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResultDto"}}}}}}}, "/api/Tools": {"get": {"tags": ["Tools"], "operationId": "GetTools", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDto"}}}}}}}, "post": {"tags": ["Tools"], "operationId": "CreateTool", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateToolCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/Tools/{id}": {"get": {"tags": ["Tools"], "operationId": "GetTool", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToolDto"}}}}}}, "put": {"tags": ["Tools"], "operationId": "UpdateTool", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateToolCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Tools"], "operationId": "DeleteTool", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": ""}}}}, "/api/Tools/mini/{miniId}": {"get": {"tags": ["Tools"], "operationId": "GetToolsForMini", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDto"}}}}}}}}, "/api/Tools/mini/{miniId}/tools/{toolId}": {"post": {"tags": ["Tools"], "operationId": "AddToolToMini", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "toolId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}, "delete": {"tags": ["Tools"], "operationId": "RemoveToolFromMini", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "toolId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/Tools/mini/{miniId}/tools": {"put": {"tags": ["Tools"], "operationId": "UpdateMiniTools", "parameters": [{"name": "miniId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMiniToolsCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/UserGroups/{groupId}/usergroups": {"get": {"tags": ["UserGroups"], "operationId": "GetUserGroups", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupDto"}}}}}}}, "post": {"tags": ["UserGroups"], "operationId": "CreateUserGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserGroupCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": ""}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}": {"get": {"tags": ["UserGroups"], "operationId": "GetUserGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupDto"}}}}}}, "put": {"tags": ["UserGroups"], "operationId": "UpdateUserGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserGroupCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": ""}}}, "delete": {"tags": ["UserGroups"], "operationId": "DeleteUserGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": ""}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}/members": {"post": {"tags": ["UserGroups"], "operationId": "AddUserGroupMember", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddUserGroupMemberCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": ""}}}, "get": {"tags": ["UserGroups"], "operationId": "GetUserGroupMembers", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupMemberDto"}}}}}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}/members/{userId}": {"delete": {"tags": ["UserGroups"], "operationId": "RemoveUserGroupMember", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 3}], "responses": {"200": {"description": ""}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}/members/{userId}/role": {"put": {"tags": ["UserGroups"], "operationId": "UpdateUserGroupMemberRole", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 3}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserGroupMemberRoleCommand"}}}, "required": true, "x-position": 4}, "responses": {"200": {"description": ""}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}/minis": {"get": {"tags": ["UserGroups"], "operationId": "GetUserGroupMinis", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SlimMiniDto"}}}}}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}/minigroups": {"get": {"tags": ["UserGroups"], "operationId": "GetUserGroupMiniGroups", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MiniGroupDto"}}}}}}}, "post": {"tags": ["UserGroups"], "operationId": "AddMiniGroupToUserGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddMiniGroupToUserGroupCommand"}}}, "required": true, "x-position": 3}, "responses": {"200": {"description": ""}}}}, "/api/UserGroups/{groupId}/usergroups/{userGroupId}/minigroups/{miniGroupId}": {"delete": {"tags": ["UserGroups"], "operationId": "RemoveMiniGroupFromUserGroup", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "userGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 2}, {"name": "miniGroupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 3}], "responses": {"200": {"description": ""}}}}, "/api/WeatherForecasts": {"get": {"tags": ["WeatherForecasts"], "operationId": "GetWeatherForecasts", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"CreateChatCommand": {"type": "object", "additionalProperties": false, "properties": {"miniId": {"type": "string"}, "threadId": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "lastMessageSentDatetime": {"type": "string", "format": "date-time"}}}, "UpdateChatCommand": {"type": "object", "additionalProperties": false, "properties": {"chatId": {"type": "string", "format": "guid"}, "summary": {"type": "string", "nullable": true}, "lastMessageSentDatetime": {"type": "string", "format": "date-time", "nullable": true}, "userIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "documentIds": {"type": "array", "nullable": true, "items": {"type": "integer", "format": "int32"}}}}, "ChatDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "chatId": {"type": "string", "format": "guid"}, "miniId": {"type": "string"}, "threadId": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "lastMessageSentDatetime": {"type": "string", "format": "date-time"}, "userIds": {"type": "array", "items": {"type": "string"}}, "documentIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "PaginatedListOfChatMessage": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ChatMessage"}}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}}, "ChatMessage": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "chatId": {"type": "string", "format": "guid"}, "role": {"$ref": "#/components/schemas/ChatRole"}, "content": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "annotations": {"type": "array", "nullable": true, "items": {}}, "parts": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/MessagePart"}}}}, "ChatRole": {"type": "string", "description": "", "x-enumNames": ["system", "user", "assistant", "tool"], "enum": ["system", "user", "assistant", "tool"]}, "MessagePart": {"type": "object", "discriminator": {"propertyName": "type", "mapping": {"text": "#/components/schemas/TextPart", "reasoning": "#/components/schemas/ReasoningPart", "tool-invocation": "#/components/schemas/ToolInvocationPart", "source": "#/components/schemas/SourcePart", "system": "#/components/schemas/SystemPart"}}, "x-abstract": true, "additionalProperties": false, "required": ["type"], "properties": {"type": {"type": "string"}}}, "TextPart": {"allOf": [{"$ref": "#/components/schemas/MessagePart"}, {"type": "object", "additionalProperties": false, "properties": {"text": {"type": "string"}}}]}, "ReasoningPart": {"allOf": [{"$ref": "#/components/schemas/MessagePart"}, {"type": "object", "additionalProperties": false, "properties": {"reasoning": {"type": "string"}}}]}, "ToolInvocationPart": {"allOf": [{"$ref": "#/components/schemas/MessagePart"}, {"type": "object", "additionalProperties": false, "properties": {"state": {"$ref": "#/components/schemas/ToolInvocationState"}, "toolCallId": {"type": "string"}, "toolName": {"type": "string"}, "args": {"nullable": true}, "result": {"nullable": true}}}]}, "ToolInvocationState": {"type": "string", "description": "", "x-enumNames": ["partial_call", "call", "result"], "enum": ["partial_call", "call", "result"]}, "SourcePart": {"allOf": [{"$ref": "#/components/schemas/MessagePart"}, {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "url": {"type": "string"}, "sourceType": {"type": "string"}, "title": {"type": "string", "nullable": true}}}]}, "SystemPart": {"allOf": [{"$ref": "#/components/schemas/MessagePart"}, {"type": "object", "additionalProperties": false, "properties": {"model": {"type": "string", "nullable": true}, "tools": {"type": "array", "nullable": true, "items": {}}}}]}, "PaginatedListOfDocumentItemDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentItemDto"}}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}}, "DocumentItemDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "listId": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "docId": {"type": "string", "format": "guid"}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentFileDto"}}, "data": {"type": "string", "nullable": true}, "type": {"type": "string"}, "createdBy": {"type": "string", "nullable": true}, "groupId": {"type": "string", "nullable": true}, "created": {"type": "string", "format": "date-time"}}}, "DocumentFileDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "fileId": {"type": "string"}, "docFileId": {"type": "string", "format": "guid"}, "name": {"type": "string", "nullable": true}, "contentType": {"type": "string", "nullable": true}, "fileExtension": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int64"}, "groupId": {"type": "string", "nullable": true}}}, "CreateDocumentItemCommand": {"type": "object", "additionalProperties": false, "properties": {"listId": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "groupId": {"type": "string"}, "data": {"type": "string", "nullable": true}, "fileIds": {"type": "array", "nullable": true, "items": {"type": "string", "format": "guid"}}, "type": {"$ref": "#/components/schemas/DocumentItemType"}}}, "DocumentItemType": {"type": "integer", "description": "", "x-enumNames": ["Draft", "Template", "Library"], "enum": [0, 1, 2]}, "UpdateDocumentItemCommand": {"type": "object", "additionalProperties": false, "properties": {"documentId": {"type": "string", "format": "guid"}, "title": {"type": "string", "nullable": true}, "groupId": {"type": "string"}}}, "UpdateDocumentItemDetailCommand": {"type": "object", "additionalProperties": false, "properties": {"documentId": {"type": "string", "format": "guid"}, "listId": {"type": "integer", "format": "int32"}, "data": {"type": "string", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "groupId": {"type": "string", "nullable": true}}}, "ExportToPdfCommand": {"type": "object", "additionalProperties": false, "properties": {"documentId": {"type": "string", "format": "guid"}, "content": {"type": "string"}}}, "DocumentListDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "groupId": {"type": "string", "nullable": true}, "defaultMiniId": {"type": "integer", "format": "int32", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentItemDto"}}}}, "CreateDocumentListCommand": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}, "groupId": {"type": "string"}, "defaultMiniId": {"type": "integer", "format": "int32", "nullable": true}}}, "UpdateDocumentListCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "groupId": {"type": "string"}, "defaultMiniId": {"type": "integer", "format": "int32", "nullable": true}}}, "PaginatedListOfFormDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FormDto"}}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}}, "FormDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "documentType": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string"}, "tenantId": {"type": "string"}, "allowedUsers": {"type": "array", "items": {"type": "string"}}, "allowedMinis": {"type": "array", "items": {"type": "string"}}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "uploadedDate": {"type": "string", "format": "date-time"}, "targetDate": {"type": "string", "format": "date-time", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FormFieldDto"}}, "externalSource": {"type": "string", "nullable": true}, "externalId": {"type": "string", "nullable": true}}}, "FormFieldDto": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string"}, "valueString": {"type": "string", "nullable": true}, "valueNumber": {"type": "number", "format": "double", "nullable": true}, "valueDate": {"type": "string", "format": "date-time", "nullable": true}, "value": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/FormFieldType"}}}, "FormFieldType": {"type": "integer", "description": "", "x-enumNames": ["Text", "Radio", "Checkbox", "DateTime"], "enum": [0, 1, 2, 3]}, "Form": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "documentType": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "formType": {"type": "string"}, "tenantId": {"type": "string"}, "allowedUsers": {"type": "array", "items": {"type": "string"}}, "allowedMinis": {"type": "array", "items": {"type": "string"}}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "uploadedDate": {"type": "string", "format": "date-time"}, "filePath": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "targetDate": {"type": "string", "format": "date-time", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FormField"}}, "externalSource": {"type": "string", "nullable": true}, "externalId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "importance": {"type": "string", "nullable": true}, "searchable": {"type": "boolean", "nullable": true}}}, "FormField": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string"}, "valueString": {"type": "string", "nullable": true}, "valueNumber": {"type": "number", "format": "double", "nullable": true}, "valueDate": {"type": "string", "format": "date-time", "nullable": true}}}, "CreateFormCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string"}, "groupId": {"type": "string"}, "miniId": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "targetDate": {"type": "string", "format": "date-time", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FormFieldDto"}}, "externalSource": {"type": "string", "nullable": true}, "externalId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "importance": {"type": "string", "nullable": true}, "searchable": {"type": "boolean", "nullable": true}}}, "UpdateFormCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string"}, "groupId": {"type": "string"}, "targetDate": {"type": "string", "format": "date-time", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FormFieldDto"}}, "externalSource": {"type": "string", "nullable": true}, "externalId": {"type": "string", "nullable": true}}}, "GroupDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "groupId": {"type": "string"}, "name": {"type": "string"}}}, "GroupMinisDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "groupId": {"type": "string"}, "name": {"type": "string"}, "minis": {"type": "array", "items": {"$ref": "#/components/schemas/MiniDto"}}}}, "MiniDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "templateId": {"type": "string", "format": "guid", "nullable": true}, "template": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/MiniTemplateDto"}]}, "miniGroupId": {"type": "string", "format": "guid", "nullable": true}, "miniGroupName": {"type": "string", "nullable": true}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/GroupDto"}}, "tools": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDto"}}, "documentFiles": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentFileDto"}}}}, "MiniTemplateDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/GroupDto"}}, "tools": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDto"}}}}, "ToolDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string"}, "endpoint": {"type": "string"}, "schema": {"type": "string"}}}, "GroupUserDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "displayName": {"type": "string"}, "givenName": {"type": "string", "nullable": true}, "surname": {"type": "string", "nullable": true}, "email": {"type": "string"}, "profilePicture": {"type": "string", "format": "byte", "nullable": true}, "isGroupOwner": {"type": "boolean"}}}, "CreateGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"groupId": {"type": "string"}, "name": {"type": "string"}}}, "UpdateGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"groupId": {"type": "string"}, "name": {"type": "string"}}}, "UpdateGroupMinisCommand": {"type": "object", "additionalProperties": false, "properties": {"groupId": {"type": "string"}, "miniIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "InviteGroupMemberCommand": {"type": "object", "additionalProperties": false, "properties": {"groupId": {"type": "string"}, "email": {"type": "string"}}}, "MiniGroupDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "groupId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "minis": {"type": "array", "items": {"$ref": "#/components/schemas/SlimMiniDto"}}, "userGroups": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupDto"}}}}, "SlimMiniDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "templateId": {"type": "string", "format": "guid", "nullable": true}, "template": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/MiniTemplateDto"}]}, "miniGroupId": {"type": "string", "format": "guid", "nullable": true}, "miniGroupName": {"type": "string", "nullable": true}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/GroupDto"}}, "tools": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDto"}}}}, "UserGroupDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "groupId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupMemberDto"}}, "miniGroups": {"type": "array", "items": {"$ref": "#/components/schemas/MiniGroupDto"}}}}, "UserGroupMemberDto": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "isManager": {"type": "boolean"}}}, "CreateMiniGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "groupId": {"type": "string"}}}, "UpdateMiniGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "groupId": {"type": "string"}}}, "AddMiniToMiniGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"miniGroupId": {"type": "string", "format": "guid"}, "miniId": {"type": "integer", "format": "int32"}}}, "AddUserGroupToMiniGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"miniGroupId": {"type": "string", "format": "guid"}, "userGroupId": {"type": "integer", "format": "int32"}}}, "CreateMiniCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "groupIds": {"type": "array", "items": {"type": "string"}}, "templateId": {"type": "string", "format": "guid", "nullable": true}, "miniGroupId": {"type": "string", "format": "guid", "nullable": true}, "externalId": {"type": "string", "nullable": true}, "externalSource": {"type": "string", "nullable": true}}}, "UpdateMiniCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "templateId": {"type": "string", "format": "guid", "nullable": true}, "miniGroupId": {"type": "string", "format": "guid", "nullable": true}, "externalId": {"type": "string", "nullable": true}, "externalSource": {"type": "string", "nullable": true}}}, "UpdateMiniGroupsRequest": {"type": "object", "additionalProperties": false, "properties": {"groupIds": {"type": "array", "items": {"type": "string"}}}}, "PaginatedListOfMiniProfileDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MiniProfileDto"}}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}}, "MiniProfileDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "version": {"type": "string"}, "miniId": {"type": "string"}, "templateId": {"type": "string", "format": "guid"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/ProfileValueDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}}}, "ProfileValueDto": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "CreateMiniProfileRequest": {"type": "object", "additionalProperties": false, "properties": {"values": {"type": "array", "items": {"$ref": "#/components/schemas/CreateMiniProfileValueRequest"}}}}, "CreateMiniProfileValueRequest": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "CreateMiniTemplateCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "groupIds": {"type": "array", "items": {"type": "string"}}}}, "UpdateMiniTemplateGroupsCommand": {"type": "object", "additionalProperties": false, "properties": {"miniTemplateId": {"type": "string", "format": "guid"}, "groupIds": {"type": "array", "items": {"type": "string"}}}}, "PaginatedListOfMiniTemplateVersionDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MiniTemplateVersionDto"}}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}}, "MiniTemplateVersionDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "templateId": {"type": "string", "format": "guid"}, "version": {"type": "string"}, "system": {"type": "string"}, "tokens": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateTokenDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}}}, "TemplateTokenDto": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string", "nullable": true}}}, "CreateMiniTemplateVersionRequest": {"type": "object", "additionalProperties": false, "properties": {"system": {"type": "string"}, "tokens": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTemplateTokenRequest"}}}}, "CreateTemplateTokenRequest": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string", "nullable": true}}}, "SearchResultDto": {"type": "object", "additionalProperties": false, "properties": {"count": {"type": "integer", "format": "int64"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SearchDocumentDto"}}, "facets": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/FacetResultDto"}}}}}, "SearchDocumentDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "minikaiId": {"type": "string"}, "documentType": {"type": "string", "nullable": true}, "formType": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "fields": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/DocumentFieldDto"}}, "fileName": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "uploadedDate": {"type": "string", "format": "date-time", "nullable": true}, "targetDate": {"type": "string", "format": "date-time", "nullable": true}, "score": {"type": "number", "format": "double"}, "semanticScore": {"type": "number", "format": "double", "nullable": true}, "semanticCaptions": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/SemanticCaptionDto"}}}}, "DocumentFieldDto": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string"}, "valueString": {"type": "string", "nullable": true}, "valueNumber": {"type": "number", "format": "double", "nullable": true}, "valueDate": {"type": "string", "format": "date-time", "nullable": true}}}, "SemanticCaptionDto": {"type": "object", "additionalProperties": false, "properties": {"text": {"type": "string"}, "highlights": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/SemanticHighlightDto"}}}}, "SemanticHighlightDto": {"type": "object", "additionalProperties": false, "properties": {"text": {"type": "string"}}}, "FacetResultDto": {"type": "object", "additionalProperties": false, "properties": {"value": {"type": "string"}, "count": {"type": "integer", "format": "int64"}}}, "SearchTextRequest": {"type": "object", "additionalProperties": false, "properties": {"query": {"type": "string"}, "miniId": {"type": "string"}, "targetDateStart": {"type": "string", "nullable": true}, "targetDateEnd": {"type": "string", "nullable": true}, "top": {"type": "integer", "format": "int32", "nullable": true}}}, "SearchVectorRequest": {"type": "object", "additionalProperties": false, "properties": {"query": {"type": "string"}, "miniId": {"type": "string"}, "targetDateStart": {"type": "string", "nullable": true}, "targetDateEnd": {"type": "string", "nullable": true}, "top": {"type": "integer", "format": "int32", "nullable": true}}}, "SearchHybridRequest": {"type": "object", "additionalProperties": false, "properties": {"query": {"type": "string"}, "miniId": {"type": "string"}, "targetDateStart": {"type": "string", "nullable": true}, "targetDateEnd": {"type": "string", "nullable": true}, "top": {"type": "integer", "format": "int32", "nullable": true}}}, "CreateToolCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "endpoint": {"type": "string"}, "schema": {"type": "string"}}}, "UpdateToolCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string"}, "endpoint": {"type": "string"}, "schema": {"type": "string"}}}, "UpdateMiniToolsCommand": {"type": "object", "additionalProperties": false, "properties": {"miniId": {"type": "integer", "format": "int32"}, "toolIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "CreateUserGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "groupId": {"type": "string"}}}, "UpdateUserGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "groupId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}}}, "AddUserGroupMemberCommand": {"type": "object", "additionalProperties": false, "properties": {"userGroupId": {"type": "integer", "format": "int32"}, "userId": {"type": "string"}, "isManager": {"type": "boolean"}}}, "UpdateUserGroupMemberRoleCommand": {"type": "object", "additionalProperties": false, "properties": {"userGroupId": {"type": "integer", "format": "int32"}, "userId": {"type": "string"}, "isManager": {"type": "boolean"}}}, "AddMiniGroupToUserGroupCommand": {"type": "object", "additionalProperties": false, "properties": {"userGroupId": {"type": "integer", "format": "int32"}, "miniGroupId": {"type": "string", "format": "guid"}}}, "WeatherForecast": {"type": "object", "additionalProperties": false, "properties": {"date": {"type": "string", "format": "date-time"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32"}, "summary": {"type": "string", "nullable": true}}}}}}