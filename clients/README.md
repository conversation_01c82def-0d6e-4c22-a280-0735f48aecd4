# Centralised Python Clients 

This directory holds python clients athat are generated from OpenAPI specs defined in `/apis/`


```
clients/
├── client-name/                # Generation places client-name here          
│   └── ...                 
│       └── ...
├── config-client-name.json     # Config file for generating the package
└── README.md
```


## Generating Python SDKs from internal OpenAPI Specs

We use the openapi-python-client library to generate Python SDKs from our internal OpenAPI specs.

This should be done from a purpose made dev container (see `.devcontainer/python-client-generator/devcontainer.json`).

```bash
openapi-python-client generate \
    --path apis/specs/client-app/{spec-name}.openapi.json \
    --config clients/config-{project_name}.json \
    --output-path clients/{project-name} \
    --overwrite
```

You should:

2.  Config should be in the format `config-{project_name}.json`
1.  Generate a python client with a specified config.
1.  It should be put in the `/clients/` folder.
1.  This client can be installed as a package into python projects using `pip install -e clients/{project_name}`

## Example Flow: Re-generating an exising Client (minikai-frontend-client)

Assuming that the config has been setup correctly, you can re-generate the client by:

1.  **Re-open the repo in the container**  
    \_Dev Containers ▸ python-client-generator (VS Code command palette).

    - Press command+shift+p and type "Reopen in Container".
    - Select "Dev Containers: Reopen in Container".
    - Select python-client-generator.

2.  Bump the Version number

    If you want to bump the version number, you can do so by editing the `package_version` field in the config file.

3.  Run the generator for your chosen client:

    ```bash
    openapi-python-client generate \
      --path apis/specs/client-app/client-api.openapi.json \
      --config clients/config-minikai-frontend-client.json \
      --output-path clients/minikai-frontend-client \
      --overwrite
    ```

## Example Flow: Generate a new Client (minikai-backend-client)

### WIP - Fill this in when the the backend client is made
