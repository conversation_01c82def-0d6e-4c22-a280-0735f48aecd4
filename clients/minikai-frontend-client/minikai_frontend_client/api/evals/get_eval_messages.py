from http import HTTPStatus
from typing import Any, Optional, Union

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.error_response import ErrorResponse
from ...models.get_messages_response import GetMessagesResponse
from ...types import UNSET, Response


def _get_kwargs(
    *,
    chat_id: str,
) -> dict[str, Any]:
    params: dict[str, Any] = {}

    params["chatId"] = chat_id

    params = {k: v for k, v in params.items() if v is not UNSET and v is not None}

    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": "/evals/get-messages",
        "params": params,
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[Union[ErrorResponse, GetMessagesResponse]]:
    if response.status_code == 200:
        response_200 = GetMessagesResponse.from_dict(response.json())

        return response_200
    if response.status_code == 400:
        response_400 = ErrorResponse.from_dict(response.json())

        return response_400
    if response.status_code == 401:
        response_401 = ErrorResponse.from_dict(response.json())

        return response_401
    if response.status_code == 500:
        response_500 = ErrorResponse.from_dict(response.json())

        return response_500
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[Union[ErrorResponse, GetMessagesResponse]]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    *,
    client: AuthenticatedClient,
    chat_id: str,
) -> Response[Union[ErrorResponse, GetMessagesResponse]]:
    """Get All Chat Messages for Evaluation

     Retrieve all messages for a chat session including system messages for evaluation purposes

    Args:
        chat_id (str):  Example: 550e8400-e29b-41d4-a716-446655440000.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[ErrorResponse, GetMessagesResponse]]
    """

    kwargs = _get_kwargs(
        chat_id=chat_id,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    *,
    client: AuthenticatedClient,
    chat_id: str,
) -> Optional[Union[ErrorResponse, GetMessagesResponse]]:
    """Get All Chat Messages for Evaluation

     Retrieve all messages for a chat session including system messages for evaluation purposes

    Args:
        chat_id (str):  Example: 550e8400-e29b-41d4-a716-446655440000.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[ErrorResponse, GetMessagesResponse]
    """

    return sync_detailed(
        client=client,
        chat_id=chat_id,
    ).parsed


async def asyncio_detailed(
    *,
    client: AuthenticatedClient,
    chat_id: str,
) -> Response[Union[ErrorResponse, GetMessagesResponse]]:
    """Get All Chat Messages for Evaluation

     Retrieve all messages for a chat session including system messages for evaluation purposes

    Args:
        chat_id (str):  Example: 550e8400-e29b-41d4-a716-446655440000.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[ErrorResponse, GetMessagesResponse]]
    """

    kwargs = _get_kwargs(
        chat_id=chat_id,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    *,
    client: AuthenticatedClient,
    chat_id: str,
) -> Optional[Union[ErrorResponse, GetMessagesResponse]]:
    """Get All Chat Messages for Evaluation

     Retrieve all messages for a chat session including system messages for evaluation purposes

    Args:
        chat_id (str):  Example: 550e8400-e29b-41d4-a716-446655440000.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[ErrorResponse, GetMessagesResponse]
    """

    return (
        await asyncio_detailed(
            client=client,
            chat_id=chat_id,
        )
    ).parsed
