from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.ui_message import UIMessage


T = TypeVar("T", bound="ChatRequest")


@_attrs_define
class ChatRequest:
    """
    Attributes:
        messages (list['UIMessage']): Array of chat messages in the conversation
        chat_id (str): Unique identifier for the chat session (GUID format) Example:
            550e8400-e29b-41d4-a716-************.
        mini_id (str): Unique identifier for the Mini (AI assistant configuration) Example: 123.
        timezone (Union[Unset, str]): Optional timezone for the user Example: Australia/Sydney.
    """

    messages: list["UIMessage"]
    chat_id: str
    mini_id: str
    timezone: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        messages = []
        for messages_item_data in self.messages:
            messages_item = messages_item_data.to_dict()
            messages.append(messages_item)

        chat_id = self.chat_id

        mini_id = self.mini_id

        timezone = self.timezone

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "messages": messages,
                "chatId": chat_id,
                "miniId": mini_id,
            }
        )
        if timezone is not UNSET:
            field_dict["timezone"] = timezone

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.ui_message import UIMessage

        d = dict(src_dict)
        messages = []
        _messages = d.pop("messages")
        for messages_item_data in _messages:
            messages_item = UIMessage.from_dict(messages_item_data)

            messages.append(messages_item)

        chat_id = d.pop("chatId")

        mini_id = d.pop("miniId")

        timezone = d.pop("timezone", UNSET)

        chat_request = cls(
            messages=messages,
            chat_id=chat_id,
            mini_id=mini_id,
            timezone=timezone,
        )

        chat_request.additional_properties = d
        return chat_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
