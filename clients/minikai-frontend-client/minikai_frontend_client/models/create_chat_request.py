from collections.abc import Mapping
from typing import Any, TypeVar

from attrs import define as _attrs_define
from attrs import field as _attrs_field

T = TypeVar("T", bound="CreateChatRequest")


@_attrs_define
class CreateChatRequest:
    """
    Attributes:
        mini_id (int): Unique identifier for the Mini (AI assistant configuration) Example: 123.
        first_message_content (str): Content of the first message to start the chat Example: Hello, I need help with my
            evaluation task.
    """

    mini_id: int
    first_message_content: str
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        mini_id = self.mini_id

        first_message_content = self.first_message_content

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "miniId": mini_id,
                "firstMessageContent": first_message_content,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        mini_id = d.pop("miniId")

        first_message_content = d.pop("firstMessageContent")

        create_chat_request = cls(
            mini_id=mini_id,
            first_message_content=first_message_content,
        )

        create_chat_request.additional_properties = d
        return create_chat_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
