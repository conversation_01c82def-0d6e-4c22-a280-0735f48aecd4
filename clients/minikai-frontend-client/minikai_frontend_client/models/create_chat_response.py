from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="CreateChatResponse")


@_attrs_define
class CreateChatResponse:
    """
    Attributes:
        chat_id (Union[Unset, str]): Unique identifier for the created chat session (GUID format) Example:
            550e8400-e29b-41d4-a716-************.
        success (Union[Unset, bool]): Whether the chat was created successfully Example: True.
        error (Union[Unset, str]): Error message if creation failed
    """

    chat_id: Union[Unset, str] = UNSET
    success: Union[Unset, bool] = UNSET
    error: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        chat_id = self.chat_id

        success = self.success

        error = self.error

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if chat_id is not UNSET:
            field_dict["chatId"] = chat_id
        if success is not UNSET:
            field_dict["success"] = success
        if error is not UNSET:
            field_dict["error"] = error

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        chat_id = d.pop("chatId", UNSET)

        success = d.pop("success", UNSET)

        error = d.pop("error", UNSET)

        create_chat_response = cls(
            chat_id=chat_id,
            success=success,
            error=error,
        )

        create_chat_response.additional_properties = d
        return create_chat_response

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
