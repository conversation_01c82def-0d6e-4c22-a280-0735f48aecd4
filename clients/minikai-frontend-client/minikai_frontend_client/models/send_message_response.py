from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.token_usage import TokenUsage
    from ..models.ui_message import UIMessage


T = TypeVar("T", bound="SendMessageResponse")


@_attrs_define
class SendMessageResponse:
    """
    Attributes:
        message (Union[Unset, UIMessage]):
        success (Union[Unset, bool]): Whether the message was processed successfully Example: True.
        usage (Union[Unset, TokenUsage]):
        error (Union[Unset, str]): Error message if processing failed
    """

    message: Union[Unset, "UIMessage"] = UNSET
    success: Union[Unset, bool] = UNSET
    usage: Union[Unset, "TokenUsage"] = UNSET
    error: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        message: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.message, Unset):
            message = self.message.to_dict()

        success = self.success

        usage: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.usage, Unset):
            usage = self.usage.to_dict()

        error = self.error

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if message is not UNSET:
            field_dict["message"] = message
        if success is not UNSET:
            field_dict["success"] = success
        if usage is not UNSET:
            field_dict["usage"] = usage
        if error is not UNSET:
            field_dict["error"] = error

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.token_usage import TokenUsage
        from ..models.ui_message import UIMessage

        d = dict(src_dict)
        _message = d.pop("message", UNSET)
        message: Union[Unset, UIMessage]
        if isinstance(_message, Unset):
            message = UNSET
        else:
            message = UIMessage.from_dict(_message)

        success = d.pop("success", UNSET)

        _usage = d.pop("usage", UNSET)
        usage: Union[Unset, TokenUsage]
        if isinstance(_usage, Unset):
            usage = UNSET
        else:
            usage = TokenUsage.from_dict(_usage)

        error = d.pop("error", UNSET)

        send_message_response = cls(
            message=message,
            success=success,
            usage=usage,
            error=error,
        )

        send_message_response.additional_properties = d
        return send_message_response

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
