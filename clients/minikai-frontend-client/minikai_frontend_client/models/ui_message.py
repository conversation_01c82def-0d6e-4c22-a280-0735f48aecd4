import datetime
from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..models.ui_message_role import UIMessageRole
from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.ui_message_annotations_item import UIMessageAnnotationsItem
    from ..models.ui_message_parts_item import UIMessagePartsItem


T = TypeVar("T", bound="UIMessage")


@_attrs_define
class UIMessage:
    """
    Attributes:
        role (UIMessageRole): The role of the message sender
        content (str): The text content of the message Example: Hello, can you help me with my query?.
        id (Union[Unset, str]): Optional unique identifier for the message
        created_at (Union[Unset, datetime.datetime]): Optional timestamp when the message was created
        parts (Union[Unset, list['UIMessagePartsItem']]): Optional array of message parts for complex messages
        annotations (Union[Unset, list['UIMessageAnnotationsItem']]): Optional annotations for the message
    """

    role: UIMessageRole
    content: str
    id: Union[Unset, str] = UNSET
    created_at: Union[Unset, datetime.datetime] = UNSET
    parts: Union[Unset, list["UIMessagePartsItem"]] = UNSET
    annotations: Union[Unset, list["UIMessageAnnotationsItem"]] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        role = self.role.value

        content = self.content

        id = self.id

        created_at: Union[Unset, str] = UNSET
        if not isinstance(self.created_at, Unset):
            created_at = self.created_at.isoformat()

        parts: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.parts, Unset):
            parts = []
            for parts_item_data in self.parts:
                parts_item = parts_item_data.to_dict()
                parts.append(parts_item)

        annotations: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.annotations, Unset):
            annotations = []
            for annotations_item_data in self.annotations:
                annotations_item = annotations_item_data.to_dict()
                annotations.append(annotations_item)

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "role": role,
                "content": content,
            }
        )
        if id is not UNSET:
            field_dict["id"] = id
        if created_at is not UNSET:
            field_dict["createdAt"] = created_at
        if parts is not UNSET:
            field_dict["parts"] = parts
        if annotations is not UNSET:
            field_dict["annotations"] = annotations

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.ui_message_annotations_item import UIMessageAnnotationsItem
        from ..models.ui_message_parts_item import UIMessagePartsItem

        d = dict(src_dict)
        role = UIMessageRole(d.pop("role"))

        content = d.pop("content")

        id = d.pop("id", UNSET)

        _created_at = d.pop("createdAt", UNSET)
        created_at: Union[Unset, datetime.datetime]
        if isinstance(_created_at, Unset):
            created_at = UNSET
        else:
            created_at = isoparse(_created_at)

        parts = []
        _parts = d.pop("parts", UNSET)
        for parts_item_data in _parts or []:
            parts_item = UIMessagePartsItem.from_dict(parts_item_data)

            parts.append(parts_item)

        annotations = []
        _annotations = d.pop("annotations", UNSET)
        for annotations_item_data in _annotations or []:
            annotations_item = UIMessageAnnotationsItem.from_dict(annotations_item_data)

            annotations.append(annotations_item)

        ui_message = cls(
            role=role,
            content=content,
            id=id,
            created_at=created_at,
            parts=parts,
            annotations=annotations,
        )

        ui_message.additional_properties = d
        return ui_message

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
