[tool.poetry]
name = "minikai-frontend-client"
version = "0.1.0"
description = "A client library for accessing Minikai Client API"
authors = []
readme = "README.md"
packages = [
    {include = "minikai_frontend_client"},
]
include = ["CHANGELOG.md", "minikai_frontend_client/py.typed"]


[tool.poetry.dependencies]
python = "^3.9"
httpx = ">=0.23.0,<0.29.0"
attrs = ">=22.2.0"
python-dateutil = "^2.8.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = ["F", "I", "UP"]
