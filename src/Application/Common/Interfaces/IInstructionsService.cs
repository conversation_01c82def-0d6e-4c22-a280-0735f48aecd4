namespace Minikai.Application.Common.Interfaces;

/// <summary>
/// Client for generating populated instructions by combining template versions and profiles
/// </summary>
public interface IMiniInstructionsClient
{
    /// <summary>
    /// Gets the populated instructions for a mini by combining the latest template version and profile
    /// </summary>
    /// <param name="miniId">The mini ID</param>
    /// <param name="templateId">The template ID (can be null)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Populated instructions with tokens replaced, or null if no template/version exists</returns>
    Task<string?> GetPopulatedInstructionsAsync(int miniId, Guid? templateId, CancellationToken cancellationToken = default);
}
