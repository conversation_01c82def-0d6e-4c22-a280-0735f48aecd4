using Minikai.Domain.Entities;

namespace Minikai.Application.Common.Interfaces;

/// <summary>
/// Interface for managing mini profiles
/// </summary>
public interface IMiniProfilesClient
{
    /// <summary>
    /// Creates a new mini profile
    /// </summary>
    Task<MiniProfile> CreateMiniProfileAsync(MiniProfile profile, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets paginated profiles for a specific mini
    /// </summary>
    Task<(List<MiniProfile> Items, int TotalCount)> GetMiniProfilesAsync(int miniId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a specific profile version for a mini
    /// </summary>
    Task<MiniProfile> GetMiniProfileAsync(int miniId, string version, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the latest profile for a mini
    /// </summary>
    Task<MiniProfile?> GetLatestMiniProfileAsync(int miniId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a specific profile version
    /// </summary>
    Task DeleteMiniProfileAsync(int miniId, string version, CancellationToken cancellationToken = default);
}
