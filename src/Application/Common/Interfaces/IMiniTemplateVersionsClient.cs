using Minikai.Application.Common.Models;
using Minikai.Domain.Entities;

namespace Minikai.Application.Common.Interfaces;

/// <summary>
/// Interface for managing mini template versions
/// </summary>
public interface IMiniTemplateVersionsClient
{
    /// <summary>
    /// Creates a new mini template version
    /// </summary>
    Task<MiniTemplateVersion> CreateMiniTemplateVersionAsync(MiniTemplateVersion templateVersion, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets paginated versions for a specific template
    /// </summary>
    Task<(List<MiniTemplateVersion> Items, int TotalCount)> GetMiniTemplateVersionsAsync(Guid templateId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a specific version of a template
    /// </summary>
    Task<MiniTemplateVersion> GetMiniTemplateVersionAsync(Guid templateId, string version, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the latest version of a template
    /// </summary>
    Task<MiniTemplateVersion?> GetLatestMiniTemplateVersionAsync(Guid templateId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a specific version of a template
    /// </summary>
    Task DeleteMiniTemplateVersionAsync(Guid templateId, string version, CancellationToken cancellationToken = default);
}
