using Minikai.Domain.Entities;

namespace Minikai.Application.MiniProfiles.Common.Models;

public class MiniProfileDto
{
    public required string Id { get; init; }
    public required string Version { get; init; }
    public required string MiniId { get; init; }
    public required Guid TemplateId { get; init; }
    public required List<ProfileValueDto> Values { get; init; } = new();
    public required DateTime CreatedAt { get; init; }
    public required string CreatedBy { get; init; }

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<MiniProfile, MiniProfileDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            
            CreateMap<ProfileValue, ProfileValueDto>();
        }
    }
}

public class ProfileValueDto
{
    public required string Name { get; init; }
    public required string Value { get; init; }
}
