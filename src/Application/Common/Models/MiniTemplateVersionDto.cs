using Minikai.Domain.Entities;

namespace Minikai.Application.MiniTemplateVersions.Common.Models;

public class MiniTemplateVersionDto
{
    public required string Id { get; init; }
    public required Guid TemplateId { get; init; }
    public required string Version { get; init; }
    public required string System { get; init; }
    public required List<TemplateTokenDto> Tokens { get; init; } = new();
    public required DateTime CreatedAt { get; init; }
    public required string CreatedBy { get; init; }
}

public class TemplateTokenDto
{
    public required string Name { get; init; }
    public required string Type { get; init; }
    public string? Description { get; init; }

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<MiniTemplateVersion, MiniTemplateVersionDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            
            CreateMap<TemplateToken, TemplateTokenDto>();
        }
    }
}
