using Minikai.Application.Common.Interfaces;

namespace Minikai.Application.DocumentLists.Commands.UpdateDocumentList;

public class UpdateDocumentListCommandValidator : AbstractValidator<UpdateDocumentListCommand>
{
    private readonly IApplicationDbContext _context;

    public UpdateDocumentListCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(v => v.Title)
            .NotEmpty()
            .MaximumLength(200)
            .MustAsync(BeUniqueTitle)
                .WithMessage("'{PropertyName}' must be unique.")
                .WithErrorCode("Unique");
    }

    public async Task<bool> BeUniqueTitle(UpdateDocumentListCommand model, string title, CancellationToken cancellationToken)
    {
        return await _context.DocumentLists
            .Where(l => l.Id != model.Id)
            .AllAsync(l => l.Title != title, cancellationToken);
    }
}
