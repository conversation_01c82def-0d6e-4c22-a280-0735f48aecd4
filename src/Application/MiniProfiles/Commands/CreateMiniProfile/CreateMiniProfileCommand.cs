using Minikai.Application.Common.Interfaces;
using Minikai.Domain.Entities;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniProfiles.Commands.CreateMiniProfile;

public record CreateMiniProfileCommand : IRequest<string>
{
    public required int MiniId { get; init; }
    public required List<CreateProfileValueDto> Values { get; init; } = new();
}

public class CreateProfileValueDto
{
    public required string Name { get; init; }
    public required string Value { get; init; }
}

public class CreateMiniProfileCommandValidator : AbstractValidator<CreateMiniProfileCommand>
{
    public CreateMiniProfileCommandValidator()
    {
        RuleFor(v => v.MiniId)
            .NotEmpty();

        RuleFor(v => v.Values)
            .NotNull();

        RuleForEach(v => v.Values).ChildRules(value =>
        {
            value.RuleFor(v => v.Name)
                .NotEmpty()
                .MaximumLength(100);

            value.RuleFor(v => v.Value)
                .NotEmpty()
                .MaximumLength(1000);
        });
    }
}

public class CreateMiniProfileCommandHandler : IRequestHandler<CreateMiniProfileCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniProfilesClient _miniProfilesClient;
    private readonly IMiniTemplateVersionsClient _miniTemplateVersionsClient;
    private readonly ICurrentUser _currentUser;

    public CreateMiniProfileCommandHandler(
        IApplicationDbContext context,
        IMiniProfilesClient miniProfilesClient,
        IMiniTemplateVersionsClient miniTemplateVersionsClient,
        ICurrentUser currentUser)
    {
        _context = context;
        _miniProfilesClient = miniProfilesClient;
        _miniTemplateVersionsClient = miniTemplateVersionsClient;
        _currentUser = currentUser;
    }

    public async Task<string> Handle(CreateMiniProfileCommand request, CancellationToken cancellationToken)
    {
        // Verify mini exists and user has access
        var mini = await _context.Minis
            .Include(m => m.MiniToGroups)
            .ThenInclude(mg => mg.Group)
            .FirstOrDefaultAsync(m => m.Id == request.MiniId, cancellationToken);

        Guard.Against.NotFound(request.MiniId, mini);

        // Check authorization - only admins or mini owners can create profiles
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = mini.MiniToGroups
                .Any(mg => _currentUser.Groups.Contains(mg.Group.GroupId));

            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Get the mini's template
        if (mini.TemplateId == null)
        {
            throw new InvalidOperationException($"Mini {request.MiniId} does not have a template assigned");
        }

        var template = await _context.MiniTemplates
            .FirstOrDefaultAsync(t => t.Id == mini.TemplateId.Value, cancellationToken);

        if (template == null)
        {
            throw new NotFoundException(nameof(MiniTemplate), mini.TemplateId.Value.ToString());
        }

        // Get the latest template version to validate against
        var latestTemplateVersion = await _miniTemplateVersionsClient.GetLatestMiniTemplateVersionAsync(mini.TemplateId.Value, cancellationToken);

        if (latestTemplateVersion == null)
        {
            throw new InvalidOperationException($"No template version found for template {mini.TemplateId.Value}");
        }

        // Validate profile values against template tokens
        ValidateProfileValues(request.Values, latestTemplateVersion.Tokens);

        // Generate version number
        var version = await GenerateVersionAsync(request.MiniId, cancellationToken);

        // Create the profile document
        var profile = new MiniProfile
        {
            Id = $"{request.MiniId}_{version}",
            Version = version,
            MiniId = request.MiniId.ToString(),
            TemplateId = mini.TemplateId.Value,
            Values = request.Values.Select(v => new ProfileValue
            {
                Name = v.Name,
                Value = v.Value
            }).ToList(),
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUser.Id ?? "system"
        };

        // Save using the client
        await _miniProfilesClient.CreateMiniProfileAsync(profile, cancellationToken);

        return version;
    }

    private void ValidateProfileValues(List<CreateProfileValueDto> profileValues, List<TemplateToken> templateTokens)
    {
        // Get all template token names
        var templateTokenNames = templateTokens.Select(t => t.Name).ToHashSet();
        var profileValueNames = profileValues.Select(v => v.Name).ToHashSet();

        // Check for missing required tokens
        var missingTokens = templateTokenNames.Except(profileValueNames).ToList();
        if (missingTokens.Any())
        {
            throw new InvalidOperationException($"Profile must provide values for all template tokens: {string.Join(", ", missingTokens.Select(t => $"{{{{{t}}}}}"))}");
        }

        // Check for extra tokens not in template
        var extraTokens = profileValueNames.Except(templateTokenNames).ToList();
        if (extraTokens.Any())
        {
            throw new InvalidOperationException($"Profile contains unknown tokens: {string.Join(", ", extraTokens.Select(t => $"{{{{{t}}}}}"))}");
        }

        // Validate no duplicate token names in profile
        var duplicateTokens = profileValues
            .GroupBy(v => v.Name)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        if (duplicateTokens.Any())
        {
            throw new InvalidOperationException($"Profile contains duplicate tokens: {string.Join(", ", duplicateTokens)}");
        }
    }
    
    private async Task<string> GenerateVersionAsync(int miniId, CancellationToken cancellationToken)
    {
        var today = DateTime.UtcNow.ToString("yyyy-MM-dd");

        // Get existing versions for the template
        var latestVersion = await _miniProfilesClient.GetLatestMiniProfileAsync(miniId, cancellationToken);

        // Find the highest sequence number for today
        int latestSequence = 0;
        if (latestVersion is not null)
        {
            if (latestVersion.Version.StartsWith(today)) // YYYY-MM-DD-NNN format
            {
                var sequencePart = latestVersion.Version.Substring(11); // Get NNN part
                if (int.TryParse(sequencePart, out var sequence))
                {
                    latestSequence = sequence;
                    // Generate next version
                    var nextSequence = latestSequence + 1;
                    return $"{today}-{nextSequence:D3}";
                }
            }
        }
        return $"{today}-{latestSequence:D3}";
    }
}
