using Minikai.Application.Common.Interfaces;
using Minikai.Application.MiniProfiles.Common.Models;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniProfiles.Queries.GetLatestMiniProfile;

public record GetLatestMiniProfileQuery : IRequest<MiniProfileDto?>
{
    public required int MiniId { get; init; }
}

public class GetLatestMiniProfileQueryValidator : AbstractValidator<GetLatestMiniProfileQuery>
{
    public GetLatestMiniProfileQueryValidator()
    {
        RuleFor(v => v.MiniId)
            .NotEmpty();
    }
}

public class GetLatestMiniProfileQueryHandler : IRequestHandler<GetLatestMiniProfileQuery, MiniProfileDto?>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniProfilesClient _miniProfilesClient;
    private readonly ICurrentUser _currentUser;
    private readonly IMapper _mapper;

    public GetLatestMiniProfileQueryHandler(
        IApplicationDbContext context,
        IMiniProfilesClient miniProfilesClient,
        ICurrentUser currentUser,
        IMapper mapper)
    {
        _context = context;
        _miniProfilesClient = miniProfilesClient;
        _currentUser = currentUser;
        _mapper = mapper;
    }

    public async Task<MiniProfileDto?> Handle(GetLatestMiniProfileQuery request, CancellationToken cancellationToken)
    {
        // Verify mini exists and user has access
        var mini = await _context.Minis
            .Include(m => m.MiniToGroups)
            .ThenInclude(mg => mg.Group)
            .FirstOrDefaultAsync(m => m.Id == request.MiniId, cancellationToken);

        Guard.Against.NotFound(request.MiniId, mini);

        // Check authorization - admins or mini owners can access profiles
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = mini.MiniToGroups
                .Any(mg => _currentUser.Groups.Contains(mg.Group.GroupId));
            
            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Get latest profile
        var latestProfile = await _miniProfilesClient.GetLatestMiniProfileAsync(request.MiniId, cancellationToken);

        return latestProfile != null ? _mapper.Map<MiniProfileDto>(latestProfile) : null;
    }
}
