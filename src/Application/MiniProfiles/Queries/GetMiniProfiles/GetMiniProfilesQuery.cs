using Minikai.Application.Common.Interfaces;
using Minikai.Application.Common.Models;
using Minikai.Application.MiniProfiles.Common.Models;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniProfiles.Queries.GetMiniProfiles;

public record GetMiniProfilesQuery : IRequest<PaginatedList<MiniProfileDto>>
{
    public required int MiniId { get; init; }
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 5;
}

public class GetMiniProfilesQueryValidator : AbstractValidator<GetMiniProfilesQuery>
{
    public GetMiniProfilesQueryValidator()
    {
        RuleFor(v => v.MiniId)
            .NotEmpty();
    }
}

public class GetMiniProfilesQueryHandler : IRequestHandler<GetMiniProfilesQuery, PaginatedList<MiniProfileDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniProfilesClient _miniProfilesClient;
    private readonly ICurrentUser _currentUser;
    private readonly IMapper _mapper;

    public GetMiniProfilesQueryHandler(
        IApplicationDbContext context,
        IMiniProfilesClient miniProfilesClient,
        ICurrentUser currentUser,
        IMapper mapper)
    {
        _context = context;
        _miniProfilesClient = miniProfilesClient;
        _currentUser = currentUser;
        _mapper = mapper;
    }

    public async Task<PaginatedList<MiniProfileDto>> Handle(GetMiniProfilesQuery request, CancellationToken cancellationToken)
    {
        // Verify mini exists and user has access
        var mini = await _context.Minis
            .Include(m => m.MiniToGroups)
            .ThenInclude(mg => mg.Group)
            .FirstOrDefaultAsync(m => m.Id == request.MiniId, cancellationToken);

        Guard.Against.NotFound(request.MiniId, mini);

        // Check authorization - only admins can access all profiles
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = mini.MiniToGroups
                .Any(mg => _currentUser.Groups.Contains(mg.Group.GroupId));
            
            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Get paginated profiles
        var (profiles, totalCount) = await _miniProfilesClient.GetMiniProfilesAsync(
            request.MiniId, 
            request.PageNumber, 
            request.PageSize, 
            cancellationToken);

        var mappedProfiles = _mapper.Map<List<MiniProfileDto>>(profiles);
        
        return new PaginatedList<MiniProfileDto>(mappedProfiles, totalCount, request.PageNumber, request.PageSize);
    }
}
