using Minikai.Application.Common.Interfaces;
using Minikai.Domain.Entities;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniTemplateVersions.Commands.CreateMiniTemplateVersion;

public record CreateMiniTemplateVersionCommand : IRequest<string>
{
    public required Guid TemplateId { get; init; }
    public required string System { get; init; }
    public required List<CreateTemplateTokenDto> Tokens { get; init; } = new();
}

public class CreateTemplateTokenDto
{
    public required string Name { get; init; }
    public required string Type { get; init; }
    public string? Description { get; init; }
}

public class CreateMiniTemplateVersionCommandValidator : AbstractValidator<CreateMiniTemplateVersionCommand>
{
    public CreateMiniTemplateVersionCommandValidator()
    {
        RuleFor(v => v.TemplateId)
            .NotEmpty();

        RuleFor(v => v.System)
            .NotEmpty()
            .MaximumLength(10000);

        RuleFor(v => v.Tokens)
            .NotNull();

        RuleForEach(v => v.Tokens).ChildRules(token =>
        {
            token.RuleFor(t => t.Name)
                .NotEmpty()
                .MaximumLength(100);

            token.RuleFor(t => t.Type)
                .NotEmpty()
                .Must(BeValidTokenType)
                .WithMessage("Type must be one of: string, number, integer, boolean, array, object");

            token.RuleFor(t => t.Description)
                .MaximumLength(500);
        });
    }

    private static bool BeValidTokenType(string type)
    {
        return type is TokenTypes.String or TokenTypes.Number or TokenTypes.Integer 
                    or TokenTypes.Boolean or TokenTypes.Array or TokenTypes.Object;
    }
}

public class CreateMiniTemplateVersionCommandHandler : IRequestHandler<CreateMiniTemplateVersionCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniTemplateVersionsClient _miniTemplateVersionsClient;
    private readonly ICurrentUser _currentUser;

    public CreateMiniTemplateVersionCommandHandler(
        IApplicationDbContext context,
        IMiniTemplateVersionsClient miniTemplateVersionsClient,
        ICurrentUser currentUser)
    {
        _context = context;
        _miniTemplateVersionsClient = miniTemplateVersionsClient;
        _currentUser = currentUser;
    }

    public async Task<string> Handle(CreateMiniTemplateVersionCommand request, CancellationToken cancellationToken)
    {
        // Verify template exists and user has access
        var template = await _context.MiniTemplates
            .Include(t => t.MiniTemplateToGroups)
            .ThenInclude(tg => tg.Group)
            .FirstOrDefaultAsync(t => t.Id == request.TemplateId, cancellationToken);

        Guard.Against.NotFound(request.TemplateId, template);

        // Check authorization
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = template.MiniTemplateToGroups
                .Any(tg => _currentUser.Groups.Contains(tg.Group.GroupId));
            
            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Generate version number
        var version = await GenerateVersionAsync(request.TemplateId, cancellationToken);

        // Determine tokens to use - inherit from latest version if none provided
        List<TemplateToken> tokensToUse;
        if (request.Tokens.Any())
        {
            // Use provided tokens
            tokensToUse = request.Tokens.Select(t => new TemplateToken
            {
                Name = t.Name,
                Type = t.Type,
                Description = t.Description
            }).ToList();
        }
        else
        {
            // Inherit tokens from latest version
            var latestVersion = await _miniTemplateVersionsClient.GetLatestMiniTemplateVersionAsync(request.TemplateId, cancellationToken);
            tokensToUse = latestVersion?.Tokens?.ToList() ?? new List<TemplateToken>();
        }

        // Create the version document
        var templateVersion = new MiniTemplateVersion
        {
            Id = $"{request.TemplateId}_{version}",
            TemplateId = request.TemplateId,
            Version = version,
            System = request.System,
            Tokens = tokensToUse,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUser.Id ?? "system"
        };

        // Save using the client
        await _miniTemplateVersionsClient.CreateMiniTemplateVersionAsync(templateVersion, cancellationToken);

        return version;
    }

    private async Task<string> GenerateVersionAsync(Guid templateId, CancellationToken cancellationToken)
    {
        var today = DateTime.UtcNow.ToString("yyyy-MM-dd");

        // Get existing versions for the template
        var latestVersion = await _miniTemplateVersionsClient.GetLatestMiniTemplateVersionAsync(templateId, cancellationToken);

        // Find the highest sequence number for today
        int nextSequence = 1; // Start with 1 for the first version
        if (latestVersion is not null)
        {
            if (latestVersion.Version.StartsWith(today)) // YYYY-MM-DD-NNN format
            {
                var sequencePart = latestVersion.Version.Substring(11); // Get NNN part
                if (int.TryParse(sequencePart, out var sequence))
                {
                    nextSequence = sequence + 1;
                }
            }
        }
        return $"{today}-{nextSequence:D3}";
    }
}
