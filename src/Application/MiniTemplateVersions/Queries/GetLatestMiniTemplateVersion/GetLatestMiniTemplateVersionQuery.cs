using Minikai.Application.Common.Interfaces;
using Minikai.Application.MiniTemplateVersions.Common.Models;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniTemplateVersions.Queries.GetLatestMiniTemplateVersion;

public record GetLatestMiniTemplateVersionQuery : IRequest<MiniTemplateVersionDto?>
{
    public required Guid TemplateId { get; init; }
}

public class GetLatestMiniTemplateVersionQueryValidator : AbstractValidator<GetLatestMiniTemplateVersionQuery>
{
    public GetLatestMiniTemplateVersionQueryValidator()
    {
        RuleFor(v => v.TemplateId)
            .NotEmpty();
    }
}

public class GetLatestMiniTemplateVersionQueryHandler : IRequestHandler<GetLatestMiniTemplateVersionQuery, MiniTemplateVersionDto?>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniTemplateVersionsClient _miniTemplateVersionsClient;
    private readonly ICurrentUser _currentUser;
    private readonly IMapper _mapper;

    public GetLatestMiniTemplateVersionQueryHandler(
        IApplicationDbContext context,
        IMiniTemplateVersionsClient miniTemplateVersionsClient,
        ICurrentUser currentUser,
        IMapper mapper)
    {
        _context = context;
        _miniTemplateVersionsClient = miniTemplateVersionsClient;
        _currentUser = currentUser;
        _mapper = mapper;
    }

    public async Task<MiniTemplateVersionDto?> Handle(GetLatestMiniTemplateVersionQuery request, CancellationToken cancellationToken)
    {
        // Verify template exists and user has access
        var template = await _context.MiniTemplates
            .Include(t => t.MiniTemplateToGroups)
            .ThenInclude(tg => tg.Group)
            .FirstOrDefaultAsync(t => t.Id == request.TemplateId, cancellationToken);

        Guard.Against.NotFound(request.TemplateId, template);

        // Check authorization
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = template.MiniTemplateToGroups
                .Any(tg => _currentUser.Groups.Contains(tg.Group.GroupId));
            
            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Get latest version
        var latestVersion = await _miniTemplateVersionsClient.GetLatestMiniTemplateVersionAsync(request.TemplateId, cancellationToken);

        return latestVersion != null ? _mapper.Map<MiniTemplateVersionDto>(latestVersion) : null;
    }
}
