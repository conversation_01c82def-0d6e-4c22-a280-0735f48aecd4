using Minikai.Application.Common.Interfaces;
using Minikai.Application.MiniTemplateVersions.Common.Models;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniTemplateVersions.Queries.GetMiniTemplateVersion;

public record GetMiniTemplateVersionQuery : IRequest<MiniTemplateVersionDto>
{
    public required Guid TemplateId { get; init; }
    public required string Version { get; init; }
}

public class GetMiniTemplateVersionQueryValidator : AbstractValidator<GetMiniTemplateVersionQuery>
{
    public GetMiniTemplateVersionQueryValidator()
    {
        RuleFor(v => v.TemplateId)
            .NotEmpty();

        RuleFor(v => v.Version)
            .NotEmpty();
    }
}

public class GetMiniTemplateVersionQueryHandler : IRequestHandler<GetMiniTemplateVersionQuery, MiniTemplateVersionDto>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniTemplateVersionsClient _miniTemplateVersionsClient;
    private readonly ICurrentUser _currentUser;
    private readonly IMapper _mapper;

    public GetMiniTemplateVersionQueryHandler(
        IApplicationDbContext context,
        IMiniTemplateVersionsClient miniTemplateVersionsClient,
        ICurrentUser currentUser,
        IMapper mapper)
    {
        _context = context;
        _miniTemplateVersionsClient = miniTemplateVersionsClient;
        _currentUser = currentUser;
        _mapper = mapper;
    }

    public async Task<MiniTemplateVersionDto> Handle(GetMiniTemplateVersionQuery request, CancellationToken cancellationToken)
    {
        // Verify template exists and user has access
        var template = await _context.MiniTemplates
            .Include(t => t.MiniTemplateToGroups)
            .ThenInclude(tg => tg.Group)
            .FirstOrDefaultAsync(t => t.Id == request.TemplateId, cancellationToken);

        Guard.Against.NotFound(request.TemplateId, template);

        // Check authorization
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = template.MiniTemplateToGroups
                .Any(tg => _currentUser.Groups.Contains(tg.Group.GroupId));
            
            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Get specific version
        var version = await _miniTemplateVersionsClient.GetMiniTemplateVersionAsync(request.TemplateId, request.Version, cancellationToken);

        return _mapper.Map<MiniTemplateVersionDto>(version);
    }
}
