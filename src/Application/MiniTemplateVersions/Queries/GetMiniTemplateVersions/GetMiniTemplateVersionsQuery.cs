using Minikai.Application.Common.Interfaces;
using Minikai.Application.Common.Models;
using Minikai.Application.MiniTemplateVersions.Common.Models;
using Minikai.Domain.Exceptions;

namespace Minikai.Application.MiniTemplateVersions.Queries.GetMiniTemplateVersions;

public record GetMiniTemplateVersionsQuery : IRequest<PaginatedList<MiniTemplateVersionDto>>
{
    public required Guid TemplateId { get; init; }
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 5;
}

public class GetMiniTemplateVersionsQueryValidator : AbstractValidator<GetMiniTemplateVersionsQuery>
{
    public GetMiniTemplateVersionsQueryValidator()
    {
        RuleFor(v => v.TemplateId)
            .NotEmpty();
    }
}

public class GetMiniTemplateVersionsQueryHandler : IRequestHandler<GetMiniTemplateVersionsQuery, PaginatedList<MiniTemplateVersionDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly IMiniTemplateVersionsClient _miniTemplateVersionsClient;
    private readonly ICurrentUser _currentUser;
    private readonly IMapper _mapper;

    public GetMiniTemplateVersionsQueryHandler(
        IApplicationDbContext context,
        IMiniTemplateVersionsClient miniTemplateVersionsClient,
        ICurrentUser currentUser,
        IMapper mapper)
    {
        _context = context;
        _miniTemplateVersionsClient = miniTemplateVersionsClient;
        _currentUser = currentUser;
        _mapper = mapper;
    }

    public async Task<PaginatedList<MiniTemplateVersionDto>> Handle(GetMiniTemplateVersionsQuery request, CancellationToken cancellationToken)
    {
        // Verify template exists and user has access
        var template = await _context.MiniTemplates
            .Include(t => t.MiniTemplateToGroups)
            .ThenInclude(tg => tg.Group)
            .FirstOrDefaultAsync(t => t.Id == request.TemplateId, cancellationToken);

        Guard.Against.NotFound(request.TemplateId, template);

        // Check authorization
        var isAdmin = _currentUser.Groups.Contains(Minikai.Domain.Constants.Groups.AdminGroup);
        if (!isAdmin)
        {
            var hasAccess = template.MiniTemplateToGroups
                .Any(tg => _currentUser.Groups.Contains(tg.Group.GroupId));
            
            if (!hasAccess)
            {
                throw new ForbiddenAccessException();
            }
        }

        // Get paginated versions
        var (versions, totalCount) = await _miniTemplateVersionsClient.GetMiniTemplateVersionsAsync(
            request.TemplateId, 
            request.PageNumber, 
            request.PageSize, 
            cancellationToken);

        var mappedVersions = _mapper.Map<List<MiniTemplateVersionDto>>(versions);
        
        return new PaginatedList<MiniTemplateVersionDto>(mappedVersions, totalCount, request.PageNumber, request.PageSize);
    }
}
