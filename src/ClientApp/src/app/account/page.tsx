'use client'

import { CreateMiniGroupModal } from '@/components/account/CreateMiniGroupModal'
import { CreateUserGroupModal } from '@/components/account/CreateUserGroupModal'
import { InviteUserModal } from '@/components/account/InviteUserModal'
import { MiniGroupsList } from '@/components/account/MiniGroupsList'
import { TabButton } from '@/components/account/TabButton'
import { UserGroupsList } from '@/components/account/UserGroupsList'
import { UsersList } from '@/components/account/UsersList'
import { ThemedContent } from '@/components/ThemedContent'
import { Button } from '@/components/ui/Button/Button'
import { Header } from '@/components/ui/Header'
import { Icon } from '@/components/ui/Icon'
import { SearchInput } from '@/components/ui/SearchInput/SearchInput'
import { useGroup } from '@/hooks/useGroup'
import { useGroupUsers } from '@/hooks/useGroupUsers'
import { useMiniGroups } from '@/hooks/useMiniGroups'
import { useUserGroupPermissions } from '@/hooks/useUserGroupPermissions'
import { useUserGroups } from '@/hooks/useUserGroups'
import { Plus, UserPlus } from 'lucide-react'
import { useState } from 'react'

export default function AccountPage() {
  const { group, loading: groupLoading } = useGroup()
  const { canManageUserGroup } = useUserGroupPermissions(group?.groupId)
  const { users } = useGroupUsers(group?.groupId)
  const { userGroups, mutate: refreshUserGroups } = useUserGroups(group?.groupId)
  const { miniGroups, mutate: refreshMiniGroups } = useMiniGroups(group?.groupId)
  const [activeTab, setActiveTab] = useState<'users' | 'User Groups' | 'Mini Groups'>(
    canManageUserGroup ? 'users' : 'User Groups',
  )
  const [searchQuery, setSearchQuery] = useState('')
  const [isCreateUserGroupModalOpen, setIsCreateUserGroupModalOpen] = useState(false)
  const [isCreateMiniGroupModalOpen, setIsCreateMiniGroupModalOpen] = useState(false)
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false)

  const handleUserGroupCreated = () => {
    refreshUserGroups()
  }

  const handleMiniGroupCreated = () => {
    refreshMiniGroups()
  }

  return (
    <ThemedContent>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Header level={1}>{groupLoading ? 'Loading...' : group?.name || 'No userGroup'}</Header>
        </div>

        <div>
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {canManageUserGroup && (
                <TabButton
                  active={activeTab === 'users'}
                  onClick={() => setActiveTab('users')}
                  label="Users"
                  count={users?.length || 0}
                />
              )}
              <TabButton
                active={activeTab === 'User Groups'}
                onClick={() => setActiveTab('User Groups')}
                label="User Groups"
                count={userGroups?.length || 0}
              />
              <TabButton
                active={activeTab === 'Mini Groups'}
                onClick={() => setActiveTab('Mini Groups')}
                label="Mini Groups"
                count={miniGroups?.length || 0}
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="w-64">
                <SearchInput value={searchQuery} onChange={setSearchQuery} placeholder={`Search ${activeTab}...`} />
              </div>
              {activeTab === 'users' ? (
                <Button variant="primary" buttonSize="medium" onClick={() => setIsInviteModalOpen(true)}>
                  Invite User
                  <Icon icon={UserPlus} className="w-4 h-4 ml-2 opacity-80" />
                </Button>
              ) : activeTab === 'User Groups' && canManageUserGroup ? (
                <Button variant="primary" buttonSize="medium" onClick={() => setIsCreateUserGroupModalOpen(true)}>
                  Create User Group
                  <Icon icon={Plus} className="w-4 h-4 ml-2 opacity-80" />
                </Button>
              ) : activeTab === 'Mini Groups' && canManageUserGroup ? (
                <Button variant="primary" buttonSize="medium" onClick={() => setIsCreateMiniGroupModalOpen(true)}>
                  Create Mini Group
                  <Icon icon={Plus} className="w-4 h-4 ml-2 opacity-80" />
                </Button>
              ) : (
                <div />
              )}
            </div>
          </div>

          {activeTab === 'users' ? (
            <UsersList groupId={group?.groupId} searchQuery={searchQuery} />
          ) : activeTab === 'User Groups' ? (
            <UserGroupsList searchQuery={searchQuery} />
          ) : (
            <MiniGroupsList searchQuery={searchQuery} />
          )}

          <CreateUserGroupModal
            isOpen={isCreateUserGroupModalOpen}
            onClose={() => setIsCreateUserGroupModalOpen(false)}
            groupId={group?.groupId || ''}
            onUserGroupCreated={handleUserGroupCreated}
          />

          <CreateMiniGroupModal
            isOpen={isCreateMiniGroupModalOpen}
            onClose={() => setIsCreateMiniGroupModalOpen(false)}
            groupId={group?.groupId || ''}
            onMiniGroupCreated={handleMiniGroupCreated}
          />

          <InviteUserModal
            isOpen={isInviteModalOpen}
            onClose={() => setIsInviteModalOpen(false)}
            groupId={group?.groupId || ''}
          />
        </div>
      </div>
    </ThemedContent>
  )
}
