'use client'

import { GroupManager } from '@/components/GroupManager'
import { MiniManager } from '@/components/MiniManager'
import { MiniTemplateManager } from '@/components/MiniTemplateManager'
import { ToolManager } from '@/components/ToolManager'
import { Icon } from '@/components/ui/Icon'
import { hasRequiredRole } from '@/lib/auth/utils'
import { Bot, FileText, Users, Wrench } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

interface TabProps {
  label: string
  isActive: boolean
  onClick: () => void
  icon: React.ReactNode
}

const Tab = ({ label, isActive, onClick, icon }: TabProps) => (
  <button
    onClick={onClick}
    className={`group relative flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all duration-200 ${
      isActive
        ? 'text-blue-600 dark:text-blue-400'
        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
    }`}
  >
    <div
      className={`p-1 rounded transition-colors duration-200 ${
        isActive
          ? 'bg-blue-100 dark:bg-blue-900/20'
          : 'bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700'
      }`}
    >
      {icon}
    </div>
    <span>{label}</span>
    {isActive && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400" />}
  </button>
)

export default function AdminConsole() {
  const { data: session } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('templates')

  useEffect(() => {
    if (!hasRequiredRole(session?.user?.roles, ['SuperAdmin'])) {
      router.push('/chat')
    }
  }, [session, router])

  if (!hasRequiredRole(session?.user?.roles, ['SuperAdmin'])) {
    return null
  }

  return (
    <div className="container mx-auto px-4">
      <div className="flex items-center justify-between py-4 border-b border-gray-100 dark:border-gray-800">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Admin Console</h1>
        <div className="flex items-center space-x-2">
          <Tab
            label="Mini Templates"
            isActive={activeTab === 'templates'}
            onClick={() => setActiveTab('templates')}
            icon={<Icon icon={FileText} className="w-4 h-4" />}
          />
          <Tab
            label="Minis"
            isActive={activeTab === 'minis'}
            onClick={() => setActiveTab('minis')}
            icon={<Icon icon={Bot} className="w-4 h-4" />}
          />
          <Tab
            label="Tools"
            isActive={activeTab === 'tools'}
            onClick={() => setActiveTab('tools')}
            icon={<Icon icon={Wrench} className="w-4 h-4" />}
          />
          <Tab
            label="Groups"
            isActive={activeTab === 'groups'}
            onClick={() => setActiveTab('groups')}
            icon={<Icon icon={Users} className="w-4 h-4" />}
          />
        </div>
      </div>

      <div className="py-4">
        {activeTab === 'templates' && <MiniTemplateManager />}

        {activeTab === 'minis' && <MiniManager />}

        {activeTab === 'tools' && <ToolManager />}

        {activeTab === 'groups' && <GroupManager />}
      </div>
    </div>
  )
}
