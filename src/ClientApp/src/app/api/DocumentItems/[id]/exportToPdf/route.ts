import { NextRequest } from 'next/server'

export async function POST(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params
  try {
    const { content } = await request.json()

    const response = await fetch(`${process.env.API_BASE_URL}/api/DocumentItems/${params.id}/exportToPdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        documentId: params.id,
        content,
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to export PDF')
    }

    const pdfData = await response.arrayBuffer()

    return new Response(pdfData, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="document.pdf"',
      },
    })
  } catch (error) {
    console.error('Error exporting PDF:', error)
    return new Response('Error exporting PDF', { status: 500 })
  }
}
