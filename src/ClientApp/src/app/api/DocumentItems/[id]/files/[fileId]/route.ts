import { DocumentItemsClient } from '@/web-api-client'
import { NextRequest } from 'next/server'

export async function DELETE(request: NextRequest, props: { params: Promise<{ id: string; fileId: string }> }) {
  const params = await props.params
  try {
    const { id, fileId } = params

    const client = new DocumentItemsClient()
    await client.removeFile(id, fileId)

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Error removing file:', error)
    return new Response('Error removing file', { status: 500 })
  }
}
