import { DocumentItemsClient } from '@/web-api-client'
import { NextRequest } from 'next/server'

export async function POST(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params
  try {
    const documentId = params.id
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const groupId = request.nextUrl.searchParams.get('groupId')
    const miniId = parseInt((formData.get('miniId') as string) || '0', 10)

    if (!files.length) {
      return new Response('No files provided', { status: 400 })
    }

    if (!groupId) {
      return new Response('Group ID is required', { status: 400 })
    }

    if (!miniId) {
      return new Response('Mini ID is required', { status: 400 })
    }

    const client = new DocumentItemsClient()
    await client.addFiles(documentId, groupId, files)

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('Error uploading files:', error)
    return new Response('Error uploading files', { status: 500 })
  }
}
