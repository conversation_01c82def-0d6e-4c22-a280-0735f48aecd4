import { getChatConfiguration } from '@/features/ai/services/chatCore.service'
import { processStreamingChat } from '@/features/ai/services/chatStream.service'
import { trackException } from '@/lib/analytics/appinsights-server'
import { UIMessage } from 'ai'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Custom Zod schema for UIMessage
const messageSchema = z.custom<UIMessage>(data => {
  try {
    const obj = data as any
    return typeof obj === 'object' && obj !== null && typeof obj.role === 'string' && typeof obj.content === 'string'
  } catch {
    return false
  }
})

const chatRequestSchema = z.object({
  messages: z.array(messageSchema),
  chatId: z.string(),
  miniId: z.string(),
  timezone: z.string().optional(),
})

export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validationResult = chatRequestSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request format',
          details: validationResult.error.format(),
        },
        { status: 400 },
      )
    }

    const { messages, chatId, miniId, timezone } = validationResult.data

    const chatConfiguration = await getChatConfiguration(miniId, timezone)

    return await processStreamingChat(chatConfiguration, messages, chatId, miniId)
  } catch (error) {
    trackException(error as Error, { route: 'api/chat' })
    return NextResponse.json({ error: 'Server error' }, { status: 500 })
  }
}
