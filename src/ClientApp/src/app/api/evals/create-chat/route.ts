import { createEvalChat } from '@/features/ai/services/evalsStream.service'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

/**
 * @swagger
 * /evals/create-chat:
 *   post:
 *     summary: Create Chat for Evaluation
 *     description: Create a new chat session for evaluation purposes
 *     operationId: createEvalChat
 *     tags:
 *       - Evals
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       description: Chat creation request
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateChatRequest'
 *     responses:
 *       200:
 *         description: Chat created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CreateChatResponse'
 *       400:
 *         description: Bad Request - Invalid request format or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *
 * components:
 *   schemas:
 *     CreateChatRequest:
 *       type: object
 *       required: [miniId, firstMessageContent]
 *       properties:
 *         miniId:
 *           type: integer
 *           description: Unique identifier for the Mini (AI assistant configuration)
 *           example: 123
 *         firstMessageContent:
 *           type: string
 *           description: Content of the first message to start the chat
 *           example: Hello, I need help with my evaluation task
 *           minLength: 1
 *     CreateChatResponse:
 *       type: object
 *       properties:
 *         chatId:
 *           type: string
 *           description: Unique identifier for the created chat session (GUID format)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         success:
 *           type: boolean
 *           description: Whether the chat was created successfully
 *           example: true
 *         error:
 *           type: string
 *           description: Error message if creation failed
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Error message describing what went wrong
 *         details:
 *           type: object
 *           description: Additional error details (for validation errors)
 *         success:
 *           type: boolean
 *           description: Always false for error responses
 *           example: false
 *         message:
 *           type: string
 *           description: Human-readable error message
 */
// Schema for creating a new chat (used by /api/evals/create-chat)
const createChatRequestSchema = z.object({
  miniId: z.number(),
  firstMessageContent: z.string().min(1),
})

const createChatHandler = async (request: NextRequest) => {
  const body = await request.json()
  const validationResult = createChatRequestSchema.safeParse(body)

  if (!validationResult.success) {
    return NextResponse.json(
      {
        success: false,
        error: 'Invalid request format',
        details: validationResult.error.format(),
      },
      { status: 400 },
    )
  }

  const result = await createEvalChat(validationResult.data)

  if (result.success) {
    return NextResponse.json(result, { status: 200 })
  } else {
    return NextResponse.json(result, { status: 400 })
  }
}

export const POST = createChatHandler
