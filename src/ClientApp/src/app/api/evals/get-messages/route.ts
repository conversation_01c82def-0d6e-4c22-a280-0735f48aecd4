import { getEvalMessages } from '@/features/ai/services/evalsStream.service'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

/**
 * @swagger
 * /evals/get-messages:
 *   get:
 *     summary: Get All Chat Messages for Evaluation
 *     description: Retrieve all messages for a chat session including system messages for evaluation purposes
 *     operationId: getEvalMessages
 *     tags:
 *       - Evals
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: chatId
 *         in: query
 *         required: true
 *         description: Unique identifier for the chat session (GUID format)
 *         schema:
 *           type: string
 *           example: "550e8400-e29b-41d4-a716-************"
 *     responses:
 *       200:
 *         description: All messages in the chat session
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GetMessagesResponse'
 *       400:
 *         description: Bad Request - Missing or invalid chat ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *
 * components:
 *   schemas:
 *     GetMessagesResponse:
 *       type: object
 *       properties:
 *         messages:
 *           type: array
 *           description: Array of all messages in the chat including system messages
 *           items:
 *             $ref: '#/components/schemas/UIMessage'
 *         success:
 *           type: boolean
 *           description: Whether the messages were retrieved successfully
 *           example: true
 *         error:
 *           type: string
 *           description: Error message if retrieval failed
 *     UIMessage:
 *       type: object
 *       required: [role, content]
 *       properties:
 *         role:
 *           type: string
 *           enum: [user, assistant, system]
 *           description: The role of the message sender
 *         content:
 *           type: string
 *           description: The text content of the message
 *           example: Hello, can you help me with my query?
 *         id:
 *           type: string
 *           description: Optional unique identifier for the message
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Optional timestamp when the message was created
 *         parts:
 *           type: array
 *           description: Optional array of message parts for complex messages
 *           items:
 *             type: object
 *             description: Message part (text, tool invocation, etc.)
 *         annotations:
 *           type: array
 *           description: Optional annotations for the message
 *           items:
 *             type: object
 *             description: Message annotation
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Error message describing what went wrong
 *         details:
 *           type: object
 *           description: Additional error details (for validation errors)
 *         success:
 *           type: boolean
 *           description: Always false for error responses
 *           example: false
 *         message:
 *           type: string
 *           description: Human-readable error message
 */
const getMessagesRequestSchema = z.object({
  chatId: z.string(),
})

const getMessagesHandler = async (request: NextRequest) => {
  const url = new URL(request.url)
  const chatId = url.searchParams.get('chatId')

  const validationResult = getMessagesRequestSchema.safeParse({ chatId })

  if (!validationResult.success) {
    return NextResponse.json(
      {
        messages: [],
        success: false,
        error: 'Invalid request format',
        details: validationResult.error.format(),
      },
      { status: 400 },
    )
  }

  const result = await getEvalMessages(validationResult.data.chatId)

  if (result.success) {
    return NextResponse.json(result, { status: 200 })
  } else {
    return NextResponse.json(result, { status: 400 })
  }
}

export const GET = getMessagesHandler
