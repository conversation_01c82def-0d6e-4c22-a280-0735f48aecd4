import { getChatConfiguration } from '@/features/ai/services/chatCore.service'
import { processNonStreamingChat } from '@/features/ai/services/evalsStream.service'
import { UIMessage } from 'ai'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

/**
 * @swagger
 * /evals/send-message:
 *   post:
 *     summary: Send Message for Evaluation
 *     description: Send a message to AI chat and receive complete response (non-streaming) with token usage for evaluation
 *     operationId: sendEvalMessage
 *     tags:
 *       - Evals
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       description: Chat request containing messages and context
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChatRequest'
 *     responses:
 *       200:
 *         description: Complete chat response with token usage
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SendMessageResponse'
 *       400:
 *         description: Bad Request - Invalid request format or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *
 * components:
 *   schemas:
 *     ChatRequest:
 *       type: object
 *       required: [messages, chatId, miniId]
 *       properties:
 *         messages:
 *           type: array
 *           description: Array of chat messages in the conversation
 *           items:
 *             $ref: '#/components/schemas/UIMessage'
 *           minItems: 1
 *         chatId:
 *           type: string
 *           description: Unique identifier for the chat session (GUID format)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         miniId:
 *           type: string
 *           description: Unique identifier for the Mini (AI assistant configuration)
 *           example: "123"
 *         timezone:
 *           type: string
 *           description: Optional timezone for the user
 *           example: Australia/Sydney
 *     UIMessage:
 *       type: object
 *       required: [role, content]
 *       properties:
 *         role:
 *           type: string
 *           enum: [user, assistant, system]
 *           description: The role of the message sender
 *         content:
 *           type: string
 *           description: The text content of the message
 *           example: Hello, can you help me with my query?
 *         id:
 *           type: string
 *           description: Optional unique identifier for the message
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Optional timestamp when the message was created
 *         parts:
 *           type: array
 *           description: Optional array of message parts for complex messages
 *           items:
 *             type: object
 *             description: Message part (text, tool invocation, etc.)
 *         annotations:
 *           type: array
 *           description: Optional annotations for the message
 *           items:
 *             type: object
 *             description: Message annotation
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Error message describing what went wrong
 *         details:
 *           type: object
 *           description: Additional error details (for validation errors)
 *         success:
 *           type: boolean
 *           description: Always false for error responses
 *           example: false
 *         message:
 *           type: string
 *           description: Human-readable error message
 *     SendMessageResponse:
 *       type: object
 *       properties:
 *         message:
 *           $ref: '#/components/schemas/UIMessage'
 *         success:
 *           type: boolean
 *           description: Whether the message was processed successfully
 *           example: true
 *         usage:
 *           $ref: '#/components/schemas/TokenUsage'
 *         error:
 *           type: string
 *           description: Error message if processing failed
 *     TokenUsage:
 *       type: object
 *       properties:
 *         promptTokens:
 *           type: integer
 *           description: Number of tokens used in the prompt
 *           example: 150
 *         completionTokens:
 *           type: integer
 *           description: Number of tokens used in the completion
 *           example: 75
 *         totalTokens:
 *           type: integer
 *           description: Total number of tokens used
 *           example: 225
 */
// Custom Zod schema for UIMessage
const messageSchema = z.custom<UIMessage>(data => {
  try {
    const obj = data as any
    return typeof obj === 'object' && obj !== null && typeof obj.role === 'string' && typeof obj.content === 'string'
  } catch {
    return false
  }
})

// Schema for chat requests (used by /api/chat and /api/evals/send-message)
const chatRequestSchema = z.object({
  messages: z.array(messageSchema),
  chatId: z.string(),
  miniId: z.number(),
  timezone: z.string().optional(),
})

const sendMessageHandler = async (request: NextRequest) => {
  // Parse and validate request body
  const body = await request.json()
  const validationResult = chatRequestSchema.safeParse(body)

  if (!validationResult.success) {
    return NextResponse.json(
      {
        success: false,
        error: 'Invalid request format',
        details: validationResult.error.format(),
      },
      { status: 400 },
    )
  }

  const { messages, chatId, miniId, timezone } = validationResult.data

  const config = await getChatConfiguration(miniId.toString(), timezone)
  const result = await processNonStreamingChat(config, messages, chatId, miniId)

  if (result.success) {
    return NextResponse.json(result, { status: 200 })
  } else {
    return NextResponse.json(result, { status: 400 })
  }
}

export const POST = sendMessageHandler
