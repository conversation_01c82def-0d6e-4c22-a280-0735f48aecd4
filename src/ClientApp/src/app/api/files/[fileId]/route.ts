export async function GET(request: Request, props: { params: Promise<{ fileId: string }> }) {
  const params = await props.params
  try {
    // Make a direct fetch to the backend API
    const response = await fetch(`${process.env.API_BASE_URL}/api/Files/${params.fileId}`, {
      method: 'GET',
      // Forward any authorization headers from the original request
      headers: {
        Authorization: request.headers.get('Authorization') || '',
      },
    })

    if (!response.ok) {
      throw new Error('Failed to download file')
    }

    // Get the content type and filename from the headers if available
    const contentType = response.headers.get('content-type') || 'application/octet-stream'
    const contentDisposition = response.headers.get('content-disposition')
    const filename = contentDisposition ? contentDisposition.split('filename=')[1]?.replace(/["']/g, '') : 'download'

    // Stream the file data directly to the client
    return new Response(response.body, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    })
  } catch (error) {
    console.error('Error downloading file:', error)
    return new Response(JSON.stringify({ error: 'Failed to download file' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
