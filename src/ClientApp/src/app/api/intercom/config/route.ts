import { authOptions } from '@/lib/auth/authConfig'
import jwt from 'jsonwebtoken'
import { getServerSession } from 'next-auth'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get the user session
    const session = await getServerSession(authOptions)

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized - no valid session' }, { status: 401 })
    }

    // Get Intercom configuration from environment
    const intercomSecret = process.env.INTERCOM_SECRET_KEY
    const intercomAppId = process.env.INTERCOM_APP_ID

    if (!intercomSecret) {
      console.error('INTERCOM_SECRET_KEY not configured')
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }

    if (!intercomAppId) {
      console.error('INTERCOM_APP_ID not configured')
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }

    // Prepare JWT payload with user data
    const now = Math.floor(Date.now() / 1000)
    const payload = {
      user_id: session.user.id,
      email: session.user.email,
      name: session.user.name || '',
      created_at: now,
      groups: (session.user.groups || []).join(', '),
      roles: (session.user.roles || []).join(', '),
      iat: now,
      exp: now + 60 * 60, // 1 hour expiration (matching session max age)
    }

    // Sign the JWT with Intercom secret
    const token = jwt.sign(payload, intercomSecret)

    return NextResponse.json({
      jwt: token,
      expires_at: payload.exp,
      app_id: intercomAppId,
    })
  } catch (error) {
    console.error('Error generating Intercom JWT:', error)
    return NextResponse.json({ error: 'Failed to generate JWT' }, { status: 500 })
  }
}
