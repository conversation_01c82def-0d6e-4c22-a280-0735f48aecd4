import { NextRequest, NextResponse } from 'next/server'
import { track, setUserProfile } from '../../../lib/analytics/mixpanel-server'
import { getToken } from 'next-auth/jwt'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, event, properties, profile } = body

    // Get the token to ensure we have the user's email
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })
    const distinctId = properties?.distinct_id || token?.user?.email || token?.user?.id

    if (!distinctId) {
      console.warn('[Mixpanel] No distinct_id available for tracking')
      return NextResponse.json({ error: 'No distinct_id available' }, { status: 400 })
    }

    switch (type) {
      case 'track':
        await track(event, { ...properties, distinct_id: distinctId })
        break
      case 'identify':
        if (profile) {
          await setUserProfile(distinctId, profile)
        }
        break
      default:
        return NextResponse.json({ error: 'Invalid event type' }, { status: 400 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error processing mixpanel request:', error)
    return NextResponse.json({ error: 'Failed to process analytics request' }, { status: 500 })
  }
}
