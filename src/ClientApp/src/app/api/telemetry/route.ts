import { NextResponse } from 'next/server'
import * as AppInsightsServer from '@/lib/analytics/appinsights-server'
import * as MixpanelServer from '@/lib/analytics/mixpanel-server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/authConfig'

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    const userId = session?.user?.email || session?.user?.id

    if (!userId) {
      return NextResponse.json({ success: false, message: 'No user ID available' }, { status: 400 })
    }

    const data = await req.json()
    const { target, type, ...payload } = data

    // Route to the appropriate analytics service
    if (target === 'appinsights' || target === 'all') {
      switch (type) {
        case 'event':
          await AppInsightsServer.trackEvent(payload.name, payload.properties)
          break
        case 'exception':
          await AppInsightsServer.trackException(new Error(payload.error.message), {
            ...payload.properties,
            stack: payload.error.stack,
          })
          break
        case 'pageview':
          // Server-side doesn't have direct pageview tracking, but we can log as an event
          await AppInsightsServer.trackEvent('PageView', { pageName: payload.name, ...payload.properties })
          break
        case 'metric':
          AppInsightsServer.trackMetric(payload.name, payload.value, payload.properties)
          break
        case 'trace':
          AppInsightsServer.trackTrace(payload.message, payload.properties, payload.severity)
          break
      }
    }

    // Also route to Mixpanel if requested
    if (target === 'mixpanel' || target === 'all') {
      switch (type) {
        case 'event':
        case 'pageview':
          await MixpanelServer.track(type === 'pageview' ? 'Page View' : payload.name, {
            ...payload.properties,
            distinct_id: userId,
          })
          break
        case 'exception':
          await MixpanelServer.track('Error', {
            distinct_id: userId,
            error_name: payload.error.name,
            error_message: payload.error.message,
            error_stack: payload.error.stack,
            ...payload.properties,
          })
          break
        case 'identify':
          await MixpanelServer.setUserProfile(userId, payload.profile)
          break
      }
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error processing telemetry:', error)
    return NextResponse.json({ success: false, message: 'Failed to process telemetry' }, { status: 500 })
  }
}
