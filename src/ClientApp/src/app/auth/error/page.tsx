'use client'

import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import React from 'react'

const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f3f4f6',
  },
  card: {
    padding: '2rem',
    backgroundColor: 'white',
    borderRadius: '0.5rem',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    color: '#1f2937',
  },
  errorMessage: {
    color: 'red',
    marginBottom: '1rem',
  },
}

export default function AuthError() {
  const searchParams = useSearchParams()
  const error = searchParams?.get('error')

  useEffect(() => {
    if (error) {
      console.error('Authentication error:', error)
    }
  }, [error])

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <h3 style={styles.title}>Authentication Error</h3>
        <p style={styles.errorMessage}>{error || 'An unknown error occurred during authentication.'}</p>
        <p>Please try again or contact support if the problem persists.</p>
      </div>
    </div>
  )
}
