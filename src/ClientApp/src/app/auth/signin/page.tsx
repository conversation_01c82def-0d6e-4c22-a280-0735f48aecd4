'use client'

import { signIn } from 'next-auth/react'
import { useState, useEffect } from 'react'
import Image from 'next/image'
import { useSearchParams } from 'next/navigation'
import { getEmailDomain, isValidEmail } from '@/lib/utils/emailUtils'

const isSafeUrl = (url: string): boolean => {
  try {
    if (url.startsWith('/')) {
      return !url.startsWith('//') && !url.startsWith('/auth/signin')
    }
    return false
  } catch {
    return false
  }
}

export default function SignIn() {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<string | null>(null)
  const [showMessage, setShowMessage] = useState(false)
  const [email, setEmail] = useState('')
  const [emailError, setEmailError] = useState<string | null>(null)
  const searchParams = useSearchParams()

  useEffect(() => {
    const urlMessage = searchParams.get('message')
    if (urlMessage) {
      setMessage(urlMessage)
      setTimeout(() => setShowMessage(true), 100)
    }
  }, [searchParams])

  const validateEmail = (email: string): boolean => {
    if (!email.trim()) {
      setEmailError('Email is required')
      return false
    }

    if (!isValidEmail(email)) {
      setEmailError('Please enter a valid email address')
      return false
    }

    setEmailError(null)
    return true
  }

  const handleSignIn = async () => {
    if (!validateEmail(email)) {
      return
    }

    setIsLoading(true)
    const callbackUrl = searchParams.get('callbackUrl')
    const finalCallbackUrl = callbackUrl && isSafeUrl(callbackUrl) ? callbackUrl : '/'

    // Check if force_account_selection flag is in sessionStorage
    const forceAccountSelection = sessionStorage.getItem('force_account_selection') === 'true'

    // Remove the flag after reading it (one-time use)
    if (forceAccountSelection) {
      sessionStorage.removeItem('force_account_selection')
    }

    // Create the options object with callbackUrl
    const options = {
      callbackUrl: finalCallbackUrl,
      redirect: true,
    }

    // Create an object for additional parameters
    let additionalParams = {}

    // Add prompt parameter if needed
    if (forceAccountSelection) {
      additionalParams = { ...additionalParams, prompt: 'select_account' }
    }

    // Add login_hint parameter if email is provided
    if (email) {
      additionalParams = { ...additionalParams, login_hint: email, domain_hint: getEmailDomain(email) }
    }

    // Call signIn with the provider, options, and additional parameters
    await signIn('azure-ad', options, additionalParams)
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-white dark:bg-gray-900">
      <div className="w-full max-w-[400px] border border-gray-200 dark:border-gray-700 rounded-lg p-8 bg-white dark:bg-gray-800">
        {/* Logo image */}
        <div className="text-center mb-10">
          <Image
            src="/assets/minikai-logo-dark.png"
            alt="Minikai Logo"
            width={180}
            height={45}
            className="dark:hidden"
            priority
            style={{ width: 'auto', height: 'auto', margin: '0 auto' }}
          />
          <Image
            src="/assets/minikai-logo-light.png"
            alt="Minikai Logo"
            width={180}
            height={45}
            className="hidden dark:block"
            priority
            style={{ width: 'auto', height: 'auto', margin: '0 auto' }}
          />
        </div>

        {/* Login text */}
        <h2 className="text-xl font-normal text-gray-600 dark:text-gray-300 mb-6">Login</h2>

        {/* Session timeout message - minimal styling */}
        {message && (
          <div
            className={`mb-6 overflow-hidden transition-all duration-300 ease-in-out ${
              showMessage ? 'max-h-40 opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
              <p className="text-sm text-gray-700 dark:text-gray-200">{message}</p>
            </div>
          </div>
        )}

        {/* Email input field */}
        <form
          onSubmit={e => {
            e.preventDefault()
            handleSignIn()
          }}
          className="mb-6"
        >
          <div className="space-y-2">
            <input
              type="email"
              placeholder="Enter email"
              value={email}
              onChange={e => {
                setEmail(e.target.value)
                if (emailError) validateEmail(e.target.value)
              }}
              onBlur={() => validateEmail(email)}
              className={`w-full px-4 py-3 border ${
                emailError ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
              } rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 ${
                emailError
                  ? 'focus:ring-red-300 dark:focus:ring-red-500'
                  : 'focus:ring-gray-300 dark:focus:ring-gray-500'
              }`}
              aria-invalid={emailError ? 'true' : 'false'}
              aria-describedby={emailError ? 'email-error' : undefined}
            />
            {emailError && (
              <p id="email-error" className="text-sm text-red-500 dark:text-red-400">
                {emailError}
              </p>
            )}
          </div>
        </form>

        {/* Sign in button with minimal styling */}
        <button
          onClick={handleSignIn}
          disabled={isLoading}
          className="w-full px-4 py-3 text-gray-800 dark:text-gray-200 text-base font-normal bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-500 disabled:opacity-50 transition-all duration-200"
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-700"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Signing in...
            </span>
          ) : (
            <span className="flex items-center justify-center">Sign in with email</span>
          )}
        </button>
      </div>
    </div>
  )
}
