export const dynamic = 'force-dynamic'

import { ChatInterface } from '@/features/chat/components/ChatInterface.client'
import ChatHeader from '@/features/chat/components/header/ChatHeader.client'
import { getChatPageData } from '@/features/chat/services/chat.service'
import { getServerAuth } from '@/lib/auth/auth.server'

interface DynamicChatPageProps {
  params: Promise<{
    chatId?: string[]
  }>
}

export default async function DynamicChatPage(props: DynamicChatPageProps) {
  const params = await props.params

  const auth = await getServerAuth()
  const canEditMini = auth.isSuperAdmin || auth.roles.includes('Admin')

  const routeChatId = params.chatId?.[0]

  const { allMinis, messages: routeInitialMessages, headerMiniDto } = await getChatPageData(routeChatId)

  if (routeChatId && routeInitialMessages.length > 0) {
    console.log(`Loaded ${routeInitialMessages.length} messages for chat ${routeChatId}`)
  }

  return (
    <>
      <ChatHeader miniForChatId={headerMiniDto} canEditMini={canEditMini} />
      <ChatInterface
        routeChatId={routeChatId}
        miniId={headerMiniDto?.id.toString()} // TODO: id of minis needs to be of consistent type. SlimMiniDto.id: number, ChatDto.miniId: string. Mentioned in: https://minikai.atlassian.net/browse/MK-290
        routeInitialMessages={routeInitialMessages}
        allMinis={allMinis}
      />
    </>
  )
}
