'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function ChatError({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  const router = useRouter()

  useEffect(() => {
    console.error('Chat Error:', error)

    // If it's an auth error, redirect to signin
    if (
      error.message.includes('auth') ||
      error.message.includes('token') ||
      error.message.includes('Authentication required')
    ) {
      router.push('/auth/signin?message=Your session has expired. Please sign in again.')
    }
  }, [error, router])

  return (
    // if it's not an auth error, show the error page
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="max-w-md w-full space-y-4 text-center">
        <div className="space-y-2">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Something went wrong</h1>
          <p className="text-gray-600 dark:text-gray-400">{error.message || 'An unexpected error occurred'}</p>
          {process.env.NODE_ENV === 'development' && (
            <pre className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left text-sm overflow-auto">
              {error.stack}
            </pre>
          )}
        </div>
        <button
          onClick={reset}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Try again
        </button>
      </div>
    </div>
  )
}
