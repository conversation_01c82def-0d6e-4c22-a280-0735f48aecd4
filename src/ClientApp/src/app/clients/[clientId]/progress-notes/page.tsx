'use client'

import { useParams, useSearchParams } from 'next/navigation'
import React from 'react'
import { ProgressNotes } from '../../../../components/ProgressNotes'
import { clientsData } from '../../../../components/TaskList'
import { ThemedContent } from '../../../../components/ThemedContent'

interface PageProps {
  params: Promise<{
    clientId: string
  }>
  searchParams: Promise<{
    highlightedNoteId?: string
  }>
}

const ProgressNotesPage: React.FC<PageProps> = () => {
  const params = useParams()
  const searchParams = useSearchParams()
  const clientId = params.clientId
  const highlightedNoteId = searchParams.get('highlightedNoteId')
    ? parseInt(searchParams.get('highlightedNoteId')!)
    : undefined

  const client = clientsData.find(c => c.id === clientId)

  if (!client) {
    return <div>Client not found</div>
  }

  return (
    <ThemedContent>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Progress Notes - {client.name}</h1>
        <ProgressNotes client={client} highlightedNoteId={highlightedNoteId || -1} />
      </div>
    </ThemedContent>
  )
}

export default ProgressNotesPage
