@import './editor.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white text-neutral-900 dark:text-white dark:bg-black;
    @apply antialiased;

    font-size: 16px;
  }
}

@layer utilities {
  .animate-gradient-x {
    background-size: 200% 100%;
    animation: gradient-x 8s linear infinite;
  }
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced form panel animations */
@keyframes form-panel-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes form-panel-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Smooth panel transitions with custom easing */
.panel-opening {
  animation: form-panel-slide-in 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.panel-closing {
  animation: form-panel-slide-out 350ms cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

html,
body {
  @apply bg-white text-neutral-900 dark:bg-black dark:text-white;
}

input::placeholder,
textarea::placeholder {
  @apply text-black/50 dark:text-white/40;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  @apply text-black/50 dark:text-white/40;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  @apply text-black/40 dark:text-white/40;
}

.react-colorful {
  width: 100% !important;
}

[data-reference-hidden] {
  opacity: 0;
  pointer-events: none;
}

::-webkit-scrollbar {
  @apply w-1 h-1 bg-neutral-500/20;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-500/50 rounded-full;
}

input[type='range'] {
  @apply h-2.5 bg-neutral-200 border-0 rounded appearance-none active:bg-neutral-300 transition-all;
  @apply dark:bg-neutral-700 dark:active:bg-neutral-600;

  &::-webkit-slider-thumb {
    @apply appearance-none w-3 h-5 bg-neutral-800 rounded-full active:bg-neutral-900 active:w-4 active:h-6 transition-all;
    @apply dark:bg-neutral-100 dark:active:bg-white;
  }
}
