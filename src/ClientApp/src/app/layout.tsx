import ActivityWrapper from '@/components/ActivityWrapper'
import AuthWrapper from '@/components/AuthWrapper'
import ClientSessionProvider from '@/components/SessionProvider'
import { ThemeProvider } from '@/components/ThemeProvider'
import { ToastProvider } from '@/components/ui/ToastProvider'
import { ChatProvider } from '@/context/ChatContext'
import { MinisProvider } from '@/context/MinisContext'
import { SidebarProvider } from '@/context/SidebarContext'
import { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

import { IntercomWidget } from '@/features/intercom/components/IntercomWidget.client'
import 'cal-sans'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Minikai',
  description: 'Here to help!',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`h-full ${inter.className}`}>
      <body className="h-full">
        <ThemeProvider>
          <ClientSessionProvider>
            <SidebarProvider>
              <MinisProvider>
                <ChatProvider>
                  <ActivityWrapper>
                    <AuthWrapper>{children}</AuthWrapper>
                  </ActivityWrapper>
                  <IntercomWidget />
                  <ToastProvider />
                </ChatProvider>
              </MinisProvider>
            </SidebarProvider>
          </ClientSessionProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
