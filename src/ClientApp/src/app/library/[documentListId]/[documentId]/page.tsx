import { RoomEditor } from '@/components/RoomEditor'
import { ThemedContent } from '@/components/ThemedContent'

export default async function RoomPage(props: {
  params: Promise<{ documentId: string; noCollab?: string; documentListId: string }>
}) {
  const params = await props.params
  return (
    <ThemedContent>
      <div className="flex flex-col w-full h-full">
        <div className="w-full h-full">
          <RoomEditor
            documentId={params.documentId}
            noCollab={params.noCollab}
            documentListId={params.documentListId}
          />
        </div>
      </div>
    </ThemedContent>
  )
}
