'use client'

import React from 'react'
import { ThemedContent } from '../../components/ThemedContent'
import { WorkspaceDocumentLists } from '../../components/WorkspaceDocumentLists'
import { DocumentListsClient, DocumentListDto } from '../../web-api-client'
import { useAuth } from '../../hooks/useAuth'

export default function LibraryPage() {
  const [documentLists, setDocumentLists] = React.useState<DocumentListDto[]>([])
  const [filteredLists, setFilteredLists] = React.useState<DocumentListDto[]>([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('desc')
  const { groupId } = useAuth()

  const fetchDocumentLists = React.useCallback(async () => {
    const documentListsClient = new DocumentListsClient()
    try {
      const response = await documentListsClient.getDocumentLists(groupId)
      setDocumentLists(response)
      setFilteredLists(response)
      setLoading(false)
    } catch (err) {
      setError('Failed to fetch document lists')
      console.error('Failed to fetch document lists:', err)
      setLoading(false)
    }
  }, [groupId])

  React.useEffect(() => {
    fetchDocumentLists()
  }, [fetchDocumentLists])

  const handleSearch = (term: string) => {
    if (!term.trim()) {
      setFilteredLists(documentLists)
      return
    }

    const searchTerm = term.toLowerCase()
    const filtered = documentLists
      .map(list => ({
        ...list,
        items: list.items?.filter(item => item.title?.toLowerCase().includes(searchTerm)),
      }))
      .filter(list => list.items && list.items.length > 0) as DocumentListDto[]

    setFilteredLists(filtered)
  }

  const handleDocumentDeleted = () => {
    fetchDocumentLists()
  }

  const toggleSort = () => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'))
  }

  return (
    <ThemedContent>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-8">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Library</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative max-w-md">
                <input
                  type="text"
                  onChange={e => handleSearch(e.target.value)}
                  placeholder="Search documents..."
                  className="w-full pl-10 pr-4 py-2 rounded-xl bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700/50 focus:outline-none focus:ring-2 focus:ring-green-400/30 dark:focus:ring-green-400/30 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                />
                <svg
                  className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <button
                onClick={toggleSort}
                className="p-2 rounded-xl bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 border border-gray-100 dark:border-gray-700/50"
              >
                <svg
                  className={`w-5 h-5 transform transition-transform duration-200 ${sortOrder === 'desc' ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div className="h-px mt-8 bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800" />
        </div>

        {loading ? (
          <div className="animate-pulse space-y-8">
            <div className="h-48 bg-gray-100 dark:bg-gray-800/50 rounded-xl" />
            <div className="h-48 bg-gray-100 dark:bg-gray-800/50 rounded-xl" />
          </div>
        ) : error ? (
          <div className="rounded-xl bg-red-50/50 dark:bg-red-900/10 p-4">
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          </div>
        ) : (
          <div className="transition-all duration-300 ease-in-out">
            <WorkspaceDocumentLists
              documentLists={filteredLists}
              onDocumentDeleted={handleDocumentDeleted}
              mode="library"
              sortOrder={sortOrder}
            />
          </div>
        )}
      </div>
    </ThemedContent>
  )
}
