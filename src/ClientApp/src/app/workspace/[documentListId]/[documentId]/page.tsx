'use client'

import { RoomEditor } from '@/components/RoomEditor'
import { ThemedContent } from '@/components/ThemedContent'
import { DocumentItemsClient } from '@/web-api-client'
import { useParams } from 'next/navigation'
import { useEffect, useRef } from 'react'

export default function RoomPage() {
  const params = useParams<{ documentId: string; noCollab?: string; documentListId: string }>()
  const containerRef = useRef<HTMLDivElement>(null)

  // Fetch document details to get its integer ID
  useEffect(() => {
    const fetchDocument = async () => {
      try {
        const client = new DocumentItemsClient()
        const doc = await client.getDocumentItem(params.documentId)
      } catch (error) {
        console.error('Error fetching document:', error)
      }
    }
    fetchDocument()
  }, [params.documentId])

  return (
    <ThemedContent>
      <div className="block 2xl:hidden"></div>
      <div ref={containerRef} className="hidden 2xl:flex w-full h-screen overflow-hidden m-0 p-0">
        <div className="flex-grow h-full flex flex-col min-h-0">
          <div className="flex-1 overflow-y-auto">
            <RoomEditor
              documentId={params.documentId}
              noCollab={params.noCollab}
              documentListId={params.documentListId}
            />
          </div>
        </div>
      </div>
    </ThemedContent>
  )
}
