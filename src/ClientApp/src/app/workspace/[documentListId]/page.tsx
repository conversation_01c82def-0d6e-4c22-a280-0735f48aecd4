import { DocumentList } from '../../../components/DocumentList'
import { ThemedContent } from '../../../components/ThemedContent'

interface DocumentListPageProps {
  params: Promise<{
    documentListId: string
  }>
}

export default async function DocumentListPage(props: DocumentListPageProps) {
  const params = await props.params
  const documentListId = parseInt(params.documentListId, 10)

  return (
    <ThemedContent>
      <div className="max-w-4xl mx-auto p-6">
        <DocumentList documentListId={documentListId} />
      </div>
    </ThemedContent>
  )
}
