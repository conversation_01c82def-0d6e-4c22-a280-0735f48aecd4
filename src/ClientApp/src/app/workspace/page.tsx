'use client'

import React, { useEffect, useState, useCallback, memo } from 'react'
import { ThemedContent } from '../../components/ThemedContent'
import { WorkspaceDocumentLists } from '../../components/WorkspaceDocumentLists'
import SearchBar from '../../components/SearchBar'
import {
  DocumentListsClient,
  DocumentListDto,
  DocumentItemDto,
  DocumentItemType,
  DocumentItemsClient,
  CreateDocumentItemCommand,
} from '../../web-api-client'
import { useAuth } from '../../hooks/useAuth'
import * as Dropdown from '@radix-ui/react-dropdown-menu'
import * as Dialog from '@radix-ui/react-dialog'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'

const RecentActivityDropdown = memo(({ documentLists }: { documentLists: DocumentListDto[] }) => {
  const router = useRouter()
  const recentItems = React.useMemo(() => {
    return documentLists
      .flatMap(list =>
        (list.items || []).map(item => ({
          ...item,
          listId: list.id,
          listTitle: list.title,
        })),
      )
      .sort((a, b) => new Date(b.created || 0).getTime() - new Date(a.created || 0).getTime())
      .slice(0, 5)
  }, [documentLists])

  if (recentItems.length === 0) return null

  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <button className="p-2 rounded-xl bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 border border-gray-100 dark:border-gray-700/50">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </button>
      </Dropdown.Trigger>
      <Dropdown.Portal>
        <Dropdown.Content className="w-80 mt-2 p-2 rounded-xl bg-white dark:bg-gray-800 shadow-xl border border-gray-100 dark:border-gray-700/50">
          <div className="px-3 py-2 mb-2">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Recent Activity</h3>
          </div>
          {recentItems.map(item => (
            <Dropdown.Item
              key={item.docId}
              className="focus:outline-none"
              onClick={() => {
                if (item.listId && item.docId) {
                  router.push(`/workspace/${item.listId}/${item.docId}`)
                }
              }}
            >
              <div className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors duration-200 cursor-pointer">
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-8 h-8 rounded-lg ${
                      item.type === 'Template'
                        ? 'bg-purple-100 dark:bg-purple-900/20'
                        : 'bg-blue-100 dark:bg-blue-900/20'
                    } flex items-center justify-center`}
                  >
                    <svg
                      className={`w-4 h-4 ${
                        item.type === 'Template'
                          ? 'text-purple-500 dark:text-purple-400'
                          : 'text-blue-500 dark:text-blue-400'
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{item.title}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(item.created || '').toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </Dropdown.Item>
          ))}
        </Dropdown.Content>
      </Dropdown.Portal>
    </Dropdown.Root>
  )
})

RecentActivityDropdown.displayName = 'RecentActivityDropdown'

const QuickActions = memo(
  ({ onDocumentCreated, documentLists }: { onDocumentCreated: () => void; documentLists: DocumentListDto[] }) => {
    const router = useRouter()
    const { groupId } = useAuth()
    const [isCreating, setIsCreating] = useState(false)
    const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
    const [isNewDocumentDialogOpen, setIsNewDocumentDialogOpen] = useState(false)
    const [selectedListId, setSelectedListId] = useState<number | null>(null)

    const templates = React.useMemo(() => {
      if (!selectedListId) return []
      const selectedList = documentLists.find(list => list.id === selectedListId)
      return selectedList?.items?.filter(item => item.type === 'Template') || []
    }, [documentLists, selectedListId])

    const allTemplates = React.useMemo(
      () => documentLists.flatMap(list => list.items?.filter(item => item.type === 'Template') || []),
      [documentLists],
    )

    const getDraftCount = useCallback((list: DocumentListDto) => {
      return list.items?.filter(item => !item.type || item.type === 'Draft').length || 0
    }, [])

    const getTemplateCount = useCallback((list: DocumentListDto) => {
      return list.items?.filter(item => item.type === 'Template').length || 0
    }, [])

    const handleCreateDocument = useCallback(
      async (type: DocumentItemType.Draft | DocumentItemType.Template) => {
        if (isCreating || !selectedListId) return

        const selectedList = documentLists.find(list => list.id === selectedListId)
        if (!selectedList) {
          toast.error('Please select a list')
          return
        }

        setIsCreating(true)
        try {
          const client = new DocumentItemsClient()
          const command = new CreateDocumentItemCommand({
            listId: selectedList.id,
            title: `New ${DocumentItemType[type]}`,
            data: '',
            fileIds: [],
            groupId: groupId || '',
            type: type,
          })

          const id = await client.createDocumentItem(command)
          if (id) {
            toast.success(`${type} created successfully`)
            onDocumentCreated()
            router.push(`/workspace/${selectedList.id}/${id}`)
          }
        } catch (error) {
          console.error('Error creating document:', error)
          toast.error('Failed to create document')
        } finally {
          setIsCreating(false)
          setIsNewDocumentDialogOpen(false)
          setSelectedListId(null)
        }
      },
      [documentLists, groupId, onDocumentCreated, router, isCreating, selectedListId],
    )

    const handleCreateFromTemplate = useCallback(
      async (template: DocumentItemDto) => {
        if (isCreating || !selectedListId) return

        const selectedList = documentLists.find(list => list.id === selectedListId)
        if (!selectedList) {
          toast.error('Please select a list')
          return
        }

        setIsCreating(true)
        try {
          const client = new DocumentItemsClient()
          const command = new CreateDocumentItemCommand({
            listId: selectedList.id,
            title: `${template.title} Copy`,
            data: template.data || '',
            fileIds: template.files?.filter(f => f.fileId).map(f => f.fileId!) || [],
            groupId: groupId || '',
            type: DocumentItemType.Draft,
          })

          const id = await client.createDocumentItem(command)
          if (id) {
            toast.success('Document created from template')
            onDocumentCreated()
            router.push(`/workspace/${selectedList.id}/${id}`)
          }
        } catch (error) {
          console.error('Error creating from template:', error)
          toast.error('Failed to create from template')
        } finally {
          setIsCreating(false)
          setIsTemplateDialogOpen(false)
          setSelectedListId(null)
        }
      },
      [documentLists, groupId, onDocumentCreated, router, isCreating, selectedListId],
    )

    const isDisabled = isCreating || !documentLists.length

    return (
      <>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
          <button
            onClick={() => setIsNewDocumentDialogOpen(true)}
            disabled={isDisabled}
            className="group relative flex items-center p-6 rounded-2xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-500/5 dark:to-purple-500/5 hover:from-blue-500/20 hover:to-purple-500/20 transition-all duration-300 border border-blue-100 dark:border-blue-900 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="w-12 h-12 rounded-xl bg-blue-500/10 dark:bg-blue-500/20 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
              {isCreating ? (
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400 animate-spin" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
              ) : (
                <svg
                  className="w-6 h-6 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              )}
            </div>
            <div className="text-left">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-1">New Document</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Create from scratch</p>
            </div>
          </button>

          <button
            onClick={() => setIsTemplateDialogOpen(true)}
            disabled={isDisabled || allTemplates.length === 0}
            className="group relative flex items-center p-6 rounded-2xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 dark:from-purple-500/5 dark:to-pink-500/5 hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300 border border-purple-100 dark:border-purple-900 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="w-12 h-12 rounded-xl bg-purple-500/10 dark:bg-purple-500/20 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
              {isCreating ? (
                <svg className="w-6 h-6 text-purple-600 dark:text-purple-400 animate-spin" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
              ) : (
                <svg
                  className="w-6 h-6 text-purple-600 dark:text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              )}
            </div>
            <div className="text-left">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-1">From Template</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {allTemplates.length === 0 ? 'No templates available' : 'Quick start guides'}
              </p>
            </div>
          </button>
        </div>

        <Dialog.Root
          open={isNewDocumentDialogOpen}
          onOpenChange={() => {
            setIsNewDocumentDialogOpen(false)
            setSelectedListId(null)
          }}
        >
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/20 backdrop-blur-sm dark:bg-black/40" />
            <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg max-h-[85vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-100 dark:border-gray-700/50">
              <Dialog.Title className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Create New Document
              </Dialog.Title>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select List</label>
                  <div className="grid grid-cols-1 gap-3">
                    {documentLists.map(list => {
                      const draftCount = getDraftCount(list)
                      return (
                        <button
                          key={list.id}
                          onClick={() => setSelectedListId(list.id!)}
                          className={`p-4 rounded-xl border text-left transition-colors duration-200 ${
                            selectedListId === list.id
                              ? 'border-blue-400/30 dark:border-blue-400/30 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-100 dark:border-gray-700/50 hover:border-blue-400/30 dark:hover:border-blue-400/30'
                          }`}
                        >
                          <h3 className="font-medium text-gray-900 dark:text-white">{list.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {draftCount} draft{draftCount === 1 ? '' : 's'}
                          </p>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-8">
                  <button
                    onClick={() => {
                      setIsNewDocumentDialogOpen(false)
                      setSelectedListId(null)
                    }}
                    className="px-4 py-2 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleCreateDocument(DocumentItemType.Draft)}
                    disabled={!selectedListId || isCreating}
                    className="px-4 py-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Document
                  </button>
                </div>
              </div>

              <Dialog.Close asChild>
                <button className="absolute top-4 right-4 p-2 rounded-lg text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </Dialog.Close>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>

        <Dialog.Root
          open={isTemplateDialogOpen}
          onOpenChange={() => {
            setIsTemplateDialogOpen(false)
            setSelectedListId(null)
          }}
        >
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/20 backdrop-blur-sm dark:bg-black/40" />
            <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl max-h-[85vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-100 dark:border-gray-700/50">
              <Dialog.Title className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Choose a Template
              </Dialog.Title>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select List</label>
                <div className="grid grid-cols-2 gap-3 mb-6">
                  {documentLists.map(list => {
                    const templateCount = getTemplateCount(list)
                    return (
                      <button
                        key={list.id}
                        onClick={() => setSelectedListId(list.id!)}
                        className={`p-4 rounded-xl border text-left transition-colors duration-200 ${
                          selectedListId === list.id
                            ? 'border-purple-400/30 dark:border-purple-400/30 bg-purple-50 dark:bg-purple-900/20'
                            : 'border-gray-100 dark:border-gray-700/50 hover:border-purple-400/30 dark:hover:border-purple-400/30'
                        }`}
                      >
                        <h3 className="font-medium text-gray-900 dark:text-white">{list.title}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {templateCount} template{templateCount === 1 ? '' : 's'}
                        </p>
                      </button>
                    )
                  })}
                </div>

                {selectedListId ? (
                  templates.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {templates.map(template => (
                        <button
                          key={template.docId}
                          onClick={() => handleCreateFromTemplate(template)}
                          className="p-4 rounded-xl bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700/50 hover:border-purple-400/30 dark:hover:border-purple-400/30 transition-colors duration-200 text-left group"
                        >
                          <div className="flex items-center space-x-3 mb-2">
                            <div className="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/20 flex items-center justify-center">
                              <svg
                                className="w-4 h-4 text-purple-500 dark:text-purple-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            </div>
                            <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-purple-500 dark:group-hover:text-purple-400 transition-colors duration-200">
                              {template.title}
                            </h3>
                          </div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Created {new Date(template.created || '').toLocaleDateString()}
                          </p>
                        </button>
                      ))}
                    </div>
                  ) : (
                    <div className="rounded-2xl bg-gray-50 dark:bg-gray-800/50 p-8 text-center">
                      <div className="w-12 h-12 rounded-xl bg-gray-100 dark:bg-gray-900/50 mx-auto mb-4 flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">No templates available in this list</p>
                    </div>
                  )
                ) : (
                  <div className="rounded-2xl bg-gray-50 dark:bg-gray-800/50 p-8 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Select a list to view available templates
                    </p>
                  </div>
                )}
              </div>

              <Dialog.Close asChild>
                <button className="absolute top-4 right-4 p-2 rounded-lg text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </Dialog.Close>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </>
    )
  },
)

QuickActions.displayName = 'QuickActions'

export default function WorkspacePage() {
  const [documentLists, setDocumentLists] = useState<DocumentListDto[]>([])
  const [filteredLists, setFilteredLists] = useState<DocumentListDto[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeView, setActiveView] = useState<'drafts' | 'templates'>('drafts')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const { groupId } = useAuth()

  const fetchDocumentLists = useCallback(async () => {
    const documentListsClient = new DocumentListsClient()
    try {
      const response = await documentListsClient.getDocumentLists(groupId || '')
      setDocumentLists(response)
      setFilteredLists(response)
      setLoading(false)
    } catch (err) {
      setError('Failed to fetch document lists')
      console.error('Failed to fetch document lists:', err)
      setLoading(false)
    }
  }, [groupId])

  useEffect(() => {
    fetchDocumentLists()
  }, [fetchDocumentLists])

  const handleSearch = useCallback(
    (term: string) => {
      if (!term.trim()) {
        setFilteredLists(documentLists)
        return
      }

      const searchTerm = term.toLowerCase()
      const filtered = documentLists
        .map(list => ({
          ...list,
          items: list.items?.filter(item => item.title?.toLowerCase().includes(searchTerm)),
        }))
        .filter(list => list.items && list.items.length > 0) as DocumentListDto[]

      setFilteredLists(filtered)
    },
    [documentLists],
  )

  const handleDocumentDeleted = useCallback(() => {
    fetchDocumentLists()
  }, [fetchDocumentLists])

  const toggleSort = useCallback(() => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'))
  }, [])

  return (
    <ThemedContent>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-8">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Workspace</h1>
              <div className="flex bg-gray-50 dark:bg-gray-800/50 rounded-2xl p-1 shadow-sm">
                <button
                  onClick={() => setActiveView('drafts')}
                  className={`px-6 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                    activeView === 'drafts'
                      ? 'bg-white dark:bg-gray-800 text-blue-500 dark:text-blue-400 shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Drafts
                </button>
                <button
                  onClick={() => setActiveView('templates')}
                  className={`px-6 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                    activeView === 'templates'
                      ? 'bg-white dark:bg-gray-800 text-purple-500 dark:text-purple-400 shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Templates
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <SearchBar onSearch={handleSearch} />
              <RecentActivityDropdown documentLists={documentLists} />
              <button
                onClick={toggleSort}
                className="p-2 rounded-xl bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 border border-gray-100 dark:border-gray-700/50"
              >
                <svg
                  className={`w-5 h-5 transform transition-transform duration-200 ${sortOrder === 'desc' ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div className="h-px mt-8 bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800" />
        </div>

        {loading ? (
          <div className="animate-pulse space-y-8">
            <div className="h-48 bg-gray-100 dark:bg-gray-800/50 rounded-xl" />
            <div className="h-48 bg-gray-100 dark:bg-gray-800/50 rounded-xl" />
          </div>
        ) : error ? (
          <div className="rounded-xl bg-red-50/50 dark:bg-red-900/10 p-4">
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          </div>
        ) : (
          <>
            <QuickActions onDocumentCreated={fetchDocumentLists} documentLists={documentLists} />
            <div className="mt-12 transition-all duration-300 ease-in-out">
              <WorkspaceDocumentLists
                documentLists={filteredLists}
                onDocumentDeleted={handleDocumentDeleted}
                activeView={activeView}
                sortOrder={sortOrder}
              />
            </div>
          </>
        )}
      </div>
    </ThemedContent>
  )
}
