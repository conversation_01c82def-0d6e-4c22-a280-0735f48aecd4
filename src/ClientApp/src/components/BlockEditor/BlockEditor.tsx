'use client'

import { LinkMenu } from '@/components/menus'
import { ContentItemMenu } from '@/components/menus/ContentItemMenu'
import { TextMenu } from '@/components/menus/TextMenu'
import { Sidebar } from '@/components/Sidebar/Sidebar'
import { EditorContext } from '@/context/EditorContext'
import ImageBlockMenu from '@/extensions/ImageBlock/components/ImageBlockMenu'
import { ColumnsMenu } from '@/extensions/MultiColumn/menus'
import { TableColumnMenu, TableRowMenu } from '@/extensions/Table/menus'
import { useBlockEditor } from '@/hooks/useBlockEditor'
import '@/styles/index.css'
import { DocumentItemsClient } from '@/web-api-client'
import { EditorContent } from '@tiptap/react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { EditorHeader } from './components/EditorHeader'
import { BlockEditorProps } from './types'

export const BlockEditor: React.FC<BlockEditorProps> = ({
  hasCollab,
  ydoc,
  provider,
  characterCount,
  collabState,
  currentUser,
  documentId,
  onDocumentUpdated,
  listId,
  defaultMiniId,
  title,
  data,
  type,
}) => {
  const menuContainerRef = useRef(null)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedMini, setSelectedMini] = useState<string | undefined>(undefined)
  const [isDragOver, setIsDragOver] = useState(false)

  const isReadOnly = type === 'Template' || type === 'Library'

  const { editor, otherUsers, minis } = useBlockEditor({
    ydoc,
    provider,
    currentUser,
    isReadOnly,
    defaultMiniId,
  })

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsDragOver(false)

      try {
        const messageData = e.dataTransfer.getData('application/json')
        if (messageData && editor) {
          const message = JSON.parse(messageData)
          if (message.content) {
            editor.commands.insertContent(message.content)
          }
        }
      } catch (error) {
        console.error('Error handling dropped message:', error)
      }
    },
    [editor],
  )

  // Initialize selectedMini with defaultMiniId
  useEffect(() => {
    if (defaultMiniId && !selectedMini) {
      const defaultMini = minis.find(m => m.id === defaultMiniId)
      if (defaultMini) {
        const miniIdString = defaultMini.id.toString()
        setSelectedMini(miniIdString)
        if (editor) {
          const aiExtension = editor.extensionManager.extensions.find(ext => ext.name === 'ai')
          if (aiExtension) {
            aiExtension.storage.selectedMiniId = defaultMini.id
          }
        }
      }
    }
  }, [defaultMiniId, selectedMini, editor, minis])

  // Set document type and ID in AI extension storage
  useEffect(() => {
    if (editor) {
      const aiExtension = editor.extensionManager.extensions.find(ext => ext.name === 'ai')
      if (aiExtension) {
        aiExtension.storage.documentType = type

        // Get numeric ID using DocumentItemsClient
        const fetchDocumentId = async () => {
          try {
            const documentItemsClient = new DocumentItemsClient()
            const doc = await documentItemsClient.getDocumentItem(documentId)
            if (doc) {
              aiExtension.storage.documentId = doc.id
            }
          } catch (error) {
            console.error('Error fetching document ID:', error)
          }
        }
        fetchDocumentId()
      }
    }
  }, [editor, type, documentId])

  const providerValue = useMemo(() => {
    return {}
  }, [])

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(prevState => !prevState)
  }, [])

  if (!editor) {
    return null
  }

  return (
    <EditorContext.Provider value={providerValue}>
      <div className="flex h-full" ref={menuContainerRef}>
        <Sidebar isOpen={isSidebarOpen} onClose={toggleSidebar} editor={editor} />
        <div className="relative flex flex-col flex-1 h-full">
          <EditorHeader
            characters={characterCount.characters()}
            collabState={collabState}
            otherUsers={otherUsers}
            words={characterCount.words()}
            isSidebarOpen={isSidebarOpen}
            toggleSidebar={toggleSidebar}
            currentUser={currentUser}
            documentId={documentId}
            onDocumentUpdated={onDocumentUpdated}
            listId={listId}
            title={title}
            data={data}
            type={type}
            editor={editor}
          />
          <div
            className="flex-1 flex flex-col overflow-hidden"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div
              className={`flex-1 flex flex-col items-center overflow-y-auto ${
                isDragOver ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
            >
              <div className="w-full max-w-4xl h-full px-4">
                <EditorContent editor={editor} className="h-full" />
              </div>
            </div>
          </div>
          {!isReadOnly && <ContentItemMenu editor={editor} />}
          <LinkMenu editor={editor} appendTo={menuContainerRef} />
          <TextMenu editor={editor} />
          <ColumnsMenu editor={editor} appendTo={menuContainerRef} />
          <TableRowMenu editor={editor} appendTo={menuContainerRef} />
          <TableColumnMenu editor={editor} appendTo={menuContainerRef} />
          <ImageBlockMenu editor={editor} appendTo={menuContainerRef} />
        </div>
      </div>
    </EditorContext.Provider>
  )
}

export default BlockEditor
