'use client'

import { <PERSON>ader<PERSON><PERSON><PERSON>, <PERSON>ci<PERSON>, Search, Trash } from 'lucide-react'
import { useState } from 'react'
import { useGroups } from '../hooks/useGroups'
import { CreateGroupCommand, GroupDto, GroupsClient, UpdateGroupCommand } from '../web-api-client'
import { Icon } from './ui/Icon'

interface GroupFormData {
  name: string
  groupId: string
}

export function GroupManager() {
  const [groupsClient] = useState(() => new GroupsClient())
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState<string | null>(null)
  const [formData, setFormData] = useState<GroupFormData>({
    name: '',
    groupId: '',
  })
  const [error, setError] = useState<string | null>(null)
  const { groups, loading, mutate: refreshGroups } = useGroups()
  const [searchTerm, setSearchTerm] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (isEditing) {
        const command = new UpdateGroupCommand({
          groupId: formData.groupId,
          name: formData.name,
        })
        await groupsClient.updateGroup(isEditing, command)
      } else {
        const command = new CreateGroupCommand({
          groupId: formData.groupId,
          name: formData.name,
        })
        await groupsClient.createGroup(command)
      }

      await refreshGroups()
      resetForm()
      setError(null)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save group'
      console.error('Error saving group:', error)
      setError(errorMessage)
    }
  }

  const handleDelete = async (groupId: string) => {
    if (!confirm('Are you sure you want to delete this group?')) return

    try {
      await groupsClient.deleteGroup(groupId)
      await refreshGroups()
      setError(null)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete group'
      console.error('Error deleting group:', error)
      setError(errorMessage)
    }
  }

  const handleEdit = (group: GroupDto) => {
    setFormData({
      name: group.name || '',
      groupId: group.groupId || '',
    })
    setIsEditing(group.groupId || null)
    setIsCreating(false)
  }

  const resetForm = () => {
    setFormData({
      name: '',
      groupId: '',
    })
    setIsCreating(false)
    setIsEditing(null)
  }

  const filteredGroups = groups.filter(
    group =>
      searchTerm === '' ||
      (group.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (group.groupId || '').toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const renderGroupCard = (group: GroupDto) => {
    const isEditingThis = isEditing === group.groupId

    if (isEditingThis) {
      return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group ID</label>
              <input
                type="text"
                value={formData.groupId}
                onChange={e => setFormData({ ...formData, groupId: e.target.value })}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={resetForm}
                className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600 rounded-lg transform active:scale-95 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 active:bg-blue-700 transform active:scale-95 transition-all duration-200 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              >
                Update
              </button>
            </div>
          </form>
        </div>
      )
    }

    return (
      <div className="bg-white dark:bg-gray-800 px-4 py-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-600 transition-colors duration-200">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">{group.name}</h3>
              <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs font-medium text-gray-600 dark:text-gray-300">
                {group.groupId}
              </span>
            </div>
          </div>
          <div className="flex items-start space-x-1">
            <button
              onClick={() => handleEdit(group)}
              className="p-1 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
            >
              <Icon icon={Pencil} className="w-4 h-4" />
            </button>
            <button
              onClick={() => group.groupId && handleDelete(group.groupId)}
              className="p-1 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 rounded hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200"
            >
              <Icon icon={Trash} className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Icon icon={LoaderCircle} className="w-6 h-6 text-gray-400 dark:text-gray-500 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between gap-4">
        <div className="flex-1 flex items-center gap-2">
          <div className="relative flex-1 max-w-md">
            <input
              type="text"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              placeholder="Search groups..."
              className="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Icon icon={Search} className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <button
          onClick={() => {
            setIsEditing(null)
            setIsCreating(true)
          }}
          className="px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 active:bg-blue-700 transform active:scale-95 transition-all duration-200 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
        >
          Create Group
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 p-3 rounded-lg border border-red-100 dark:border-red-800 text-sm">
          {error}
        </div>
      )}

      {isCreating && !isEditing && (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group ID</label>
              <input
                type="text"
                value={formData.groupId}
                onChange={e => setFormData({ ...formData, groupId: e.target.value })}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={resetForm}
                className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600 rounded-lg transform active:scale-95 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 active:bg-blue-700 transform active:scale-95 transition-all duration-200 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              >
                Create
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="grid gap-2">
        {filteredGroups.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-4 text-sm">No groups found</div>
        ) : (
          filteredGroups.map(group => <div key={group.groupId}>{renderGroupCard(group)}</div>)
        )}
      </div>
    </div>
  )
}
