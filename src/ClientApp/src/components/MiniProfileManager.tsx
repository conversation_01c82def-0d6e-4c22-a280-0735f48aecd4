'use client'

import {
  CreateMiniProfileRequest,
  CreateMiniProfileValueRequest,
  MiniProfileDto,
  MiniTemplateVersionDto,
  MiniTemplatesClient,
  MinisClient,
} from '@/web-api-client'
import * as Dialog from '@radix-ui/react-dialog'
import { Calendar, ChevronDown, ChevronRight, Eye, Plus, User } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useTheme } from './ThemeProvider'
import { Icon } from './ui/Icon'

interface MiniProfileManagerProps {
  miniId: number
  templateId?: string
  isExpanded: boolean
  onToggle: () => void
}

export function MiniProfileManager({ miniId, templateId, isExpanded, onToggle }: MiniProfileManagerProps) {
  const [minisClient] = useState(() => new MinisClient())
  const [templatesClient] = useState(() => new MiniTemplatesClient())
  const [profiles, setProfiles] = useState<MiniProfileDto[]>([])
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalCount: 0,
    hasMore: false,
    loadingMore: false,
  })
  const [latestTemplateVersion, setLatestTemplateVersion] = useState<MiniTemplateVersionDto | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedProfile, setSelectedProfile] = useState<MiniProfileDto | null>(null)
  const [selectedProfileTemplateVersion, setSelectedProfileTemplateVersion] = useState<MiniTemplateVersionDto | null>(
    null,
  )
  const [loadingProfileTemplate, setLoadingProfileTemplate] = useState(false)
  const [formValues, setFormValues] = useState<{ [key: string]: string }>({})
  const { portalContainerId } = useTheme()

  const fetchProfiles = async (reset = true) => {
    if (!isExpanded) return

    if (reset) {
      setLoading(true)
      setPagination(prev => ({ ...prev, currentPage: 1 }))
    } else {
      setPagination(prev => ({ ...prev, loadingMore: true }))
    }

    try {
      const pageToFetch = reset ? 1 : pagination.currentPage + 1
      const profilesData = await minisClient.getMiniProfiles(miniId, pageToFetch, 5)

      if (reset) {
        setProfiles(profilesData.items || [])
      } else {
        setProfiles(prev => [...prev, ...(profilesData.items || [])])
      }

      setPagination({
        currentPage: pageToFetch,
        totalCount: profilesData.totalCount || 0,
        hasMore: profilesData.hasNextPage || false,
        loadingMore: false,
      })

      setError(null)
    } catch (error) {
      console.error('Error fetching profiles:', error)
      setError('Failed to load profiles')
    } finally {
      setLoading(false)
      setPagination(prev => ({ ...prev, loadingMore: false }))
    }
  }

  const handleLoadMore = () => {
    if (!pagination.loadingMore && pagination.hasMore) {
      fetchProfiles(false)
    }
  }

  useEffect(() => {
    if (isExpanded) {
      fetchProfiles()
    }
  }, [isExpanded, miniId])

  const fetchLatestTemplateVersion = async () => {
    if (!templateId) return

    try {
      const version = await templatesClient.getLatestMiniTemplateVersion(templateId)
      setLatestTemplateVersion(version)

      // Initialize form values with empty strings for each token
      if (version?.tokens) {
        const initialValues: { [key: string]: string } = {}
        version.tokens.forEach(token => {
          initialValues[token.name] = ''
        })
        setFormValues(initialValues)
      }
    } catch (error) {
      console.error('Error fetching latest template version:', error)
      setLatestTemplateVersion(null)
    }
  }

  const handleCreateProfile = async () => {
    if (!latestTemplateVersion?.tokens) return

    try {
      const values = latestTemplateVersion.tokens
        .filter(token => formValues[token.name]?.trim())
        .map(
          token =>
            new CreateMiniProfileValueRequest({
              name: token.name,
              value: formValues[token.name],
            }),
        )

      if (values.length === 0) {
        setError('Please provide at least one token value')
        return
      }

      const request = new CreateMiniProfileRequest({ values })
      await minisClient.createMiniProfile(miniId, request)

      setIsCreateDialogOpen(false)
      setFormValues({})
      await fetchProfiles()
    } catch (error) {
      console.error('Error creating profile:', error)
      setError('Failed to create profile')
    }
  }

  const handleViewProfile = async (profile: MiniProfileDto) => {
    setSelectedProfile(profile)
    setIsViewDialogOpen(true)

    // Fetch the template version for this profile if we have a templateId
    if (templateId) {
      setLoadingProfileTemplate(true)
      try {
        const version = await templatesClient.getLatestMiniTemplateVersion(templateId)
        setSelectedProfileTemplateVersion(version)
      } catch (error) {
        console.error('Error fetching template version for profile:', error)
        setSelectedProfileTemplateVersion(null)
      } finally {
        setLoadingProfileTemplate(false)
      }
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date))
  }

  const replaceTokensInSystemInstructions = (
    systemInstructions: string,
    profileValues: { name: string; value: string }[],
    templateTokens: { name: string; type: string; description?: string }[],
  ) => {
    if (!systemInstructions) return { processedText: '', replacedTokens: [], missingTokens: [] }

    let processedText = systemInstructions
    const replacedTokens: string[] = []
    const missingTokens: string[] = []

    // Create a map of profile values for quick lookup
    const valueMap = new Map(profileValues.map(v => [v.name, v.value]))

    // Find all tokens in the system instructions (assuming {{tokenName}} format)
    const tokenRegex = /\{\{(\w+)\}\}/g
    const foundTokens = new Set<string>()
    let match

    while ((match = tokenRegex.exec(systemInstructions)) !== null) {
      foundTokens.add(match[1])
    }

    // Process each found token
    foundTokens.forEach(tokenName => {
      const tokenRegex = new RegExp(`\\{\\{${tokenName}\\}\\}`, 'g')
      const profileValue = valueMap.get(tokenName)

      if (profileValue !== undefined) {
        processedText = processedText.replace(tokenRegex, profileValue)
        replacedTokens.push(tokenName)
      } else {
        // Check if this token exists in template tokens
        const templateToken = templateTokens.find(t => t.name === tokenName)
        if (templateToken) {
          missingTokens.push(tokenName)
          // Replace with a placeholder indicating missing value
          processedText = processedText.replace(tokenRegex, `[${tokenName}: NOT SET]`)
        }
      }
    })

    return { processedText, replacedTokens, missingTokens }
  }

  return (
    <div className="mt-3 border-t border-gray-100 dark:border-gray-700 pt-3">
      <div className="flex items-center justify-between">
        <button
          onClick={onToggle}
          className="flex items-center gap-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <Icon icon={isExpanded ? ChevronDown : ChevronRight} className="w-4 h-4" />
          Profiles
        </button>

        {isExpanded && templateId && (
          <button
            onClick={async () => {
              await fetchLatestTemplateVersion()
              setIsCreateDialogOpen(true)
            }}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            <Icon icon={Plus} className="w-3 h-3" />
            Create
          </button>
        )}
      </div>

      {isExpanded && (
        <div className="mt-2 space-y-2">
          {loading && <div className="text-xs text-gray-500 dark:text-gray-400">Loading profiles...</div>}

          {error && <div className="text-xs text-red-500 dark:text-red-400">{error}</div>}

          {profiles.length === 0 && !loading && (
            <div className="text-xs text-gray-500 dark:text-gray-400">No profiles created yet</div>
          )}

          {profiles.map((profile, index) => {
            const isLatest = index === 0 // First item is the latest since they're sorted by createdAt DESC
            return (
              <div
                key={profile.id}
                className={`flex items-center justify-between p-2 rounded border ${
                  isLatest
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                }`}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span
                      className={`text-xs font-medium ${
                        isLatest ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'
                      }`}
                    >
                      {profile.version}
                    </span>
                    {isLatest && (
                      <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                        Latest
                      </span>
                    )}
                  </div>
                  <div
                    className={`flex items-center gap-3 text-xs mt-1 ${
                      isLatest ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    <div>{profile.values?.length || 0} values</div>
                    <div className="flex items-center gap-1">
                      <Icon icon={Calendar} className="w-3 h-3" />
                      {formatDate(profile.createdAt!)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon icon={User} className="w-3 h-3" />
                      {profile.createdBy}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => handleViewProfile(profile)}
                    className={`p-1 rounded transition-all ${
                      isLatest
                        ? 'text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/30'
                        : 'text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    }`}
                    title="View Profile"
                  >
                    <Icon icon={Eye} className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )
          })}

          {/* Skeleton loaders while loading more */}
          {pagination.loadingMore && (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded border animate-pulse"
                >
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                  </div>
                  <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded"></div>
                </div>
              ))}
            </div>
          )}

          {/* Show More button */}
          {pagination.hasMore && !pagination.loadingMore && (
            <button
              onClick={handleLoadMore}
              className="w-full mt-3 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Show More ({pagination.totalCount - profiles.length} remaining)
            </button>
          )}
        </div>
      )}

      {/* Create Profile Dialog */}
      <Dialog.Root open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <Dialog.Portal container={document.getElementById(portalContainerId) || undefined}>
          <Dialog.Overlay className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[100001]" />
          <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl max-h-[85vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-100 dark:border-gray-700 z-[100002]">
            <Dialog.Title className="text-xl font-bold text-gray-900 dark:text-white mb-4">Create Profile</Dialog.Title>

            {latestTemplateVersion ? (
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Template Tokens (Version: {latestTemplateVersion.version})
                  </h3>
                  <div className="space-y-3">
                    {latestTemplateVersion.tokens?.map(token => (
                      <div key={token.name} className="p-3 bg-gray-50 dark:bg-gray-700 rounded border">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Name
                            </label>
                            <div className="px-2 py-1 bg-gray-100 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded text-sm text-gray-700 dark:text-gray-300">
                              {token.name}
                            </div>
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Type
                            </label>
                            <div className="px-2 py-1 bg-gray-100 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded text-sm text-gray-700 dark:text-gray-300">
                              {token.type}
                            </div>
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Value
                            </label>
                            <input
                              type={token.type === 'number' ? 'number' : 'text'}
                              value={formValues[token.name] || ''}
                              onChange={e =>
                                setFormValues(prev => ({
                                  ...prev,
                                  [token.name]: e.target.value,
                                }))
                              }
                              className="w-full px-2 py-1 bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                              placeholder={`Enter ${token.name} value...`}
                            />
                          </div>
                        </div>
                        {token.description && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 dark:text-gray-400">{token.description}</p>
                          </div>
                        )}
                      </div>
                    )) || <div className="text-sm text-gray-500 dark:text-gray-400">No tokens available</div>}
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Dialog.Close asChild>
                    <button className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                      Cancel
                    </button>
                  </Dialog.Close>
                  <button
                    onClick={handleCreateProfile}
                    className="px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Create Profile
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500 dark:text-gray-400">Loading template information...</div>
              </div>
            )}
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      {/* View Profile Dialog */}
      <Dialog.Root open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <Dialog.Portal container={document.getElementById(portalContainerId) || undefined}>
          <Dialog.Overlay className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[100001]" />
          <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl max-h-[85vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-100 dark:border-gray-700 z-[100002]">
            <Dialog.Title className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Version: {selectedProfile?.version}
            </Dialog.Title>

            {selectedProfile && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span>
                    <div className="text-gray-600 dark:text-gray-400">{formatDate(selectedProfile.createdAt!)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Created By:</span>
                    <div className="text-gray-600 dark:text-gray-400">{selectedProfile.createdBy}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Tokens:</h3>
                  <div className="space-y-3">
                    {selectedProfile.values?.map(value => (
                      <div key={value.name} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                        <div className="space-y-2">
                          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                            {value.name}
                          </div>
                          <div className="text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 p-3 rounded border font-mono">
                            {value.value}
                          </div>
                        </div>
                      </div>
                    )) || <div className="text-sm text-gray-500 dark:text-gray-400">No values defined</div>}
                  </div>
                </div>

                {loadingProfileTemplate && (
                  <div className="text-center py-4">
                    <div className="text-gray-500 dark:text-gray-400">Loading template information...</div>
                  </div>
                )}

                {selectedProfileTemplateVersion && (
                  <>
                    {/* System Instructions with Profile Values */}
                    {selectedProfileTemplateVersion.system && (
                      <div>
                        <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">System Instructions:</h3>
                        {(() => {
                          const { processedText } = replaceTokensInSystemInstructions(
                            selectedProfileTemplateVersion.system,
                            selectedProfile.values || [],
                            selectedProfileTemplateVersion.tokens || [],
                          )

                          return (
                            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                              <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-gray-100 font-mono leading-relaxed">
                                {processedText}
                              </pre>
                            </div>
                          )
                        })()}
                      </div>
                    )}
                  </>
                )}

                <div className="flex justify-end">
                  <Dialog.Close asChild>
                    <button className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                      Close
                    </button>
                  </Dialog.Close>
                </div>
              </div>
            )}
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  )
}
