'use client'

import { CreateMiniTemplateVersionRequest, MiniTemplatesClient, MiniTemplateVersionDto } from '@/web-api-client'
import * as Dialog from '@radix-ui/react-dialog'
import { Calendar, ChevronDown, ChevronRight, Eye, Hash, Plus, User } from 'lucide-react'
import { useEffect, useState } from 'react'
import { SystemTokensSection } from './SystemTokensSection'
import { useTheme } from './ThemeProvider'
import { Icon } from './ui/Icon'

interface MiniTemplateVersionManagerProps {
  templateId: string
  isExpanded: boolean
  onToggle: () => void
}

export function MiniTemplateVersionManager({ templateId, isExpanded, onToggle }: MiniTemplateVersionManagerProps) {
  const [templatesClient] = useState(() => new MiniTemplatesClient())
  const [versions, setVersions] = useState<MiniTemplateVersionDto[]>([])
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalCount: 0,
    hasMore: false,
    loadingMore: false,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedVersion, setSelectedVersion] = useState<MiniTemplateVersionDto | null>(null)
  const [formData, setFormData] = useState({
    system: '',
  })
  const [inheritedTokens, setInheritedTokens] = useState<any[]>([])
  const [latestVersion, setLatestVersion] = useState<string | null>(null)
  const { portalContainerId } = useTheme()

  const fetchVersions = async (reset = true) => {
    if (!isExpanded) return

    if (reset) {
      setLoading(true)
      setPagination(prev => ({ ...prev, currentPage: 1 }))
    } else {
      setPagination(prev => ({ ...prev, loadingMore: true }))
    }

    try {
      const pageToFetch = reset ? 1 : pagination.currentPage + 1
      const versionsData = await templatesClient.getMiniTemplateVersions(templateId, pageToFetch, 5)

      if (reset) {
        setVersions(versionsData.items || [])
      } else {
        setVersions(prev => [...prev, ...(versionsData.items || [])])
      }

      setPagination({
        currentPage: pageToFetch,
        totalCount: versionsData.totalCount || 0,
        hasMore: versionsData.hasNextPage || false,
        loadingMore: false,
      })

      setError(null)
    } catch (error) {
      console.error('Error fetching versions:', error)
      setError('Failed to load versions')
    } finally {
      setLoading(false)
      setPagination(prev => ({ ...prev, loadingMore: false }))
    }
  }

  const handleLoadMore = () => {
    if (!pagination.loadingMore && pagination.hasMore) {
      fetchVersions(false)
    }
  }

  useEffect(() => {
    if (isExpanded) {
      fetchVersions()
    }
  }, [isExpanded, templateId])

  const fetchLatestVersionTokens = async () => {
    try {
      const latestVersionData = await templatesClient.getLatestMiniTemplateVersion(templateId)
      if (latestVersionData) {
        setInheritedTokens(latestVersionData.tokens || [])
        setLatestVersion(latestVersionData.version)
        // Pre-populate the system prompt with the previous version's system prompt
        setFormData(prev => ({
          ...prev,
          system: latestVersionData.system || '',
        }))
      } else {
        setInheritedTokens([])
        setLatestVersion(null)
        // Clear the system prompt if no previous version exists
        setFormData(prev => ({
          ...prev,
          system: '',
        }))
      }
    } catch (error) {
      console.error('Error fetching latest version:', error)
      setInheritedTokens([])
      setLatestVersion(null)
      // Clear the system prompt on error
      setFormData(prev => ({
        ...prev,
        system: '',
      }))
    }
  }

  const handleCreateVersion = async () => {
    try {
      // Create version with only system prompt - tokens will be inherited server-side
      const request = new CreateMiniTemplateVersionRequest({
        system: formData.system,
        tokens: [], // Empty array - tokens inherited from latest version
      })

      await templatesClient.createMiniTemplateVersion(templateId, request)

      setIsCreateDialogOpen(false)
      setFormData({
        system: '',
      })
      await fetchVersions()
    } catch (error) {
      console.error('Error creating version:', error)
      setError('Failed to create version')
    }
  }

  const handleViewVersion = (version: MiniTemplateVersionDto) => {
    setSelectedVersion(version)
    setIsViewDialogOpen(true)
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date))
  }

  return (
    <div className="mt-3 border-t border-gray-100 dark:border-gray-700 pt-3">
      <div className="flex items-center justify-between">
        <button
          onClick={onToggle}
          className="flex items-center gap-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <Icon icon={isExpanded ? ChevronDown : ChevronRight} className="w-4 h-4" />
          Versions
        </button>

        {isExpanded && (
          <button
            onClick={async () => {
              await fetchLatestVersionTokens()
              setIsCreateDialogOpen(true)
            }}
            disabled={versions.length === 0}
            className={`flex items-center gap-1 px-2 py-1 text-xs rounded transition-colors ${
              versions.length === 0
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-green-500 text-white hover:bg-green-600'
            }`}
            title={versions.length === 0 ? 'Cannot create version - no previous versions to inherit tokens from' : ''}
          >
            <Icon icon={Plus} className="w-3 h-3" />
            Create
          </button>
        )}
      </div>

      {isExpanded && (
        <div className="mt-2 space-y-2">
          {loading && <div className="text-xs text-gray-500 dark:text-gray-400">Loading versions...</div>}

          {error && <div className="text-xs text-red-500 dark:text-red-400">{error}</div>}

          {versions.length === 0 && !loading && (
            <div className="text-xs text-gray-500 dark:text-gray-400">No versions created yet</div>
          )}

          {versions.map((version, index) => {
            const isLatest = index === 0 // First item is the latest since they're sorted by createdAt DESC
            return (
              <div
                key={version.id}
                className={`flex items-center justify-between p-2 rounded border ${
                  isLatest
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                }`}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span
                      className={`text-xs font-medium ${
                        isLatest ? 'text-green-900 dark:text-green-100' : 'text-gray-900 dark:text-white'
                      }`}
                    >
                      {version.version}
                    </span>
                    {isLatest && (
                      <span className="px-2 py-0.5 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs font-medium rounded-full">
                        Latest
                      </span>
                    )}
                  </div>
                  <div
                    className={`flex items-center gap-3 text-xs mt-1 ${
                      isLatest ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    <div className="flex items-center gap-1">
                      <Icon icon={Hash} className="w-3 h-3" />
                      {version.tokens?.length || 0} tokens
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon icon={Calendar} className="w-3 h-3" />
                      {formatDate(version.createdAt!)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon icon={User} className="w-3 h-3" />
                      {version.createdBy}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => handleViewVersion(version)}
                    className={`p-1 rounded transition-all ${
                      isLatest
                        ? 'text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-100 dark:hover:bg-green-800/30'
                        : 'text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    }`}
                    title="View Version"
                  >
                    <Icon icon={Eye} className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )
          })}

          {/* Skeleton loaders while loading more */}
          {pagination.loadingMore && (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded border animate-pulse"
                >
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                  </div>
                  <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded"></div>
                </div>
              ))}
            </div>
          )}

          {/* Show More button */}
          {pagination.hasMore && !pagination.loadingMore && (
            <button
              onClick={handleLoadMore}
              className="w-full mt-3 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Show More ({pagination.totalCount - versions.length} remaining)
            </button>
          )}
        </div>
      )}

      {/* Create Version Dialog */}
      <Dialog.Root open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <Dialog.Portal container={document.getElementById(portalContainerId) || undefined}>
          <Dialog.Overlay className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[100001]" />
          <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-6xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-100 dark:border-gray-700 z-[100002]">
            <Dialog.Title className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Create Template Version
            </Dialog.Title>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">System Prompt</label>
                <textarea
                  value={formData.system}
                  onChange={e => setFormData(prev => ({ ...prev, system: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows={12}
                  placeholder="Enter the system prompt for this template version..."
                />
              </div>

              <SystemTokensSection />

              <div>
                <div className="mb-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Template Tokens</label>
                  {latestVersion && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Tokens will be inherited from version: {latestVersion}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  {inheritedTokens.length > 0 ? (
                    inheritedTokens.map(token => (
                      <div key={token.name} className="p-3 bg-gray-100 dark:bg-gray-600 rounded border opacity-75">
                        <div className="flex items-center gap-3">
                          <div className="font-medium text-sm text-gray-700 dark:text-gray-300">{token.name}</div>
                          <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/20 rounded text-xs font-medium text-blue-600 dark:text-blue-400">
                            {token.type}
                          </span>
                        </div>
                        {token.description && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{token.description}</div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-gray-500 dark:text-gray-400 p-3 bg-gray-100 dark:bg-gray-600 rounded border">
                      No tokens to inherit
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Dialog.Close asChild>
                  <button className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    Cancel
                  </button>
                </Dialog.Close>
                <button
                  onClick={handleCreateVersion}
                  className="px-3 py-2 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors"
                >
                  Create Version
                </button>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      {/* View Version Dialog */}
      <Dialog.Root open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <Dialog.Portal container={document.getElementById(portalContainerId) || undefined}>
          <Dialog.Overlay className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[100001]" />
          <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-6xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-100 dark:border-gray-700 z-[100002]">
            <Dialog.Title className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Template Version {selectedVersion?.version}
            </Dialog.Title>

            {selectedVersion && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span>
                    <div className="text-gray-600 dark:text-gray-400">{formatDate(selectedVersion.createdAt!)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Created By:</span>
                    <div className="text-gray-600 dark:text-gray-400">{selectedVersion.createdBy}</div>
                  </div>
                </div>

                <SystemTokensSection />

                <div>
                  <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Template Tokens:</h3>
                  <div className="space-y-2">
                    {selectedVersion.tokens?.map(token => (
                      <div key={token.name} className="p-3 bg-gray-50 dark:bg-gray-700 rounded border">
                        <div className="flex items-center gap-3">
                          <div className="font-medium text-sm text-gray-900 dark:text-white">{token.name}</div>
                          <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/20 rounded text-xs font-medium text-blue-600 dark:text-blue-400">
                            {token.type}
                          </span>
                        </div>
                        {token.description && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{token.description}</div>
                        )}
                      </div>
                    )) || <div className="text-sm text-gray-500 dark:text-gray-400">No tokens defined</div>}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">System Prompt:</h3>
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded border">
                    <pre className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap font-mono">
                      {selectedVersion.system || 'No system prompt'}
                    </pre>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Dialog.Close asChild>
                    <button className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                      Close
                    </button>
                  </Dialog.Close>
                </div>
              </div>
            )}
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  )
}
