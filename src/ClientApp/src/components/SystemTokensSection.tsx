'use client'

interface SystemTokensSectionProps {
  className?: string
}

export function SystemTokensSection({ className = '' }: SystemTokensSectionProps) {
  // Get current date/time for example
  const currentDateTime = new Date().toLocaleString('en-AU', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZoneName: 'short',
  })

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">System Tokens</label>
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
        These tokens are always available and automatically provided by the system
      </p>

      <div className="space-y-3">
        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Name</label>
              <div className="w-full px-2 py-1 bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded text-sm flex items-center gap-2">
                <span className="font-medium text-gray-700 dark:text-gray-300">{'CurrentDateTime'}</span>
              </div>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Type</label>
              <div className="w-full px-2 py-1 bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded text-sm text-gray-700 dark:text-gray-300">
                datetime
              </div>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Description</label>
              <div className="w-full px-2 py-1 bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded text-sm text-gray-700 dark:text-gray-300">
                Current date and time
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
