/* -------------------- Error Messages -------------------- */
import { ChatError, ERROR_CATEGORIES } from '../types/error.types'

export const AI_ERRORS: Record<string, ChatError> = {
  NETWORK_ERROR: {
    category: ERROR_CATEGORIES.NETWORK_ERROR,
    name: 'Network Error',
    message: 'Unable to connect to the server. Please check your connection and try again.',
    isRetryable: true,
  },
  RATE_LIMIT_ERROR: {
    category: ERROR_CATEGORIES.RATE_LIMIT_ERROR,
    name: 'Rate Limit Error',
    message: 'Too many requests. Please wait a moment before trying again.',
    isRetryable: true,
  },
  CONTEXT_LIMIT_ERROR: {
    category: ERROR_CATEGORIES.CONTEXT_LIMIT_ERROR,
    name: 'Session Length Error',
    message: 'Session too long. Please begin another session and try again.',
    isRetryable: false,
  },
  CONTENT_FILTER_ERROR: {
    category: ERROR_CATEGORIES.CONTENT_FILTER_ERROR,
    name: 'Content Filter Error',
    message: "I can't respond to that request. Please try rephrasing your message.",
    isRetryable: true,
  },
  INVALID_REQUEST: {
    category: ERROR_CATEGORIES.INVALID_REQUEST_ERROR,
    name: 'Invalid Request Error',
    message: 'Invalid request format. Please refresh the page and try again.',
    isRetryable: false,
  },
  AUTHENTICATION_ERROR: {
    category: ERROR_CATEGORIES.AUTHENTICATION_ERROR,
    name: 'Authentication Error',
    message: 'Your session has expired. Please refresh your page and sign in again.',
    isRetryable: false,
  },
  INVALID_TOOL_ARGUMENTS_ERROR: {
    category: ERROR_CATEGORIES.INVALID_TOOL_ARGUMENTS_ERROR,
    name: 'Invalid Tool Error',
    message: 'A tool has had an error. Please submit a support request, or contact us your admin regarding this issue.',
    isRetryable: false,
  },
} as const

export const TOKEN_LIMIT = 48000
export const CHARS_PER_TOKEN = 4
