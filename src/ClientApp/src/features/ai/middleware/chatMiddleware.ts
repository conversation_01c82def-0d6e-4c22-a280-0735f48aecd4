import { CHARS_PER_TOKEN, TOKEN_LIMIT } from '@/constants/error.constants'
import { trackTrace } from '@/lib/analytics/analytics-server'
import { ISearchResultDto } from '@/web-api-client'
import { LanguageModelV1Middleware, LanguageModelV1Prompt } from 'ai'
import { isSearchTool } from '../utils/toolsConfig.utils'

export function isAISearchResult(result: unknown): result is ISearchResultDto {
  if (typeof result !== 'object' || result === null) return false
  const obj = result as Record<string, unknown>
  return typeof obj.count === 'number' && Array.isArray(obj.results)
}

function estimateTokens(text: string) {
  return Math.ceil(text.length / CHARS_PER_TOKEN)
}

type PromptMessage = LanguageModelV1Prompt[number]
type ToolMessage = Extract<PromptMessage, { role: 'tool' }>
type ToolContent = ToolMessage['content']
type ToolResultPart = ToolContent[number]

function getValidPrompt(params: any): LanguageModelV1Prompt | null {
  if (!params.prompt || !Array.isArray(params.prompt)) {
    return null
  }
  return params.prompt as LanguageModelV1Prompt
}

/**
 * Calculates the total token count for a single message in the prompt.
 * Handles both string content and array-based content with multiple parts (text, tool-calls, tool-results).
 *
 * @param message - A single message from the LanguageModelV1Prompt
 * @returns The total estimated token count for the message
 */
function calculateMessageTokens(message: PromptMessage) {
  let tokens = 0

  if (message.content) {
    if (typeof message.content === 'string') {
      tokens += estimateTokens(message.content)
    } else if (Array.isArray(message.content)) {
      for (const part of message.content) {
        if (part.type === 'text' && 'text' in part) {
          tokens += estimateTokens(part.text)
        } else if (part.type === 'tool-call') {
          tokens += estimateTokens(JSON.stringify(part))
        } else if (part.type === 'tool-result') {
          tokens += estimateTokens(JSON.stringify(part.result))
        }
      }
    }
  }

  return tokens
}

/**
 * Type guard to check if a message is a search tool with results
 * @param message - The message to check
 * @returns True if the message is a tool message with search tool content that has actual results
 */
export function isSearchToolWithResults(message: PromptMessage) {
  if (message.role !== 'tool' || !Array.isArray(message.content)) {
    return false
  }

  return message.content.some(part => {
    if (part.type !== 'tool-result' || !isSearchTool(part.toolName)) {
      return false
    }

    return isAISearchResult(part.result) && part.result.results.length > 0
  })
}

export function truncateAISearchResult(searchResult: ISearchResultDto, maxTokensToReduce: number) {
  const results = [...searchResult.results]
  let tokensRemoved = 0

  // Remove from the end (least relevant results first)
  while (results.length > 0 && tokensRemoved < maxTokensToReduce) {
    const lastResult = results[results.length - 1]
    const resultTokens = estimateTokens(JSON.stringify(lastResult))

    if (tokensRemoved + resultTokens <= maxTokensToReduce) {
      results.pop()
      tokensRemoved += resultTokens
    } else {
      break
    }
  }

  return {
    truncated: {
      ...searchResult,
      count: results.length,
      results,
    },
    tokensRemoved,
  }
}

/**
 * Truncates AI search results within a single tool message.
 * Divides available tokens equally among all search results in the message.
 */
export function truncateToolMessage(message: ToolMessage, maxTokensToReduce: number) {
  // First, count how many AI search results we have
  const aiSearchCount = message.content.filter(
    part => part.type === 'tool-result' && isSearchTool(part.toolName) && isAISearchResult(part.result),
  ).length

  if (aiSearchCount === 0) {
    return null
  }

  // Divide tokens equally among all AI search results
  const tokensPerSearch = Math.floor(maxTokensToReduce / aiSearchCount)
  let totalTokensRemoved = 0

  const processedContent = message.content.map(part => {
    if (part.type !== 'tool-result' || !isSearchTool(part.toolName)) {
      return part
    }

    if (!isAISearchResult(part.result)) {
      return part
    }

    const { truncated, tokensRemoved } = truncateAISearchResult(part.result, tokensPerSearch)
    totalTokensRemoved += tokensRemoved

    return {
      ...part,
      result: truncated,
    }
  })

  if (totalTokensRemoved === 0) {
    return null
  }

  return {
    message: { ...message, content: processedContent },
    tokensRemoved: totalTokensRemoved,
  }
}

/**
 * Truncates AI search results from tool messages to reduce token usage.
 * Processes messages chronologically - earlier searches (broader) are truncated first,
 * preserving later searches (more targeted). Within each message, divides tokens
 * equally among multiple search results.
 */
export function truncateSearchResultsFromPrompt(
  prompt: LanguageModelV1Prompt,
  targetReduction: number,
): LanguageModelV1Prompt {
  const result = prompt.reduce(
    // For sequential search results, we want to process the last message first, ensureing the most recent search results are preserved
    (acc, message) => {
      if (acc.tokensReduced >= targetReduction) {
        return { ...acc, prompt: [...acc.prompt, message] }
      }

      if (!isSearchToolWithResults(message)) {
        return { ...acc, prompt: [...acc.prompt, message] }
      }

      const remainingReduction = targetReduction - acc.tokensReduced
      const truncationResult = truncateToolMessage(message as ToolMessage, remainingReduction)

      if (!truncationResult) {
        return { ...acc, prompt: [...acc.prompt, message] }
      }

      return {
        prompt: [...acc.prompt, truncationResult.message],
        tokensReduced: acc.tokensReduced + truncationResult.tokensRemoved,
      }
    },
    { prompt: [] as LanguageModelV1Prompt, tokensReduced: 0 },
  )
  return result.prompt
}

/**
 * Identifies the start of the current conversation turn by finding the last user message.
 * A turn includes: user message → assistant tool calls → tool results → assistant response.
 *
 * @param prompt - The complete prompt array
 * @returns The index of the last user message, or -1 if no user message found
 */
function findCurrentTurnStart(prompt: LanguageModelV1Prompt) {
  for (let i = prompt.length - 1; i >= 0; i--) {
    if (prompt[i].role === 'user') {
      return i
    }
  }
  return -1
}

/**
 * Middleware that handles historical context from previous conversation turns.
 * Filters out AI search tool results from all messages before the current turn
 * to prevent stale citation markers and reduce token usage from historical context.
 *
 * Historical context = all messages before the current turn starts.
 */
export const previousTurnMiddleware: LanguageModelV1Middleware = {
  transformParams: async ({ params }) => {
    const prompt = getValidPrompt(params)
    if (!prompt) return params

    const currentTurnStart = findCurrentTurnStart(prompt)

    const filteredPrompt: LanguageModelV1Prompt = prompt.map((message, index) => {
      if (index >= currentTurnStart) {
        return message
      }

      if (message.role !== 'tool' || !Array.isArray(message.content)) {
        return message
      }

      const toolMessage = message as ToolMessage
      const processedContent: ToolContent = toolMessage.content.map((part: ToolResultPart) => {
        if (part.type === 'tool-result' && isSearchTool(part.toolName)) {
          return {
            ...part,
            result: [],
          }
        }

        return part
      })

      return {
        ...toolMessage,
        content: processedContent,
      }
    })

    return {
      ...params,
      prompt: filteredPrompt,
    }
  },
}

/**
 * Middleware that handles the current conversation turn, which may contain multiple steps.
 * A turn starts with the last user message and includes all subsequent assistant tool calls,
 * tool results, and responses. This middleware specifically manages large AI search results
 * that can exceed 400k tokens within the current turn.
 *
 * Key behaviors:
 * - Only processes messages within the current turn (preserves all historical context)
 * - Examines ALL steps in the current turn for ai_search tools
 * - Implements intelligent truncation of search results from the end going up
 * - Maintains the integrity of the current agent conversation flow
 */
export const currentTurnMiddleware: LanguageModelV1Middleware = {
  transformParams: async ({ params }) => {
    const prompt = getValidPrompt(params)
    if (!prompt) return params

    const promptCopy = [...prompt]

    const currentTurnStart = findCurrentTurnStart(promptCopy)

    let totalTokens = 0
    for (const message of promptCopy) {
      totalTokens += calculateMessageTokens(message)
    }

    if (totalTokens <= TOKEN_LIMIT) {
      return params
    }

    const tokensToReduce = totalTokens - TOKEN_LIMIT

    const currentTurnMessages = promptCopy.slice(currentTurnStart)

    const truncatedCurrentTurn = truncateSearchResultsFromPrompt(currentTurnMessages, tokensToReduce)

    const historicalMessages = promptCopy.slice(0, currentTurnStart)
    const finalPrompt = [...historicalMessages, ...truncatedCurrentTurn]

    trackTrace('Search result tokens truncated', {
      message_count: params.prompt.length,
      tokens_truncated: tokensToReduce,
      token_limit: TOKEN_LIMIT,
      total_tokens: totalTokens,
    })

    return {
      ...params,
      prompt: finalPrompt,
    }
  },
}
