import { registry } from '@/lib/registry'
import { createServerApiClient } from '@/lib/utils/apiUtils.server'
import { convertUtcTimestampsToLocal } from '@/lib/utils/timezone.utils'
import { MinisClient, ToolDto } from '@/web-api-client'
import { LanguageModelV1, ToolSet, wrapLanguageModel } from 'ai'
import { currentTurnMiddleware, previousTurnMiddleware } from '../middleware/chatMiddleware'
import { createToolSet } from '../tools'

export interface ChatConfiguration {
  model: LanguageModelV1
  tools: ToolSet
  system: string
  maxSteps: number
  toolCallStreaming: boolean
}

export async function getChatConfiguration(miniId: string, timezone?: string): Promise<ChatConfiguration> {
  const { modelId, tools, system } = await getMiniConfig(miniId, timezone)
  const modelName = `azure:${modelId.replace(/^azure:/, '')}` as const

  const model = wrapLanguageModel({
    model: registry.languageModel(modelName),
    middleware: [previousTurnMiddleware, currentTurnMiddleware],
  })

  const toolSet = createToolSet(tools, miniId)

  return {
    model,
    tools: toolSet,
    system,
    maxSteps: 5,
    toolCallStreaming: true,
  }
}

/**
 * Configuration for a mini
 */
interface MiniConfig {
  /**
   * The model ID to use for the mini (e.g., "azure:gpt-4o")
   */
  modelId: string

  /**
   * The system prompt to use for the mini
   */
  system: string

  /**
   * The tools to use for the mini
   */
  tools: ToolDto[]
}

/**
 * Gets the configuration for a given mini ID
 * @param miniId The mini ID
 * @param timezone Optional timezone for converting UTC timestamps to local time
 * @returns The mini configuration
 */
async function getMiniConfig(miniId: string, timezone?: string): Promise<MiniConfig> {
  console.log(`Getting mini config for mini ID: ${miniId}`)
  const client = await createServerApiClient(MinisClient)
  const mini = await client.getSlimMini(Number(miniId))

  // Get the base system prompt
  let systemPrompt = mini.instructions || 'You are a helpful assistant.'

  // Convert UTC timestamps to user's local timezone if timezone is provided
  if (timezone) {
    systemPrompt = convertUtcTimestampsToLocal(systemPrompt, timezone)
  }

  return {
    modelId: `azure:${process.env.AZURE_OPENAI_DEPLOYMENT_NAME}`,
    system: systemPrompt,
    tools: mini.tools,
  }
}
