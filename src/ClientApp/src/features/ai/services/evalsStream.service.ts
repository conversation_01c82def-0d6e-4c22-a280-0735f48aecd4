import { createChat } from '@/features/chat/api/chat.api'
import { mapChatMessagesToUIMessages } from '@/features/chat/mappers/chat.mapper'
import { trackException, trackTrace } from '@/lib/analytics/analytics-server'
import { createServerApiClient } from '@/lib/utils/apiUtils.server'
import { ChatsClient } from '@/web-api-client'
import { appendResponseMessages, Message, streamText, UIMessage } from 'ai'
import { ChatConfiguration } from './chatCore.service'

/* -------------------- Types -------------------- */
export interface TokenUsage {
  promptTokens: number
  completionTokens: number
  totalTokens: number
}

export interface CreateChatResponse {
  chatId?: string
  success: boolean
  error?: string
}

export interface SendMessageResponse {
  message: Message
  success: boolean
  usage?: TokenUsage
  error?: string
}

export interface GetMessagesResponse {
  messages: UIMessage[]
  success: boolean
  error?: string
}

/* -------------------- Service Functions -------------------- */

/**
 * Creates a new chat for evaluation purposes
 */
export async function createEvalChat(request: {
  miniId: number
  firstMessageContent: string
}): Promise<CreateChatResponse> {
  try {
    const result = await createChat(request.miniId.toString(), request.firstMessageContent)

    if (result.chatId) {
      return {
        chatId: result.chatId,
        success: true,
      }
    } else {
      return {
        success: false,
        error: result.error || 'Failed to create chat',
      }
    }
  } catch (error) {
    if (error instanceof Error) {
      trackException(error, { route: 'api/evals/create-chat' })
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : JSON.stringify(error),
    }
  }
}

/**
 * Processes a chat message and returns the complete response (non-streaming)
 */
export async function processNonStreamingChat(
  config: ChatConfiguration,
  messages: UIMessage[],
  chatId: string,
  miniId: number,
): Promise<SendMessageResponse> {
  try {
    trackTrace('Evals API received request:', { chatId, miniId, messageCount: messages.length })

    let capturedUsage: TokenUsage | undefined

    const { model, tools, system, maxSteps, toolCallStreaming } = config

    const result = await streamText({
      model,
      tools,
      system,
      messages: [...messages] as UIMessage[],
      toolCallStreaming,
      maxSteps,
      onFinish: async final => {
        if (final.usage) {
          capturedUsage = {
            promptTokens: final.usage.promptTokens,
            completionTokens: final.usage.completionTokens,
            totalTokens: final.usage.totalTokens,
          }
        }
      },
      onError: (error: unknown) => {
        trackException(error as Error, { route: 'api/evals/send-message', chatId, miniId })
        throw error
      },
    })

    // Collect the full response to ensure the stream is consumed
    let fullResponse = ''
    for await (const chunk of result.textStream) {
      fullResponse += chunk
    }

    // Wait for the result to complete to ensure onFinish has been called
    const final = await result.response
    const lastUserMessage = [messages[messages.length - 1]]

    const userAndAssistantMessages = appendResponseMessages({
      messages: lastUserMessage,
      responseMessages: final.messages,
    })

    return {
      message: userAndAssistantMessages[1],
      success: true,
      usage: capturedUsage,
    }
  } catch (error) {
    if (error instanceof Error) {
      trackException(error, { route: 'api/evals/send-message', chatId, miniId })
    }

    return {
      message: null as any,
      success: false,
      error: error instanceof Error ? error.message : JSON.stringify(error),
    }
  }
}

/**
 * Gets ALL messages for a chat (including system messages) for evaluation purposes
 */
export async function getEvalMessages(chatId: string): Promise<GetMessagesResponse> {
  if (!chatId || typeof chatId !== 'string') {
    return {
      messages: [],
      success: false,
      error: 'Invalid chat ID',
    }
  }

  try {
    trackTrace('Getting eval messages:', { chatId })

    const client = await createServerApiClient(ChatsClient)
    const response = await client.getApiChatsMessages(chatId, undefined, 1, 100, true)

    // Convert to UI messages but keep ALL messages (including system)
    const allMessages = response.items.reverse()
    const uiMessages = mapChatMessagesToUIMessages(allMessages)

    trackTrace('Retrieved eval messages:', { chatId, messageCount: uiMessages.length })

    return {
      messages: uiMessages,
      success: true,
    }
  } catch (error) {
    if (error instanceof Error) {
      trackException(error, { route: 'api/evals/get-messages', chatId })
    }
    return {
      messages: [],
      success: false,
      error: error instanceof Error ? error.message : JSON.stringify(error),
    }
  }
}
