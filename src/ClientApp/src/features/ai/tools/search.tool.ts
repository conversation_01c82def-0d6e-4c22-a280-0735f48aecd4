import { createServerApiClient } from '@/lib/utils/apiUtils.server'
import { SearchClient, SearchHybridRequest, ToolDto } from '@/web-api-client'
import { tool } from 'ai'
import { augmentSearchResults } from '../mappers/id.mapper'
import { jsonSchemaToZod } from './schema.mapper'

/**
 * Creates a search tool from a ToolDto with dynamic schema support
 * Handles AI-powered vector search with document caching for citations
 */
export function createSearchTool(toolDto: ToolDto, miniId: string) {
  const { name, description, schema: schemaString } = toolDto

  // Parse and convert schema dynamically
  const jsonSchema = JSON.parse(schemaString)
  const zodSchema = jsonSchemaToZod(jsonSchema)

  return tool({
    description: description,
    parameters: zodSchema,
    execute: async (parameters, { toolCallId }) => {
      const searchClient = await createServerApiClient(SearchClient)

      const request = new SearchHybridRequest({
        query: parameters.query,
        targetDateStart: parameters.targetDateStart,
        targetDateEnd: parameters.targetDateEnd,
        top: parameters.top || 20,
        miniId: String(miniId),
      })

      const response = await searchClient.searchHybrid(request)

      if (response.results) {
        // Augment search results and add display IDs
        const augmentedResults = augmentSearchResults(response.results)
        const resultsWithDisplayIds = augmentedResults.map((doc, idx) => {
          const displayId = String(idx + 1)

          return {
            ...doc,
            displayId,
          }
        })

        return {
          ...response,
          results: resultsWithDisplayIds,
        }
      }

      return response
    },
  })
}
