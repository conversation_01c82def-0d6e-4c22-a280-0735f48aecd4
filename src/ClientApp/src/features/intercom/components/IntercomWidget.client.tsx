'use client'

import Intercom, { shutdown } from '@intercom/messenger-js-sdk'
import { useSession } from 'next-auth/react'
import { useEffect, useRef } from 'react'
import { IntercomConfigResponse } from '../.types'

export const IntercomWidget = () => {
  const { data: session } = useSession()
  const intercomInitialized = useRef(false)
  const lastUserEmail = useRef<string | null>(null)

  // Function to fetch Intercom configuration and JWT
  const fetchIntercomConfig = async (): Promise<IntercomConfigResponse | null> => {
    try {
      const response = await fetch(`/api/intercom/config`)
      if (!response.ok) {
        console.error('Failed to fetch Intercom config:', response.status, response.statusText)
        return null
      }

      const data: IntercomConfigResponse = await response.json()
      return data
    } catch (error) {
      console.error('Error fetching Intercom config:', error)
      return null
    }
  }

  // Handle Intercom initialization and user session changes
  useEffect(() => {
    const handleSessionChange = async () => {
      try {
        const currentUserEmail = session?.user?.email || null

        // If user logged out (had email before, now doesn't), shutdown Intercom
        if (lastUserEmail.current && !currentUserEmail && intercomInitialized.current) {
          shutdown()
          intercomInitialized.current = false
          lastUserEmail.current = null
          console.log('Intercom shutdown due to user logout')
          return
        }

        // If user changed (different email), shutdown and reinitialize
        if (
          lastUserEmail.current &&
          currentUserEmail &&
          lastUserEmail.current !== currentUserEmail &&
          intercomInitialized.current
        ) {
          shutdown()
          intercomInitialized.current = false
          lastUserEmail.current = null
          console.log('Intercom shutdown due to user change')
        }

        // If already initialized for the same user, don't reinitialize
        if (intercomInitialized.current && lastUserEmail.current === currentUserEmail) {
          return
        }

        // Only initialize if we have a user session
        if (!session?.user) {
          return
        }

        // Fetch Intercom configuration from server
        const config = await fetchIntercomConfig()
        if (!config) {
          console.warn('Failed to fetch Intercom configuration - widget will not initialize')
          return
        }

        // Prepare Intercom settings
        const intercomSettings = {
          app_id: config.app_id,
          region: 'ap', // Australia/Asia-Pacific region
          intercom_user_jwt: config.jwt,
        } as any

        // Initialize Intercom
        Intercom(intercomSettings)
        intercomInitialized.current = true
        lastUserEmail.current = currentUserEmail

        console.log('Intercom initialized successfully for user:', currentUserEmail)
      } catch (error) {
        console.error('Failed to handle Intercom session change:', error)
      }
    }

    handleSessionChange()
  }, [session])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intercomInitialized.current) {
        try {
          shutdown()
          intercomInitialized.current = false
          lastUserEmail.current = null
        } catch (error) {
          console.error('Failed to shutdown Intercom on unmount:', error)
        }
      }
    }
  }, [])

  return null // The SDK handles rendering the widget
}
