import {
  CreateMiniProfileRequest,
  CreateMiniProfileValueRequest,
  MinisClient,
  MiniTemplatesClient,
  MiniTemplateVersionDto,
} from '@/web-api-client'

/**
 * Get the latest template version with tokens for a given template
 */
export async function getLatestTemplateVersion(templateId: string): Promise<MiniTemplateVersionDto> {
  const client = new MiniTemplatesClient()
  return await client.getLatestMiniTemplateVersion(templateId)
}

/**
 * Create a Mini Profile with the provided token values
 */
export async function createMiniProfile(miniId: number, values: Array<{ name: string; value: string }>): Promise<void> {
  const client = new MinisClient()

  const request = new CreateMiniProfileRequest({
    values: values.map(
      v =>
        new CreateMiniProfileValueRequest({
          name: v.name,
          value: v.value,
        }),
    ),
  })

  return await client.createMiniProfile(miniId, request)
}

/**
 * Create a Mini Profile with the Name token populated from the Mini name
 * and any other required tokens with default values
 */
export async function createMiniProfileWithName(miniId: number, miniName: string, templateId: string): Promise<void> {
  try {
    // Get the latest template version to understand required tokens
    const templateVersion = await getLatestTemplateVersion(templateId)

    // Prepare token values
    const values: Array<{ name: string; value: string }> = []

    // Process each token from the template
    for (const token of templateVersion.tokens || []) {
      if (token.name === 'Name') {
        // Use the Mini's name for the Name token
        values.push({ name: token.name, value: miniName })
      } else {
        // For other tokens, provide a default value based on type
        let defaultValue = ''
        switch (token.type) {
          case 'string':
            defaultValue = `[${token.name}]` // Placeholder format
            break
          case 'number':
          case 'integer':
            defaultValue = '0'
            break
          case 'boolean':
            defaultValue = 'false'
            break
          case 'array':
            defaultValue = '[]'
            break
          case 'object':
            defaultValue = '{}'
            break
          default:
            defaultValue = `[${token.name}]`
        }
        values.push({ name: token.name, value: defaultValue })
      }
    }

    // Create the Mini Profile
    await createMiniProfile(miniId, values)

    console.log(`Mini Profile created successfully for Mini ${miniId} with Name: "${miniName}"`)
  } catch (error) {
    // Log error but don't throw - we don't want to block Mini creation
    console.error('Failed to create Mini Profile:', error)
    throw error // Re-throw so caller can handle appropriately
  }
}
