import { prefetchCopyLibs } from '@/features/chat/utils/copy.utils'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useMediaQuery } from './useMediaQuery'

/**
 * Manages copy button visibility with responsive behavior.
 * Desktop: shows on hover with debounced hiding. Mobile: shows on tap with auto-hide.
 * Prefetches copy libraries when button becomes visible for better performance.
 * @returns Visibility state and handlers for show/hide behavior
 */
export function useCopyButtonVisibility() {
  const MOBILE_AUTO_HIDE_DELAY = 10000 // Auto-hide after 10 seconds on mobile
  const DESKTOP_DEBOUNCE_DELAY = 1000 // Grace period for cursor movement on desktop
  const [showCopyButton, setShowCopyButton] = useState(false)
  const isMobile = useMediaQuery('(max-width: 767px)')
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const clearHideTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [])

  const scheduleHide = useCallback(
    (delay: number) => {
      clearHideTimeout()
      timeoutRef.current = setTimeout(() => {
        setShowCopyButton(false)
      }, delay)
    },
    [clearHideTimeout],
  )

  const handleShowCopyButton = useCallback(() => {
    clearHideTimeout()
    setShowCopyButton(true)
    prefetchCopyLibs()

    // Mobile: auto-hide after delay, Desktop: stays visible until hover ends
    if (isMobile) {
      scheduleHide(MOBILE_AUTO_HIDE_DELAY)
    }
  }, [isMobile, clearHideTimeout, scheduleHide, MOBILE_AUTO_HIDE_DELAY])

  const handleHideCopyButton = useCallback(() => {
    // Mobile uses auto-hide timer set in handleShowCopyButton, no manual hiding needed
    if (isMobile) return

    // Desktop: delay hiding to allow cursor movement from message to copy button
    scheduleHide(DESKTOP_DEBOUNCE_DELAY)
  }, [isMobile, scheduleHide, DESKTOP_DEBOUNCE_DELAY])

  useEffect(() => {
    return clearHideTimeout
  }, [clearHideTimeout])

  return { showCopyButton, handleShowCopyButton, handleHideCopyButton, isMobile }
}
