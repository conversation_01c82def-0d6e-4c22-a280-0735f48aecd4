import { convertMarkdownToHTML } from '@/features/chat/utils/copy.utils'
import { trackEvent } from '@/lib/analytics/analytics-client'
import { useCallback, useState } from 'react'
import toast from 'react-hot-toast'

interface CopyOptions {
  messageRole?: 'user' | 'assistant'
  toolName?: string | null
}

export function useCopyToClipboard() {
  const [copied, setCopied] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  const copyToClipboard = useCallback(async (textToCopy: string, options?: CopyOptions) => {
    setIsAnimating(true)
    try {
      const htmlPromise = convertMarkdownToHTML(textToCopy)

      const clipboardItems: ClipboardItem[] = [
        new ClipboardItem({
          'text/html': new Promise(async resolve => {
            const html = await htmlPromise
            resolve(new Blob([html || textToCopy], { type: 'text/html' }))
          }),
          'text/plain': new Blob([textToCopy], { type: 'text/plain' }),
        }),
      ]

      navigator.clipboard.write(clipboardItems)
      setCopied(true)

      toast.success('Message copied to clipboard')

      trackEvent('Message Copy', {
        message_length: textToCopy.length,
        message_role: options?.messageRole,
        tool_name: options?.toolName,
        has_citations: textToCopy.includes('[['),
      })
    } catch (error) {
      toast.error('Failed to copy message. Please try selecting and copying manually.')

      trackEvent('Message Copy Error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        message_length: textToCopy.length,
        message_role: options?.messageRole,
        tool_name: options?.toolName,
      })

      setCopied(false)
    } finally {
      setTimeout(() => {
        setCopied(false)
        setIsAnimating(false)
      }, 2000) // Animation duration
    }
  }, [])

  return { copied, isAnimating, copyToClipboard }
}
