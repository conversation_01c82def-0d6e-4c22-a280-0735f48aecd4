'use client'

import { useCallback, useRef, useState } from 'react'
import toast from 'react-hot-toast'
import { useGroup } from './useGroup'

export interface FileUploadProgress {
  fileName: string
  percentComplete: number
  bytesUploaded: number
  totalBytes: number
  isComplete: boolean
  isUploading: boolean
  isCanceled: boolean
  hasFailed: boolean
  xhr?: XMLHttpRequest
}

export interface FilesUploadProgress {
  fileProgresses: Record<string, FileUploadProgress>
  overallPercentComplete: number
  filesCompleted: number
  totalFiles: number
  canceledFiles: number
  failedFiles: number
}

export class FileUploadError extends Error {
  constructor(
    message: string,
    public status?: number,
    public statusText?: string,
  ) {
    super(message)
    this.name = 'FileUploadError'
  }
}

export interface FileUploadOptions {
  /**
   * Whether to show progress notifications
   * @default true
   */
  showProgress?: boolean

  /**
   * Callback for progress updates
   */
  onProgress?: (progress: FilesUploadProgress) => void

  /**
   * Callback for when the upload is complete
   */
  onComplete?: () => void

  /**
   * Callback for when a file upload is canceled
   */
  onCancel?: (fileName: string) => void
}

export function useFileUpload() {
  const { group } = useGroup()
  const [uploadProgress, setUploadProgress] = useState<Record<string, FilesUploadProgress>>({})
  const xhrMap = useRef<Map<string, XMLHttpRequest>>(new Map())
  const fileObjectsRef = useRef<Map<string, File>>(new Map())

  // Function to upload a single file
  const uploadSingleFile = useCallback(
    (file: File, uploadId: string, miniId?: string, options?: FileUploadOptions): Promise<Response> => {
      return new Promise((resolve, reject) => {
        if (!group?.groupId) {
          reject(new Error('No group selected'))
          return
        }

        const xhr = new XMLHttpRequest()
        const formData = new FormData()

        // Add file to form data
        formData.append('files', file)

        // Store the XHR in the map for potential cancellation
        xhrMap.current.set(`${uploadId}-${file.name}`, xhr)

        // Update the file progress to indicate it's uploading
        setUploadProgress(prev => {
          const currentProgress = prev[uploadId] || {
            fileProgresses: {},
            overallPercentComplete: 0,
            filesCompleted: 0,
            totalFiles: 1,
            canceledFiles: 0,
          }

          const updatedFileProgresses = { ...currentProgress.fileProgresses }
          updatedFileProgresses[file.name] = {
            fileName: file.name,
            percentComplete: 0,
            bytesUploaded: 0,
            totalBytes: file.size,
            isComplete: false,
            isUploading: true,
            isCanceled: false,
            hasFailed: false,
            xhr,
          }

          // Store the file object for potential retry
          fileObjectsRef.current.set(`${uploadId}-${file.name}`, file)

          return {
            ...prev,
            [uploadId]: {
              ...currentProgress,
              fileProgresses: updatedFileProgresses,
            },
          }
        })

        // Track progress
        xhr.upload.addEventListener('progress', event => {
          if (event.lengthComputable) {
            const percentComplete = Math.round((event.loaded / event.total) * 100)

            setUploadProgress(prev => {
              const currentProgress = prev[uploadId]
              if (!currentProgress) return prev

              // Update just this file's progress
              const updatedFileProgresses = { ...currentProgress.fileProgresses }
              const fileProgress = updatedFileProgresses[file.name]

              if (fileProgress && !fileProgress.isCanceled) {
                updatedFileProgresses[file.name] = {
                  ...fileProgress,
                  percentComplete,
                  bytesUploaded: Math.round((file.size * percentComplete) / 100),
                }
              }

              // Calculate overall progress
              const totalBytes = Object.values(updatedFileProgresses).reduce((sum, file) => sum + file.totalBytes, 0)
              const uploadedBytes = Object.values(updatedFileProgresses).reduce(
                (sum, file) => sum + file.bytesUploaded,
                0,
              )
              const overallPercentComplete = totalBytes > 0 ? Math.round((uploadedBytes / totalBytes) * 100) : 0

              return {
                ...prev,
                [uploadId]: {
                  ...currentProgress,
                  fileProgresses: updatedFileProgresses,
                  overallPercentComplete,
                },
              }
            })
          }
        })

        // Handle completion
        xhr.addEventListener('load', () => {
          // Remove the XHR from the map
          xhrMap.current.delete(`${uploadId}-${file.name}`)

          if (xhr.status >= 200 && xhr.status < 300) {
            // Update progress to complete
            setUploadProgress(prev => {
              const currentProgress = prev[uploadId]
              if (!currentProgress) return prev

              const updatedFileProgresses = { ...currentProgress.fileProgresses }
              const fileProgress = updatedFileProgresses[file.name]

              if (fileProgress && !fileProgress.isCanceled) {
                updatedFileProgresses[file.name] = {
                  ...fileProgress,
                  percentComplete: 100,
                  bytesUploaded: file.size,
                  isComplete: true,
                  isUploading: false,
                }
              }

              const filesCompleted = Object.values(updatedFileProgresses).filter(f => f.isComplete).length
              const canceledFiles = Object.values(updatedFileProgresses).filter(f => f.isCanceled).length
              const totalFiles = Object.keys(updatedFileProgresses).length

              // Recalculate overall progress
              const totalBytes = Object.values(updatedFileProgresses).reduce(
                (sum, file) => sum + (file.isCanceled ? 0 : file.totalBytes),
                0,
              )
              const uploadedBytes = Object.values(updatedFileProgresses).reduce(
                (sum, file) => sum + file.bytesUploaded,
                0,
              )
              const overallPercentComplete = totalBytes > 0 ? Math.round((uploadedBytes / totalBytes) * 100) : 100

              const failedFiles = Object.values(updatedFileProgresses).filter(f => f.hasFailed).length

              const updatedProgress = {
                fileProgresses: updatedFileProgresses,
                overallPercentComplete,
                filesCompleted,
                totalFiles,
                canceledFiles,
                failedFiles,
              }

              // Call onProgress callback if provided
              options?.onProgress?.(updatedProgress)

              // Call onComplete if all files are done (completed, canceled, or failed)
              if (filesCompleted + canceledFiles + failedFiles === totalFiles) {
                options?.onComplete?.()
              }

              return {
                ...prev,
                [uploadId]: updatedProgress,
              }
            })

            resolve(
              new Response(xhr.response, {
                status: xhr.status,
                statusText: xhr.statusText,
              }),
            )
          } else {
            // Update progress to indicate error
            setUploadProgress(prev => {
              const currentProgress = prev[uploadId]
              if (!currentProgress) return prev

              const updatedFileProgresses = { ...currentProgress.fileProgresses }
              const fileProgress = updatedFileProgresses[file.name]

              if (fileProgress) {
                updatedFileProgresses[file.name] = {
                  ...fileProgress,
                  isUploading: false,
                  hasFailed: true,
                }
              }

              const filesCompleted = Object.values(updatedFileProgresses).filter(f => f.isComplete).length
              const canceledFiles = Object.values(updatedFileProgresses).filter(f => f.isCanceled).length
              const failedFiles = Object.values(updatedFileProgresses).filter(f => f.hasFailed).length
              const totalFiles = Object.keys(updatedFileProgresses).length

              // Call onComplete if all files are done (completed, canceled, or failed)
              if (filesCompleted + canceledFiles + failedFiles === totalFiles) {
                options?.onComplete?.()
              }

              return {
                ...prev,
                [uploadId]: {
                  ...currentProgress,
                  fileProgresses: updatedFileProgresses,
                  failedFiles: (currentProgress.failedFiles || 0) + 1,
                },
              }
            })

            reject(
              new FileUploadError(
                `Failed to upload file ${file.name}: ${xhr.status} ${xhr.statusText}`,
                xhr.status,
                xhr.statusText,
              ),
            )
          }
        })

        // Handle errors
        xhr.addEventListener('error', () => {
          // Remove the XHR from the map
          xhrMap.current.delete(`${uploadId}-${file.name}`)

          // Update progress to indicate error
          setUploadProgress(prev => {
            const currentProgress = prev[uploadId]
            if (!currentProgress) return prev

            const updatedFileProgresses = { ...currentProgress.fileProgresses }
            const fileProgress = updatedFileProgresses[file.name]

            if (fileProgress) {
              updatedFileProgresses[file.name] = {
                ...fileProgress,
                isUploading: false,
                hasFailed: true,
              }
            }

            const filesCompleted = Object.values(updatedFileProgresses).filter(f => f.isComplete).length
            const canceledFiles = Object.values(updatedFileProgresses).filter(f => f.isCanceled).length
            const failedFiles = Object.values(updatedFileProgresses).filter(f => f.hasFailed).length
            const totalFiles = Object.keys(updatedFileProgresses).length

            // Call onComplete if all files are done (completed, canceled, or failed)
            if (filesCompleted + canceledFiles + failedFiles === totalFiles) {
              options?.onComplete?.()
            }

            return {
              ...prev,
              [uploadId]: {
                ...currentProgress,
                fileProgresses: updatedFileProgresses,
                failedFiles: (currentProgress.failedFiles || 0) + 1,
              },
            }
          })

          reject(new FileUploadError(`Network error occurred during upload of ${file.name}`))
        })

        xhr.addEventListener('abort', () => {
          // Remove the XHR from the map
          xhrMap.current.delete(`${uploadId}-${file.name}`)

          // Update progress to indicate cancellation
          setUploadProgress(prev => {
            const currentProgress = prev[uploadId]
            if (!currentProgress) return prev

            const updatedFileProgresses = { ...currentProgress.fileProgresses }
            const fileProgress = updatedFileProgresses[file.name]

            if (fileProgress) {
              updatedFileProgresses[file.name] = {
                ...fileProgress,
                isUploading: false,
                isCanceled: true,
              }
            }

            const filesCompleted = Object.values(updatedFileProgresses).filter(f => f.isComplete).length
            const canceledFiles = Object.values(updatedFileProgresses).filter(f => f.isCanceled).length
            const totalFiles = Object.keys(updatedFileProgresses).length

            // Call onCancel callback if provided
            options?.onCancel?.(file.name)

            const failedFiles = Object.values(updatedFileProgresses).filter(f => f.hasFailed).length

            // Call onComplete if all files are done (completed, canceled, or failed)
            if (filesCompleted + canceledFiles + failedFiles === totalFiles) {
              options?.onComplete?.()
            }

            return {
              ...prev,
              [uploadId]: {
                ...currentProgress,
                fileProgresses: updatedFileProgresses,
                canceledFiles,
              },
            }
          })

          reject(new FileUploadError(`Upload of ${file.name} was canceled`))
        })

        // Open and send the request
        const url = `/api/Files?groupId=${group.groupId}&miniId=${miniId}`
        xhr.open('POST', url)
        xhr.send(formData)
      })
    },
    [group?.groupId],
  )

  // Function to cancel a file upload
  const cancelFileUpload = useCallback((uploadId: string, fileName: string) => {
    const xhrKey = `${uploadId}-${fileName}`
    const xhr = xhrMap.current.get(xhrKey)

    if (xhr) {
      xhr.abort()
      xhrMap.current.delete(xhrKey)
      return true
    }

    return false
  }, [])

  // Function to upload multiple files
  const uploadFiles = useCallback(
    (
      files: File[],
      miniId?: string,
      options?: FileUploadOptions,
    ): { promise: Promise<Response[]>; uploadId: string } => {
      // Generate a unique ID for this upload batch
      const uploadId = `upload-${Date.now()}`

      // Initialize progress tracking for all files
      const fileProgresses: Record<string, FileUploadProgress> = {}
      files.forEach(file => {
        fileProgresses[file.name] = {
          fileName: file.name,
          percentComplete: 0,
          bytesUploaded: 0,
          totalBytes: file.size,
          isComplete: false,
          isUploading: false,
          isCanceled: false,
          hasFailed: false,
        }
      })

      const initialProgress: FilesUploadProgress = {
        fileProgresses,
        overallPercentComplete: 0,
        filesCompleted: 0,
        totalFiles: files.length,
        canceledFiles: 0,
        failedFiles: 0,
      }

      setUploadProgress(prev => ({
        ...prev,
        [uploadId]: initialProgress,
      }))

      // Upload each file individually
      const uploadPromises = files.map(file =>
        uploadSingleFile(file, uploadId, miniId, options).catch(error => {
          console.error(`Error uploading ${file.name}:`, error)
          return new Response(null, { status: 500, statusText: error.message })
        }),
      )

      const promise = Promise.all(uploadPromises)

      return { promise, uploadId }
    },
    [uploadSingleFile],
  )

  // Enhanced upload function with error handling
  const upload = useCallback(
    async (files: File[], miniId?: string, options?: FileUploadOptions) => {
      try {
        const { promise, uploadId } = uploadFiles(files, miniId, options)
        return { response: await promise, uploadId }
      } catch (error) {
        if (error instanceof FileUploadError) {
          throw error
        }
        console.error('Error uploading files:', error)
        toast.error('Failed to upload files. Please try again.')
        throw new FileUploadError('Failed to upload files')
      }
    },
    [uploadFiles],
  )

  // Enhanced cancel function with error handling
  const cancelUpload = useCallback(
    (uploadId: string, fileName: string) => {
      try {
        return cancelFileUpload(uploadId, fileName)
      } catch (error) {
        console.error('Error canceling file upload:', error)
        return false
      }
    },
    [cancelFileUpload],
  )

  // Function to retry a failed file upload
  const retryUpload = useCallback(
    (uploadId: string, fileName: string, miniId?: string, options?: FileUploadOptions): Promise<Response> | null => {
      // Find the file in the progress data
      const progress = uploadProgress[uploadId]
      if (!progress || !progress.fileProgresses[fileName]) {
        return null
      }

      // Get the file object
      const file = fileObjectsRef.current.get(`${uploadId}-${fileName}`)
      if (!file) {
        return null
      }

      // Reset the file progress
      setUploadProgress(prev => {
        const currentProgress = prev[uploadId]
        if (!currentProgress) return prev

        const updatedFileProgresses = { ...currentProgress.fileProgresses }
        updatedFileProgresses[fileName] = {
          ...updatedFileProgresses[fileName],
          percentComplete: 0,
          bytesUploaded: 0,
          isUploading: true,
          hasFailed: false,
        }

        return {
          ...prev,
          [uploadId]: {
            ...currentProgress,
            fileProgresses: updatedFileProgresses,
            failedFiles: Math.max(0, (currentProgress.failedFiles || 0) - 1),
          },
        }
      })

      // Start the upload again
      return uploadSingleFile(file, uploadId, miniId, options)
    },
    [uploadSingleFile, uploadProgress],
  )

  return {
    upload,
    cancelUpload,
    uploadFiles,
    cancelFileUpload,
    retryUpload,
    uploadProgress,
  }
}
