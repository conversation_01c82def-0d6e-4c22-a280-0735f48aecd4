import { useSession } from 'next-auth/react'
import useS<PERSON> from 'swr'
import { GroupDto } from '@/web-api-client'
import { useAuth } from './useAuth'

const fetcher = async (url: string, token: string) => {
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  if (!response.ok) {
    if (response.status === 404) {
      return null // Group not found is a valid state
    }
    throw new Error('Failed to fetch group')
  }
  return response.json()
}

export function useGroup(groupIdParam?: string) {
  const { data: session } = useSession()
  const { groupId: authGroupId } = useAuth()
  const effectiveGroupId = groupIdParam || authGroupId

  const {
    data: group,
    error,
    mutate,
  } = useSWR<GroupDto & { owners?: string[] }>(
    effectiveGroupId ? [`/api/Groups/${effectiveGroupId}`, session?.accessToken] : null,
    ([url, token]) => fetcher(url, token as string),
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    group: group ?? null,
    loading: !error && !group && effectiveGroupId !== undefined,
    error,
    mutate,
  }
}
