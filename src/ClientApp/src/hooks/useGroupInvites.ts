import { GroupsClient, InviteGroupMemberCommand } from '@/web-api-client'
import { useGroupUsers } from './useGroupUsers'

export function useGroupInvites(groupId: string) {
  const { mutate: mutateUsers } = useGroupUsers(groupId)

  const inviteUsers = async (emails: string[]) => {
    const client = new GroupsClient()
    const results = await Promise.allSettled(
      emails.map(async email => {
        try {
          await client.inviteGroupMember(groupId, new InviteGroupMemberCommand({ groupId, email }))
          return { email, success: true }
        } catch (error: any) {
          let message = 'Failed to send invitation'
          try {
            // The error response is a JSON string that needs to be parsed
            const errorData = JSON.parse(error.response)
            message = errorData.detail || message
          } catch {
            // If parsing fails, keep the default message
          }
          throw new Error(message)
        }
      }),
    )

    // Count successful invitations
    const successCount = results.filter(r => r.status === 'fulfilled').length

    // Only revalidate if we had any successful invitations
    if (successCount > 0) {
      await mutateUsers()
    }

    return results
  }

  return { inviteUsers }
}
