import { useEffect, useState } from 'react'

interface Mini {
  id: number
  name: string
  description: string | null
  templateId?: string
}

interface GroupMinis {
  id: number
  groupId: string
  name: string
  minis: Mini[]
}

export function useGroupMinis(groupId: string | undefined) {
  const [groupMinis, setGroupMinis] = useState<GroupMinis | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    async function fetchGroupMinis() {
      try {
        if (!groupId) {
          setGroupMinis(null)
          return
        }

        const response = await fetch(`/api/Groups/${groupId}/minis`)
        if (!response.ok) throw new Error('Failed to fetch group minis')

        const data = await response.json()
        setGroupMinis(data)
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch group minis'))
      } finally {
        setLoading(false)
      }
    }

    fetchGroupMinis()
  }, [groupId])

  return { groupMinis, loading, error }
}
