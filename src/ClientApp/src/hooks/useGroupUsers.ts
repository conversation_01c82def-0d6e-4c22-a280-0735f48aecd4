import { useSession } from 'next-auth/react'
import { GroupUserDto } from '@/web-api-client'
import useSWR from 'swr'

const fetcher = async (url: string, token: string) => {
  const response = await fetch(url, {
    headers: {
      Authorization: `Bear<PERSON> ${token}`,
    },
  })
  if (!response.ok) throw new Error('Failed to fetch users')
  return response.json()
}

export function useGroupUsers(groupId?: string) {
  const { data: session } = useSession()
  const {
    data: users,
    error,
    mutate,
  } = useSWR<GroupUserDto[]>(groupId ? [`/api/Groups/${groupId}/users`, session?.accessToken] : null, ([url, token]) =>
    fetcher(url, token as string),
  )

  return {
    users: users ?? [],
    loading: !error && !users,
    error,
    mutate,
  }
}
