import { useSession } from 'next-auth/react'
import useS<PERSON> from 'swr'
import { GroupDto, GroupsClient } from '@/web-api-client'
import { useAuth } from './useAuth'

const fetcher = async (url: string, token: string) => {
  const client = new GroupsClient()
  return await client.getGroups()
}

export function useGroups() {
  const { data: session } = useSession()
  const { groupId } = useAuth()

  const { data, error, mutate } = useSWR<GroupDto[]>(
    groupId ? [`/api/Groups`, session?.accessToken] : null,
    ([url, token]) => fetcher(url, token as string),
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    groups: data ?? [],
    loading: !error && !data,
    error,
    mutate,
  }
}
