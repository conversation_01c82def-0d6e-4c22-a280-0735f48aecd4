'use client'

import { MiniFormData } from '@/types/mini'
import {
  CreateMiniCommand,
  MinisClient,
  ToolsClient,
  UpdateMiniCommand,
  UpdateMiniToolsCommand,
} from '@/web-api-client'
import toast from 'react-hot-toast'
import { useGroup } from './useGroup'
import { useMinis } from './useMinis'

async function uploadFiles(files: File[], groupId: string, miniId: number) {
  const formData = new FormData()
  Array.from(files).forEach(file => {
    formData.append('files', file)
  })

  const response = await fetch(`/api/Files?groupId=${groupId}&miniId=${miniId}`, {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error('Upload failed:', {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
    })
    throw new Error(`Failed to upload files: ${response.status} ${response.statusText} - ${errorText}`)
  }
}

export function useMiniActions() {
  const { group } = useGroup()
  const { mutate } = useMinis()

  const createMini = async (data: MiniFormData) => {
    if (!group?.groupId) {
      throw new Error('No group selected')
    }

    try {
      const minisClient = new MinisClient()
      const command = CreateMiniCommand.fromJS({
        name: data.name,
        description: data.description,
        groupIds: [group.groupId],
      })

      // Create mini
      const miniId = await minisClient.createMini(command)

      // Upload files if any
      if (data.files?.length && data.files instanceof FileList) {
        await uploadFiles(Array.from(data.files), group.groupId, miniId)
      }

      // Add tools if any
      if (data.toolIds?.length) {
        const toolsClient = new ToolsClient()
        const updateToolsCommand = UpdateMiniToolsCommand.fromJS({
          miniId: miniId,
          toolIds: data.toolIds,
        })
        await toolsClient.updateMiniTools(miniId, updateToolsCommand)
      }

      await mutate()
      toast.success('Mini created successfully')
      return miniId
    } catch (error) {
      console.error('Error creating mini:', error)
      toast.error('Failed to create Mini')
      throw error
    }
  }

  const updateMini = async (miniId: number, data: MiniFormData) => {
    if (!group?.groupId) {
      throw new Error('No group selected')
    }

    try {
      const minisClient = new MinisClient()

      // Update mini details
      const updateCommand = UpdateMiniCommand.fromJS({
        id: miniId,
        name: data.name,
        description: data.description,
        groupIds: [group.groupId],
        externalId: undefined,
        externalSource: undefined,
      })
      await minisClient.updateMini(miniId, updateCommand)

      // Update tools
      const toolsClient = new ToolsClient()
      const updateToolsCommand = UpdateMiniToolsCommand.fromJS({
        miniId: miniId,
        toolIds: data.toolIds || [],
      })
      await toolsClient.updateMiniTools(miniId, updateToolsCommand)

      // Upload new files if any
      if (data.files?.length && data.files instanceof FileList) {
        await uploadFiles(Array.from(data.files), group.groupId, miniId)
      }

      await mutate()
      toast.success('Mini updated successfully')
    } catch (error) {
      console.error('Error updating mini:', error)
      toast.error('Failed to update Mini')
      throw error
    }
  }

  const deleteMini = async (miniId: number) => {
    try {
      const client = new MinisClient()
      await client.deleteMini(miniId)
      await mutate()
      toast.success('Mini deleted successfully')
    } catch (error) {
      console.error('Error deleting mini:', error)
      toast.error('Failed to delete Mini')
      throw error
    }
  }

  return {
    createMini,
    updateMini,
    deleteMini,
  }
}
