import { useMemo, useState } from 'react'
import { SlimMiniDto } from '../web-api-client'

export function useMiniFilters(minis: SlimMiniDto[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedGroupFilter, setSelectedGroupFilter] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  const filteredMinis = useMemo(() => {
    return minis.filter(mini => {
      const matchesSearch =
        searchTerm === '' ||
        (mini.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (mini.description || '').toLowerCase().includes(searchTerm.toLowerCase())
      const matchesGroup =
        selectedGroupFilter.length === 0 || mini.groups?.some(g => g.groupId && selectedGroupFilter.includes(g.groupId))
      return matchesSearch && matchesGroup
    })
  }, [minis, searchTerm, selectedGroupFilter])

  const pageCount = Math.ceil(filteredMinis.length / itemsPerPage)
  const paginatedMinis = filteredMinis.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  return {
    searchTerm,
    setSearchTerm,
    selectedGroupFilter,
    setSelectedGroupFilter,
    currentPage,
    setCurrentPage,
    pageCount,
    paginatedMinis,
  }
}
