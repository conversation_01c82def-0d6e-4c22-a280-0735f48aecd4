import useSWR from 'swr'
import { MiniGroupsClient, MiniGroupDto } from '@/web-api-client'

export function useMiniGroup(groupId: string | undefined, miniGroupId: string | undefined) {
  const { data, error, mutate } = useSWR<MiniGroupDto>(
    groupId && miniGroupId ? ['miniGroup', groupId, miniGroupId] : null,
    async () => {
      const client = new MiniGroupsClient()
      return await client.getMiniGroup(groupId!, miniGroupId!)
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    miniGroup: data,
    loading: !error && !data,
    error,
    mutate,
  }
}
