import { useAuth } from './useAuth'
import { useGroup } from './useGroup'
import { useGroupUsers } from './useGroupUsers'
import { useUserGroups } from './useUserGroups'
import { MiniGroupDto } from '@/web-api-client'
import useSWR from 'swr'

export function useMiniGroupPermissions(groupId?: string, miniGroup?: MiniGroupDto) {
  const { isSuperAdmin, userId, groupId: authGroupId } = useAuth()
  const effectiveGroupId = groupId || authGroupId
  const { group } = useGroup(effectiveGroupId)
  const { users, loading: usersLoading } = useGroupUsers(effectiveGroupId || '')
  const { userGroups, loading: userGroupsLoading } = useUserGroups(effectiveGroupId)

  // Check if user is a group owner
  const isGroupOwner = users.some(user => user.id === userId && user.isGroupOwner)

  const canManageMiniGroup = isSuperAdmin || isGroupOwner

  return {
    canManageMiniGroup,
    canViewMiniGroup: true, // Everyone can view mini groups they have access to
    isLoading: !group || usersLoading || userGroupsLoading,
  }
}
