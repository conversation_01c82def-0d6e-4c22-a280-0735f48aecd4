import useSWR from 'swr'
import { MiniGroupsClient, MiniGroupDto } from '@/web-api-client'

export function useMiniGroups(groupId: string | undefined) {
  const { data, error, mutate } = useSWR<MiniGroupDto[]>(
    groupId ? ['miniGroups', groupId] : null,
    async () => {
      const client = new MiniGroupsClient()
      return await client.getMiniGroups(groupId!)
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    miniGroups: data ?? [],
    loading: !error && !data,
    error,
    mutate,
  }
}
