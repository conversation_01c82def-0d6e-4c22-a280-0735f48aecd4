import { MiniDto, MinisClient } from '@/web-api-client'
import useSWR from 'swr'
import { useGroup } from './useGroup'

// Hook for slim Mini list (used in TeamsList, etc.)
export function useMinis() {
  const { group } = useGroup()

  const { data, error, mutate } = useSWR(
    group?.id ? ['minis', group.id] : null,
    async () => {
      const client = new MinisClient()
      return await client.getMinis(group!.groupId)
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    minis: data ?? [],
    loading: !error && !data,
    error,
    mutate,
  }
}

// Hook for full Mini details (used in Mini Builder, etc.)
export function useMiniDetails(miniId: number | null) {
  const { data, error, mutate } = useSWR(
    miniId ? ['mini', miniId] : null,
    async () => {
      const client = new MinisClient()
      return await client.getMini(miniId!)
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    mini: data as MiniDto | undefined,
    loading: !error && !data,
    error,
    mutate,
  }
}
