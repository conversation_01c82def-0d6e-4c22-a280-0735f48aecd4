import { useAuth } from './useAuth'
import { UserGroupDto } from '@/web-api-client'

export function useUserGroupManagerPermissions(groupId?: string, userGroup?: UserGroupDto) {
  const { userId } = useAuth()

  // Check if user is a manager of this specific User Group
  const isUserGroupManager = userGroup?.members.some(member => member.userId === userId && member.isManager) || false

  return {
    canManageUserGroupMembers: isUserGroupManager,
    isLoading: !userGroup,
  }
}
