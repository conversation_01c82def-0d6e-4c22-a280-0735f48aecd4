import useSWR from 'swr'
import { UserGroupsClient, UserGroupMemberDto } from '../web-api-client'

export function useUserGroupMembers(groupId?: string, userGroupId?: number) {
  const client = new UserGroupsClient()

  const { data, error, mutate } = useSWR(
    groupId && userGroupId ? ['userGroup-members', groupId, userGroupId] : null,
    async ([, gId, tId]) => {
      return await client.getUserGroupMembers(gId, tId)
    },
  )

  return {
    members: data ?? ([] as UserGroupMemberDto[]),
    loading: !error && !data,
    error,
    mutate,
  }
}
