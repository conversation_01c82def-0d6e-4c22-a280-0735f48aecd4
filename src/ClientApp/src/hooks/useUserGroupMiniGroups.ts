import useSWR from 'swr'
import { UserGroupsClient, MiniGroupDto } from '@/web-api-client'

export function useUserGroupMiniGroups(groupId: string | undefined, userGroupId: number | undefined) {
  const { data, error, mutate } = useSWR<MiniGroupDto[]>(
    groupId && userGroupId ? ['userGroupMiniGroups', groupId, userGroupId] : null,
    async () => {
      const client = new UserGroupsClient()
      return await client.getUserGroupMiniGroups(groupId!, userGroupId!)
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
      shouldRetryOnError: false,
    },
  )

  return {
    miniGroups: data ?? [],
    loading: !error && !data,
    error,
    mutate,
  }
}
