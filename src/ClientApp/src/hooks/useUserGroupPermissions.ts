import { useAuth } from './useAuth'
import { useGroup } from './useGroup'
import { useGroupUsers } from './useGroupUsers'

export function useUserGroupPermissions(groupId?: string) {
  const { isSuperAdmin, userId, groupId: authGroupId } = useAuth()
  const effectiveGroupId = groupId || authGroupId
  const { group } = useGroup(effectiveGroupId)
  const { users, loading } = useGroupUsers(effectiveGroupId || '')

  const isGroupOwner = users.some(user => user.id === userId && user.isGroupOwner)

  return {
    canManageUserGroup: isSuperAdmin || isGroupOwner,
    canViewUserGroup: true, // Everyone can view user groups they're a member of
    isLoading: !group || loading,
  }
}
