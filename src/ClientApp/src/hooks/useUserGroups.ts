import { useSession } from 'next-auth/react'
import { UserGroupDto } from '@/web-api-client'
import useSWR from 'swr'

const fetcher = async (url: string, token: string) => {
  const response = await fetch(url, {
    headers: {
      Authorization: `Bear<PERSON> ${token}`,
    },
  })
  if (!response.ok) throw new Error('Failed to fetch user groups')
  return response.json()
}

export function useUserGroups(groupId?: string) {
  const { data: session } = useSession()
  const {
    data: userGroups,
    error,
    mutate,
  } = useSWR<UserGroupDto[]>(
    groupId ? [`/api/UserGroups/${groupId}/usergroups`, session?.accessToken] : null,
    ([url, token]) => fetcher(url, token as string),
  )

  return {
    userGroups: userGroups ?? [],
    loading: !error && !userGroups,
    error,
    mutate,
  }
}
