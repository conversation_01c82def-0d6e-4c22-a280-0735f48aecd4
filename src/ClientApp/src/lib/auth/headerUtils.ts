import { getServerSession } from 'next-auth/next'
import { headers } from 'next/headers'
import { authOptions } from './authConfig'

async function getAuthTokenFromAuthHeader() {
  const headersList = await headers()
  return headersList.get('authorization')?.replace('Bearer ', '') || null
}

async function getAuthTokenFromSession() {
  const session = await getServerSession(authOptions)
  return session?.accessToken || null
}

export async function getAuthToken() {
  let headerToken = null
  const headersList = await headers()
  if (headersList.get('SDK-Name')) {
    headerToken = getAuthTokenFromAuthHeader()
  } else {
    headerToken = getAuthTokenFromSession()
  }
  return headerToken
}
