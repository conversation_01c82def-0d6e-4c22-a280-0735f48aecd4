import { getAuthToken } from '@/lib/auth/headerUtils'

export async function createServerApiClient<T>(ClientClass: new (baseUrl: string, http: any) => T): Promise<T> {
  const enhancedFetch = async (url: RequestInfo, init?: RequestInit) => {
    const headers = new Headers(init?.headers)
    const token = await getAuthToken()
    if (token && !headers.has('Authorization')) {
      headers.set('Authorization', `Bearer ${token}`)
    }

    const subKey = process.env.API_SUBSCRIPTION_KEY
    if (subKey && !headers.has('Ocp-Apim-Subscription-Key')) {
      headers.set('Ocp-Apim-Subscription-Key', subKey)
    }

    return fetch(url, { ...init, headers })
  }

  return new ClientClass(process.env.API_BASE_URL || '', { fetch: enhancedFetch })
}
