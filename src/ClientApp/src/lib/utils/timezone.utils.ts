/**
 * Utility functions for handling timezone conversions
 */

/**
 * Converts UTC timestamps in text to the user's local timezone
 * Specifically handles the {{CurrentDateTime}} token format from the backend
 *
 * @param text - Text containing UTC timestamps in ISO format (yyyy-MM-ddTHH:mm:ss.fffZ)
 * @param timezone - Target timezone (e.g., 'America/New_York', 'Australia/Sydney')
 * @returns Text with timestamps converted to local timezone
 */
export function convertUtcTimestampsToLocal(text: string, timezone?: string): string {
  if (!text || !timezone) {
    return text
  }

  // Regex to match ISO 8601 UTC timestamps (yyyy-MM-ddTHH:mm:ss.fffZ format)
  const utcTimestampRegex = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/g

  return text.replace(utcTimestampRegex, utcTimestamp => {
    try {
      const date = new Date(utcTimestamp)

      // Convert to local timezone with a readable format
      return date.toLocaleString('en-AU', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false, // Use 24-hour format for consistency,
        timeZoneName: 'short',
      })
    } catch (error) {
      console.warn('Failed to convert timestamp:', utcTimestamp, error)
      return utcTimestamp // Return original if conversion fails
    }
  })
}

/**
 * Gets the user's current timezone
 * @returns The user's timezone identifier (e.g., 'America/New_York')
 */
export function getUserTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  } catch (error) {
    console.warn('Failed to get user timezone, defaulting to UTC:', error)
    return 'UTC'
  }
}

/**
 * Formats a date for display in the user's timezone
 * @param date - Date to format
 * @param timezone - Target timezone
 * @returns Formatted date string
 */
export function formatDateInTimezone(date: Date, timezone?: string): string {
  if (!timezone) {
    timezone = getUserTimezone()
  }

  try {
    return date.toLocaleString('en-AU', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
      timeZoneName: 'short',
    })
  } catch (error) {
    console.warn('Failed to format date in timezone:', error)
    return date.toISOString()
  }
}
