import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { buildCallbackUrl, hasRequiredRole, REQUIRED_MINI_ROLES, validateToken } from './lib/auth/utils'

function debugLog(area: string, message: string, data?: any) {
  console.log(`[Middleware Debug] [${area}] ${message}`, data ? data : '')
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  const auth = await validateToken(request)

  if (!auth.valid) {
    debugLog('Auth', 'No valid token found')
    if (!pathname.startsWith('/api/')) {
      debugLog('Auth', 'Redirecting to sign in page')
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }
    debugLog('Auth', 'Returning 401 for API request')
    return new NextResponse(JSON.stringify({ success: false, message: 'Authentication required' }), {
      status: 401,
      headers: { 'content-type': 'application/json' },
    })
  }

  // Check for token errors or expiration
  if (
    auth.type === 'session' &&
    auth.sessionToken &&
    (auth.sessionToken.error ||
      (auth.sessionToken.accessTokenExpires && Date.now() > auth.sessionToken.accessTokenExpires))
  ) {
    debugLog('Auth', 'Token invalid or expired', {
      hasError: !!auth.sessionToken.error,
      expiry: auth.sessionToken.accessTokenExpires
        ? new Date(auth.sessionToken.accessTokenExpires).toISOString()
        : 'none',
    })

    const message = 'Your session has expired. Please sign in again.'
    if (!pathname.startsWith('/api/')) {
      debugLog('Auth', 'Redirecting to sign in page with callback')
      return NextResponse.redirect(new URL(buildCallbackUrl(pathname, '', message), request.url))
    }
    debugLog('Auth', 'Returning 401 for API request')
    return new NextResponse(JSON.stringify({ success: false, message: 'Session expired' }), {
      status: 401,
      headers: { 'content-type': 'application/json' },
    })
  }

  // Set up headers with auth token
  debugLog('Headers', 'Setting up request headers')
  const requestHeaders = new Headers(request.headers)
  const sdkName = request.headers.get('SDK-Name')
  if (auth.token) {
    requestHeaders.set('Authorization', `Bearer ${auth.token}`)
  }
  if (process.env.API_SUBSCRIPTION_KEY) {
    requestHeaders.set('Ocp-Apim-Subscription-Key', process.env.API_SUBSCRIPTION_KEY)
  }
  if (sdkName) {
    requestHeaders.set('SDK-Name', sdkName)
  }

  // Handle API rewrites
  if (
    pathname.startsWith('/api/') &&
    !pathname.startsWith('/api/config') &&
    !pathname.startsWith('/api/mixpanel') &&
    !pathname.startsWith('/api/evals') &&
    !pathname.startsWith('/api/telemetry')
  ) {
    debugLog('API', 'Processing API request', { pathname })
    const apiBaseUrl = process.env.API_BASE_URL
    if (!apiBaseUrl) {
      debugLog('API', 'No API base URL configured')
      return NextResponse.next()
    }

    const apiUrl = new URL(apiBaseUrl)
    const rewriteUrl = request.nextUrl.clone()
    rewriteUrl.protocol = apiUrl.protocol
    rewriteUrl.host = apiUrl.hostname
    rewriteUrl.pathname = apiUrl.pathname.replace(/\/$/, '') + pathname
    rewriteUrl.port = apiUrl.hostname === 'localhost' || apiUrl.hostname === '127.0.0.1' ? apiUrl.port : ''

    debugLog('API', 'Rewriting request', {
      originalUrl: request.url,
      rewriteUrl: rewriteUrl.toString(),
    })

    return NextResponse.rewrite(rewriteUrl, {
      request: { headers: requestHeaders },
    })
  }

  // Check minis access
  if (pathname.startsWith('/minis')) {
    debugLog('Minis', 'Checking minis access')
    const userRoles = auth.sessionToken?.roles || []
    if (!hasRequiredRole(userRoles, REQUIRED_MINI_ROLES)) {
      debugLog('Minis', 'User does not have required roles')
      return NextResponse.redirect(new URL('/chat', request.url))
    }
    debugLog('Minis', 'Minis access verified')
  }

  // Check admin access
  if (pathname.startsWith('/admin')) {
    debugLog('Admin', 'Checking super admin access')
    const userRoles = auth.sessionToken?.roles || []
    if (!hasRequiredRole(userRoles, ['SuperAdmin'])) {
      debugLog('Admin', 'User does not have Super Admin role')
      return NextResponse.redirect(new URL('/chat', request.url))
    }
    debugLog('Super Admin', 'Super Admin access verified')
  }

  return NextResponse.next({
    request: { headers: requestHeaders },
  })
}

export const config = {
  matcher: [
    '/api/DocumentLists/:path*',
    '/api/DocumentItems/:path*',
    '/api/Tools/:path*',
    '/api/Minis/:path*',
    '/api/Groups/:path*',
    '/api/Forms/:path*',
    '/((?!_next/static|_next/image|favicon.ico|assets/|auth/signin|api/auth|api/config|api/assistant|api/messages|api/mixpanel|api/telemetry|api/chat|api/intercom).*)',
  ],
}
