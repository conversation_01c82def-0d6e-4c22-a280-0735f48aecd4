export enum ERROR_CATEGORIES {
  NETWORK_ERROR = 'NETWORK_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CONTEXT_LIMIT_ERROR = 'CONTEXT_LIMIT_ERROR',
  CONTENT_FILTER_ERROR = 'CONTENT_FILTER_ERROR',
  INVALID_REQUEST_ERROR = 'INVALID_REQUEST_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  INVALID_TOOL_ARGUMENTS_ERROR = 'INVALID_TOOL_ARGUMENTS_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface ChatError {
  category: ERROR_CATEGORIES
  name: string
  message: string
  isRetryable: boolean
}

export function isAuthenticationError(error: ChatError): boolean {
  return error.category === ERROR_CATEGORIES.AUTHENTICATION_ERROR
}
