{"definition": {"openapi": "3.0.1", "info": {"title": "Minikai Client API", "description": "NextJS API routes for the Minikai platform", "version": "1.0"}, "servers": [{"url": "https://app.{env}.minikai.com/api"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token obtained from Microsoft Azure AD OAuth2 authorization code flow"}}}, "security": [{"BearerAuth": []}]}, "apiFolder": "src/app/api", "outputDir": "../../apis/specs/client-app", "outputFile": "client-api.openapi.json"}