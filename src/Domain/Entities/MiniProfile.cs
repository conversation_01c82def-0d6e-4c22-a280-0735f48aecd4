using System.Text.Json.Serialization;

namespace Minikai.Domain.Entities;

public class MiniProfile
{
    [JsonPropertyName("id")]
    public required string Id { get; set; } // Format: "{miniId}_{version}"
    
    [JsonPropertyName("version")]
    public required string Version { get; set; } // Format: "2025-07-01-001"
    
    [JsonPropertyName("miniId")]
    public required string MiniId { get; set; } // References SQL Mini.Id
    
    [JsonPropertyName("templateId")]
    public required Guid TemplateId { get; set; } // References SQL MiniTemplate.Id
    
    [JsonPropertyName("values")]
    public required List<ProfileValue> Values { get; set; } = new();
    
    [JsonPropertyName("createdAt")]
    public required DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("createdBy")]
    public required string CreatedBy { get; set; }
}

public class ProfileValue
{
    [JsonPropertyName("name")]
    public required string Name { get; set; } // Token name (e.g., "Name")
    
    [JsonPropertyName("value")]
    public required string Value { get; set; } // Token value (e.g., "Jake")
}
