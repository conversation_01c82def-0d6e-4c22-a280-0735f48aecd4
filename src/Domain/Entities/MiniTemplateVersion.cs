using System.Text.Json.Serialization;

namespace Minikai.Domain.Entities;

public class MiniTemplateVersion
{
    [JsonPropertyName("id")]
    public required string Id { get; set; } // Format: "{templateId}_{version}"
    
    [JsonPropertyName("templateId")]
    public required Guid TemplateId { get; set; } // References SQL MiniTemplate.Id
    
    [JsonPropertyName("version")]
    public required string Version { get; set; } // Format: "2025-07-01-001"
    
    [JsonPropertyName("system")]
    public required string System { get; set; } // System prompt instructions
    
    [JsonPropertyName("tokens")]
    public required List<TemplateToken> Tokens { get; set; } = new();
    
    [JsonPropertyName("createdAt")]
    public required DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("createdBy")]
    public required string CreatedBy { get; set; }
}

public class TemplateToken
{
    [JsonPropertyName("name")]
    public required string Name { get; set; }
    
    [JsonPropertyName("type")]
    public required string Type { get; set; } // "string", "number", "integer", "boolean", "array", "object"
    
    [JsonPropertyName("description")]
    public string? Description { get; set; }
}

public static class TokenTypes
{
    public const string String = "string";
    public const string Number = "number";
    public const string Integer = "integer";
    public const string Boolean = "boolean";
    public const string Array = "array";
    public const string Object = "object";
}
