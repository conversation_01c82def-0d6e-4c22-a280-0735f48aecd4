# Azure AD Configuration
# These credentials are required for authenticating with the Minikai API
AZURE_TENANT_ID=your-tenant-id-here
AZURE_AD_CLIENT_ID=your-client-id-here
AZURE_AD_CLIENT_SECRET=your-client-secret-here

# API Configuration
# Subscription key for accessing the Minikai API
API_SUBSCRIPTION_KEY=your-subscription-key-here


# API endpoints (modify if using different environments)
# API_BASE_URL=https://app.dev.minikai.com/api
# API_BASE_URL=https://host.docker.internal:44447/api  <- If accessing local Next.js Server
API_BASE_URL=https://api.minikai.com/api

# Optional: Timezone for chat requests
DEFAULT_TIMEZONE=UTC

# Instructions:
# 1. Copy this file to .env: cp .env.example .env
# 2. Fill in your actual credentials from the Azure portal
# 3. Never commit the .env file to version control
# 4. Contact your admin if you need access to these credentials
