# Minikai Evaluation Library

A lightweight, strongly-typed, pandas-based library for batch evaluation of Mini responses from CSV inputs.

## Features

- ✅ **Strongly Typed**: Complete UIMessage types matching AI SDK
- 📊 **Pandas Integration**: Built for data science workflows
- 🔧 **Extensible**: Easy-to-hack response processing
- 📝 **CSV-Driven**: Simple input/output format
- 🔄 **Retry Logic**: Handles empty responses with configurable delays
- 🎯 **Flexible Column Order**: Configurable output column ordering

## Quick Start

```python
from minikai_eval import run_evaluation

# Run evaluation with default settings
results_df = run_evaluation(
    input_csv_path="input.csv",
    output_csv_path="results.csv"
)
```

## CSV Format

### Required Input Columns

Your CSV must contain these columns (order doesn't matter):

- `MiniId` - The Mini identifier to evaluate
- `User message` - The user's input message
- `ChatId` - Chat session ID (can be empty, will be auto-generated)

## Customization

### Adding Custom Metrics

Edit `minikai_eval/extractors.py` and modify the `extract_fields` function:

```python
def extract_fields(message: UIMessage) -> Dict[str, Any]:
    # ... existing code ...

    # Add your custom metrics
    fields["custom_score"] = _calculate_custom_score(message)
    fields["sentiment"] = _analyze_sentiment(text_content)

    return fields
```

## Examples

See the `examples/` directory for:

- `sample_input.csv` - Example input format
- `run_eval.py` - Basic usage script
- `README.md` - Detailed examples

## Architecture

```
minikai_eval/
├── __init__.py        # Public API
├── schemas.py         # Pydantic validation models
├── client.py          # Connections to the Python minikai-frontend-client
├── eval.py            # Main evaluation pipeline
└── extractors.py      # Response field extraction
```
