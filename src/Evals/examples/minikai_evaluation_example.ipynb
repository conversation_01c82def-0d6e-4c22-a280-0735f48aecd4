%load_ext nb_mypy
%nb_mypy On

import os
from pathlib import Path


# Try to load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print('✅ Loaded .env file')
except ImportError:
    print('⚠️ python-dotenv not installed, using system environment variables')

# Check required environment variables
required_env_vars = [
    'AZURE_TENANT_ID',
    'AZURE_AD_CLIENT_ID',
    'AZURE_AD_CLIENT_SECRET',
    'API_SUBSCRIPTION_KEY',
    'API_BASE_URL'
]

print('🔍 Environment Configuration:')
missing_vars = []
for var in required_env_vars:
    value = os.getenv(var)
    if value:
        display_value = f'{value[:12]}...' if len(value) > 8 else value
        print(f'  ✅ {var}: {display_value}')
    else:
        print(f'  ❌ {var}: Not set')
        missing_vars.append(var)

if missing_vars:
    print(f'\n⚠️ Missing environment variables: {", ".join(missing_vars)}')
    print('📝 Please check your .env file configuration')
else:
    print('\n✅ All required environment variables are configured')

# Initialize the client and test authentication


from minikai_eval import (
    MinikaiClient, 
    run_evaluation,
    MinikaiClientError,
    AuthenticationError
)
print('✅ Minikai Evaluation Library imported successfully')

try:
    print('🔐 Initializing Minikai client...')
    client = MinikaiClient()
    print('✅ Client initialized successfully')
    
    print('🔑 Testing authentication...')
    token = client.login()
    print('✅ Authentication successful!')
    print(f'   Token starts with: {token[:20]}...')
    
    # Check if client is authenticated
    is_auth = client.is_authenticated()
    print(f'   Client authenticated: {is_auth}')
    
except AuthenticationError as e:
    print(f'❌ Authentication failed: {e}')
    print('   Please check your Azure AD credentials')
    client = None
except Exception as e:
    print(f'❌ Client initialization failed: {e}')
    client = None

from minikai_eval.eval import prepare_previous_messages
from minikai_eval.extractors import extract_system_message_content


if client and client.is_authenticated():
    try:
        # Example Mini ID - replace with your actual Mini ID
        MINI_ID = 321  # Replace with your actual Mini ID
        
        print(f'💬 Testing single message with Mini ID: {MINI_ID}')
        
        # Create a chat
        print('📝 Creating chat...')
        chatResponse = client.create_chat(MINI_ID, "What's your job.")
        print(f'✅ Chat created with ID: {chatResponse.chat_id}')
        
        
        print('☑️ Prepare messages')
        messageHistory = prepare_previous_messages("41b35932-fe1e-480a-87e7-314b81554351", client)
        print('✅ Message history gathered successfully!')
        
        
        print('📤 Sending message...')
        response = client.send_message(MINI_ID,"41b35932-fe1e-480a-87e7-314b81554351", "Mock it again", messageHistory)
        print('✅ Message sent successfully!')

        listMessages = client.get_messages("41b35932-fe1e-480a-87e7-314b81554351")
        print('✅ Messages retrieved successfully!')
        print
    
        
        # Display response details
        print('📋 Response Details:')
        print(f'   Response: {response}')
        
        
        # Test extraction
        from minikai_eval import extract_fields


        extracted = extract_fields(response)
        print('📊 Extracted Fields:')   
        for key, value in extracted.items():
            print(f'   {key}: {value}')
            
            
    except MinikaiClientError as e:
        print(f'❌ API Error: {e}')
    except Exception as e:
        print(f'❌ Unexpected error: {e}')
else:
    print('⚠️ Skipping single message test - client not authenticated')

# Test extraction
from minikai_eval import extract_fields

if client and client.is_authenticated():
    try:
        input_csv_path = 'src/Evals/examples/sample_evaluation_input_real.csv'
        output_csv_path = 'src/Evals/examples/evaluation_results.csv'
        
        print('🚀 Starting batch evaluation...')
        
        # Run the evaluation
        results_df = run_evaluation(
            input_csv_path=input_csv_path,
            output_csv_path=output_csv_path,
            client=client,
            extract_fn=extract_fields
        )
        
        print('✅ Batch evaluation completed!')
        
    except Exception as e:
        print(f'❌ Evaluation failed: {e}')
        results_df = None
else:
    print('⚠️ Skipping batch evaluation - client not authenticated')
    results_df = None

if results_df is not None:
    print('📊 Evaluation Results Summary:')
    print(f'  - Total rows processed: {len(results_df)}')
    print(f'  - Responses with tool calls: {results_df["has_tool_calls"].sum()}')
    print(f'  - Average response length: {results_df["assistant_response_length"].mean():.1f} chars')
    
    print('📋 Column names in output:')
    for col in results_df.columns:
        print(f'  - {col}')
    
    print('🔍 Sample Results:')
    display(results_df.head())
    
    # Response length distribution
    if len(results_df) > 1:
        import matplotlib.pyplot as plt
        
        plt.figure(figsize=(10, 4))
        
        plt.subplot(1, 2, 1)
        plt.hist(results_df['assistant_response_length'], bins=10, alpha=0.7)
        plt.title('Response Length Distribution')
        plt.xlabel('Characters')
        plt.ylabel('Count')
        
        
        plt.tight_layout()
        plt.show()
else:
    print('⚠️ No results to analyze - evaluation was skipped or failed')



