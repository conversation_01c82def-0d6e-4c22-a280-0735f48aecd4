{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Minikai Evaluation Library - Complete Example\n", "\n", "This notebook demonstrates how to use the upgraded Minikai Evaluation Library with real API integration.\n", "\n", "## What's New in v0.2.0\n", "\n", "- ✅ **Real API Integration**: Uses minikai-client-api-client instead of dummy responses\n", "- 🔐 **Azure AD Authentication**: MSAL-based authentication with token management\n", "- 📊 **Robust Type Safety**: Uses generated API models directly\n", "- ⚙️ **Environment Configuration**: Easy setup via .env files\n", "- 🔄 **Error <PERSON>**: Proper retry logic and error reporting\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Installation\n", "\n", "Before running this notebook, ensure you have:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'jupyte<PERSON>_mypy'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun_line_magic\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mload_ext\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mjupyter_mypy\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/IPython/core/interactiveshell.py:2504\u001b[39m, in \u001b[36mInteractiveShell.run_line_magic\u001b[39m\u001b[34m(self, magic_name, line, _stack_depth)\u001b[39m\n\u001b[32m   2502\u001b[39m     kwargs[\u001b[33m'\u001b[39m\u001b[33mlocal_ns\u001b[39m\u001b[33m'\u001b[39m] = \u001b[38;5;28mself\u001b[39m.get_local_scope(stack_depth)\n\u001b[32m   2503\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m.builtin_trap:\n\u001b[32m-> \u001b[39m\u001b[32m2504\u001b[39m     result = \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2506\u001b[39m \u001b[38;5;66;03m# The code below prevents the output from being displayed\u001b[39;00m\n\u001b[32m   2507\u001b[39m \u001b[38;5;66;03m# when using magics with decorator @output_can_be_silenced\u001b[39;00m\n\u001b[32m   2508\u001b[39m \u001b[38;5;66;03m# when the last Python token in the expression is a ';'.\u001b[39;00m\n\u001b[32m   2509\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(fn, magic.MAGIC_OUTPUT_CAN_BE_SILENCED, \u001b[38;5;28;01mFalse\u001b[39;00m):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/IPython/core/magics/extension.py:33\u001b[39m, in \u001b[36mExtensionMagics.load_ext\u001b[39m\u001b[34m(self, module_str)\u001b[39m\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m module_str:\n\u001b[32m     32\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m UsageError(\u001b[33m'\u001b[39m\u001b[33mMissing module name.\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m res = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mshell\u001b[49m\u001b[43m.\u001b[49m\u001b[43mextension_manager\u001b[49m\u001b[43m.\u001b[49m\u001b[43mload_extension\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodule_str\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m res == \u001b[33m'\u001b[39m\u001b[33malready loaded\u001b[39m\u001b[33m'\u001b[39m:\n\u001b[32m     36\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mThe \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m extension is already loaded. To reload it, use:\u001b[39m\u001b[33m\"\u001b[39m % module_str)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/IPython/core/extensions.py:62\u001b[39m, in \u001b[36mExtensionManager.load_extension\u001b[39m\u001b[34m(self, module_str)\u001b[39m\n\u001b[32m     55\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Load an IPython extension by its module name.\u001b[39;00m\n\u001b[32m     56\u001b[39m \n\u001b[32m     57\u001b[39m \u001b[33;03mReturns the string \"already loaded\" if the extension is already loaded,\u001b[39;00m\n\u001b[32m     58\u001b[39m \u001b[33;03m\"no load function\" if the module doesn't have a load_ipython_extension\u001b[39;00m\n\u001b[32m     59\u001b[39m \u001b[33;03mfunction, or None if it succeeded.\u001b[39;00m\n\u001b[32m     60\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     61\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m62\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_load_extension\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodule_str\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     63\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mModuleNotFoundError\u001b[39;00m:\n\u001b[32m     64\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m module_str \u001b[38;5;129;01min\u001b[39;00m BUILTINS_EXTS:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/IPython/core/extensions.py:77\u001b[39m, in \u001b[36mExtensionManager._load_extension\u001b[39m\u001b[34m(self, module_str)\u001b[39m\n\u001b[32m     75\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m.shell.builtin_trap:\n\u001b[32m     76\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m module_str \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m sys.modules:\n\u001b[32m---> \u001b[39m\u001b[32m77\u001b[39m         mod = \u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodule_str\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     78\u001b[39m     mod = sys.modules[module_str]\n\u001b[32m     79\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._call_load_ipython_extension(mod):\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/local/python/3.11.13/lib/python3.11/importlib/__init__.py:126\u001b[39m, in \u001b[36mimport_module\u001b[39m\u001b[34m(name, package)\u001b[39m\n\u001b[32m    124\u001b[39m             \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m    125\u001b[39m         level += \u001b[32m1\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m126\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen importlib._bootstrap>:1204\u001b[39m, in \u001b[36m_gcd_import\u001b[39m\u001b[34m(name, package, level)\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen importlib._bootstrap>:1176\u001b[39m, in \u001b[36m_find_and_load\u001b[39m\u001b[34m(name, import_)\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen importlib._bootstrap>:1140\u001b[39m, in \u001b[36m_find_and_load_unlocked\u001b[39m\u001b[34m(name, import_)\u001b[39m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'jupyter_mypy'"]}], "source": ["%load_ext nb_mypy\n", "%nb_mypy On"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Configuration\n", "\n", "First, let's check and validate our environment configuration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "\n", "# Try to load environment variables\n", "try:\n", "    from dotenv import load_dotenv\n", "    load_dotenv()\n", "    print('✅ Loaded .env file')\n", "except ImportError:\n", "    print('⚠️ python-dotenv not installed, using system environment variables')\n", "\n", "# Check required environment variables\n", "required_env_vars = [\n", "    'AZURE_TENANT_ID',\n", "    'AZURE_AD_CLIENT_ID',\n", "    'AZURE_AD_CLIENT_SECRET',\n", "    'API_SUBSCRIPTION_KEY',\n", "    'API_BASE_URL'\n", "]\n", "\n", "print('🔍 Environment Configuration:')\n", "missing_vars = []\n", "for var in required_env_vars:\n", "    value = os.getenv(var)\n", "    if value:\n", "        display_value = f'{value[:12]}...' if len(value) > 8 else value\n", "        print(f'  ✅ {var}: {display_value}')\n", "    else:\n", "        print(f'  ❌ {var}: Not set')\n", "        missing_vars.append(var)\n", "\n", "if missing_vars:\n", "    print(f'\\n⚠️ Missing environment variables: {\", \".join(missing_vars)}')\n", "    print('📝 Please check your .env file configuration')\n", "else:\n", "    print('\\n✅ All required environment variables are configured')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Authentication Test\n", "\n", "Let's test authentication with the Minikai platform."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the client and test authentication\n", "\n", "\n", "from minikai_eval import (\n", "    MinikaiClient, \n", "    run_evaluation,\n", "    MinikaiClientError,\n", "    AuthenticationError\n", ")\n", "print('✅ Minikai Evaluation Library imported successfully')\n", "\n", "try:\n", "    print('🔐 Initializing Minikai client...')\n", "    client = MinikaiClient()\n", "    print('✅ Client initialized successfully')\n", "    \n", "    print('🔑 Testing authentication...')\n", "    token = client.login()\n", "    print('✅ Authentication successful!')\n", "    print(f'   Token starts with: {token[:20]}...')\n", "    \n", "    # Check if client is authenticated\n", "    is_auth = client.is_authenticated()\n", "    print(f'   Client authenticated: {is_auth}')\n", "    \n", "except AuthenticationError as e:\n", "    print(f'❌ Authentication failed: {e}')\n", "    print('   Please check your Azure AD credentials')\n", "    client = None\n", "except Exception as e:\n", "    print(f'❌ Client initialization failed: {e}')\n", "    client = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Single Message Evaluation Example\n", "\n", "Let's test a single message evaluation to ensure everything works."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from minikai_eval.eval import prepare_previous_messages\n", "from minikai_eval.extractors import extract_system_message_content\n", "\n", "\n", "if client and client.is_authenticated():\n", "    try:\n", "        # Example Mini ID - replace with your actual Mini ID\n", "        MINI_ID = 321  # Replace with your actual Mini ID\n", "        \n", "        print(f'💬 Testing single message with Mini ID: {MINI_ID}')\n", "        \n", "        # Create a chat\n", "        print('📝 Creating chat...')\n", "        chatResponse = client.create_chat(MINI_ID, \"What's your job.\")\n", "        print(f'✅ Chat created with ID: {chatResponse.chat_id}')\n", "        \n", "        \n", "        print('☑️ Prepare messages')\n", "        messageHistory = prepare_previous_messages(\"41b35932-fe1e-480a-87e7-314b81554351\", client)\n", "        print('✅ Message history gathered successfully!')\n", "        \n", "        \n", "        print('📤 Sending message...')\n", "        response = client.send_message(MINI_ID,\"41b35932-fe1e-480a-87e7-314b81554351\", \"Mock it again\", messageHistory)\n", "        print('✅ Message sent successfully!')\n", "\n", "        listMessages = client.get_messages(\"41b35932-fe1e-480a-87e7-314b81554351\")\n", "        print('✅ Messages retrieved successfully!')\n", "        print\n", "    \n", "        \n", "        # Display response details\n", "        print('📋 Response Details:')\n", "        print(f'   Response: {response}')\n", "        \n", "        \n", "        # Test extraction\n", "        from minikai_eval import extract_fields\n", "\n", "\n", "        extracted = extract_fields(response)\n", "        print('📊 Extracted Fields:')   \n", "        for key, value in extracted.items():\n", "            print(f'   {key}: {value}')\n", "            \n", "            \n", "    except MinikaiClientError as e:\n", "        print(f'❌ API Error: {e}')\n", "    except Exception as e:\n", "        print(f'❌ Unexpected error: {e}')\n", "else:\n", "    print('⚠️ Skipping single message test - client not authenticated')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON>\n", "\n", "Now let's run a complete batch evaluation."]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Starting batch evaluation...\n", "📂 Loading CSV from src/Evals/examples/sample_evaluation_input_real.csv\n", "✅ Loaded 3 rows with valid columns\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing rows:   0%|          | 0/3 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["🔄 Error sending message, retrying in 5s: Failed to send message: Value after * must be an iterable, not NoneType\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing rows:  67%|██████▋   | 2/3 [00:05<00:02,  2.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["❌ Error processing row: Failed to send message: Value after * must be an iterable, not NoneType\n", "🔄 Error sending message, retrying in 5s: Failed to send message: Value after * must be an iterable, not NoneType\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing rows: 100%|██████████| 3/3 [00:10<00:00,  3.74s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["❌ Error processing row: Failed to send message: Value after * must be an iterable, not NoneType\n", "🔄 Error sending message, retrying in 5s: Failed to send message: Value after * must be an iterable, not NoneType\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing rows: 100%|██████████| 3/3 [00:15<00:00,  5.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["❌ Error processing row: Failed to send message: Value after * must be an iterable, not NoneType\n", "💾 Writing results to src/Evals/examples/evaluation_results.csv\n", "✅ Evaluation complete! 3 rows processed\n", "✅ Batch evaluation completed!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Test extraction\n", "from minikai_eval import extract_fields\n", "\n", "if client and client.is_authenticated():\n", "    try:\n", "        input_csv_path = 'src/Evals/examples/sample_evaluation_input_real.csv'\n", "        output_csv_path = 'src/Evals/examples/evaluation_results.csv'\n", "        \n", "        print('🚀 Starting batch evaluation...')\n", "        \n", "        # Run the evaluation\n", "        results_df = run_evaluation(\n", "            input_csv_path=input_csv_path,\n", "            output_csv_path=output_csv_path,\n", "            client=client,\n", "            extract_fn=extract_fields\n", "        )\n", "        \n", "        print('✅ Batch evaluation completed!')\n", "        \n", "    except Exception as e:\n", "        print(f'❌ Evaluation failed: {e}')\n", "        results_df = None\n", "else:\n", "    print('⚠️ Skipping batch evaluation - client not authenticated')\n", "    results_df = None"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Results Analysis\n", "\n", "Let's analyze the evaluation results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if results_df is not None:\n", "    print('📊 Evaluation Results Summary:')\n", "    print(f'  - Total rows processed: {len(results_df)}')\n", "    print(f'  - Responses with tool calls: {results_df[\"has_tool_calls\"].sum()}')\n", "    print(f'  - Average response length: {results_df[\"assistant_response_length\"].mean():.1f} chars')\n", "    \n", "    print('📋 Column names in output:')\n", "    for col in results_df.columns:\n", "        print(f'  - {col}')\n", "    \n", "    print('🔍 Sample Results:')\n", "    display(results_df.head())\n", "    \n", "    # Response length distribution\n", "    if len(results_df) > 1:\n", "        import matplotlib.pyplot as plt\n", "        \n", "        plt.figure(figsize=(10, 4))\n", "        \n", "        plt.subplot(1, 2, 1)\n", "        plt.hist(results_df['assistant_response_length'], bins=10, alpha=0.7)\n", "        plt.title('Response Length Distribution')\n", "        plt.xlabel('Characters')\n", "        plt.ylabel('Count')\n", "        \n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "else:\n", "    print('⚠️ No results to analyze - evaluation was skipped or failed')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}