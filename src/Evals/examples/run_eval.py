#!/usr/bin/env python3
"""
Example script showing how to use the minikai-eval library.

This script demonstrates the basic usage of the evaluation pipeline
with the sample CSV data.
"""

from pathlib import Path
from minikai_eval import run_evaluation, MinikaiClient


def main():
    """Run the evaluation example."""
    
    # Get paths relative to this script
    script_dir = Path(__file__).parent
    input_csv = script_dir / "sample_input.csv"
    output_csv = script_dir / "sample_output.csv"
    
    print("🚀 Starting Minikai Evaluation Example")
    print(f"📁 Input CSV: {input_csv}")
    print(f"📁 Output CSV: {output_csv}")
    
    # Create a client (will use real API)
    # Make sure your .env file is configured with Azure AD credentials
    client = MinikaiClient()
    
    try:
        # Run the evaluation
        results_df = run_evaluation(
            input_csv_path=input_csv,
            output_csv_path=output_csv,
            client=client
        )
        
        print("\n📊 Results Summary:")
        print(f"  - Total rows processed: {len(results_df)}")
        print(f"  - Responses with tool calls: {results_df['has_tool_calls'].sum()}")
        print(f"  - Average response length: {results_df['assistant_response_length'].mean():.1f} chars")
        
        print("\n📋 Column names in output:")
        for col in results_df.columns:
            print(f"  - {col}")
        
        print(f"\n✅ Example complete! Check {output_csv} for full results.")
        
    except Exception as e:
        print(f"❌ Error running evaluation: {e}")
        raise


if __name__ == "__main__":
    main()