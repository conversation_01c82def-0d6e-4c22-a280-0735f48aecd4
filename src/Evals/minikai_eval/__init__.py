"""
Minikai Evaluation Library

A lightweight, pandas-based library for batch evaluation of Mini responses
from CSV inputs with strongly-typed data structures.
"""

from .auth import AuthenticationError, MinikaiAuth
from .client import MinikaiClient, MinikaiClientError
from .config import Config, load_config
from .eval import run_evaluation
from .extractors import extract_fields

__version__ = "0.2.0"

__all__ = [
    "MinikaiClient",
    "MinikaiClientError",
    "run_evaluation",
    "extract_fields",
    "Config",
    "load_config",
    "MinikaiAuth",
    "AuthenticationError",
    "UIMessage",
    "UIMessageRole",
]
