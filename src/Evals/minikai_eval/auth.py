"""
Authentication handling for Minikai client using Azure AD.

This module provides Azure AD authentication using MSAL (Microsoft Authentication Library)
for service-to-service authentication with the Minikai platform.
"""

import os
import time
from typing import Dict, Optional
import msal

from .config import Config


class TokenStorage:
    """
    Simple in-memory token storage for development.
    
    In production, consider using a more robust storage solution
    like Redis or a database for token persistence.
    """
    
    def __init__(self):
        self._token_data: Optional[Dict] = None
    
    def save_token(self, token_data: Dict) -> None:
        """Save token data to storage."""
        self._token_data = token_data
    
    def load_token(self) -> Optional[Dict]:
        """Load token data from storage."""
        return self._token_data
    
    def clear_token(self) -> None:
        """Clear stored token data."""
        self._token_data = None
    
    def is_token_expired(self, token_data: Dict) -> bool:
        """
        Check if token is expired.
        
        Args:
            token_data: Token dictionary with 'expires_in' field
            
        Returns:
            True if token is expired or will expire in next 300 seconds
        """
        if not token_data or 'expires_in' not in token_data:
            return True
        
        # Consider token expired if it expires in the next 5 minutes
        expires_at = token_data.get('_expires_at', 0)
        return time.time() >= (expires_at - 300)


class AuthenticationError(Exception):
    """Exception raised when authentication fails."""
    pass


class MinikaiAuth:
    """
    Handles Azure AD authentication for Minikai platform.
    
    Uses the client credentials flow for service-to-service authentication.
    """
    
    def __init__(self, config: Config):
        """
        Initialize authentication with configuration.
        
        Args:
            config: Configuration instance with Azure AD settings
        """
        self.config = config
        self.storage = TokenStorage()
        self._msal_app = None
        self._current_token = None
        
        # Validate configuration
        self._validate_config()
        
        # For client credentials flow, we need /.default scope
        self.scopes = [f"api://{config.azure_client_id}/.default"]
        
    def _validate_config(self) -> None:
        """Validate that all required configuration is present."""
        try:
            self.config.validate()
        except ValueError as e:
            raise AuthenticationError(f"Configuration validation failed: {e}")
    
    @property
    def msal_app(self) -> msal.ConfidentialClientApplication:
        """Get or create MSAL application instance."""
        if self._msal_app is None:
            authority = f"https://login.microsoftonline.com/{self.config.azure_tenant_id}"
            self._msal_app = msal.ConfidentialClientApplication(
                client_id=self.config.azure_client_id,
                client_credential=self.config.azure_client_secret,
                authority=authority
            )
        return self._msal_app
    
    async def login(self, force_refresh: bool = False) -> str:
        """
        Authenticate and get access token.
        
        Args:
            force_refresh: Force a new token even if cached token is valid
            
        Returns:
            Access token string
            
        Raises:
            AuthenticationError: If authentication fails
        """
        # Try to use cached token first
        if not force_refresh:
            token = await self._get_cached_token()
            if token:
                return token
        
        # Get new token
        token = await self._acquire_new_token()
        return token
    
    def login_sync(self, force_refresh: bool = False) -> str:
        """
        Synchronous version of login for compatibility.
        
        Args:
            force_refresh: Force a new token even if cached token is valid
            
        Returns:
            Access token string
            
        Raises:
            AuthenticationError: If authentication fails
        """
        # Try to use cached token first
        if not force_refresh:
            token = self._get_cached_token_sync()
            if token:
                return token
        
        # Get new token
        token = self._acquire_new_token_sync()
        return token
    
    async def _get_cached_token(self) -> Optional[str]:
        """Try to get a valid cached token."""
        return self._get_cached_token_sync()
    
    def _get_cached_token_sync(self) -> Optional[str]:
        """Try to get a valid cached token (sync version)."""
        try:
            token_data = self.storage.load_token()
            if token_data and not self.storage.is_token_expired(token_data):
                self._current_token = token_data
                return token_data["access_token"]
        except Exception as e:
            print(f"Warning: Failed to load cached token: {e}")
        
        return None
    
    async def _acquire_new_token(self) -> str:
        """Acquire a new token from Azure AD."""
        return self._acquire_new_token_sync()
    
    def _acquire_new_token_sync(self) -> str:
        """Acquire a new token from Azure AD (sync version)."""
        try:
            # Try to get token from cache first
            accounts = self.msal_app.get_accounts()
            if accounts:
                # Try silent acquisition first
                result = self.msal_app.acquire_token_silent(
                    scopes=self.scopes,
                    account=accounts[0]
                )
                if result and "access_token" in result:
                    self._save_token(result)
                    return result["access_token"]
            
            # If silent acquisition fails, use client credentials flow
            result = self.msal_app.acquire_token_for_client(scopes=self.scopes)
            
            if "access_token" in result:
                self._save_token(result)
                return result["access_token"]
            else:
                error_msg = result.get("error_description", "Unknown authentication error")
                raise AuthenticationError(f"Failed to acquire token: {error_msg}")
                
        except Exception as e:
            if isinstance(e, AuthenticationError):
                raise
            raise AuthenticationError(f"Authentication failed: {str(e)}")
    
    def _save_token(self, token_result: Dict) -> None:
        """Save token result to storage."""
        # Add expiration timestamp for easier checking
        if 'expires_in' in token_result:
            token_result['_expires_at'] = time.time() + token_result['expires_in']
        
        self._current_token = token_result
        self.storage.save_token(token_result)
    
    async def get_valid_token(self) -> str:
        """Get a valid access token, refreshing if necessary."""
        return self.get_valid_token_sync()
    
    def get_valid_token_sync(self) -> str:
        """Get a valid access token, refreshing if necessary (sync version)."""
        if self._current_token and not self.storage.is_token_expired(self._current_token):
            return self._current_token["access_token"]
        
        return self.login_sync(force_refresh=True)
    
    async def logout(self) -> None:
        """Clear stored authentication data."""
        self._current_token = None
        self.storage.clear_token()
    
    def get_auth_headers(self, token: Optional[str] = None) -> Dict[str, str]:
        """
        Get authentication headers for API requests.
        
        Args:
            token: Optional token to use, otherwise uses current token
            
        Returns:
            Dictionary of headers
        """
        headers = {}
        
        # Add authorization header
        if token is None and self._current_token:
            token = self._current_token["access_token"]
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # Add subscription key if available
        if self.config.api_subscription_key:
            headers["Ocp-Apim-Subscription-Key"] = self.config.api_subscription_key

        headers["SDK-Name"] = "evals-python-sdk"
         
        return headers
    
    def is_authenticated(self) -> bool:
        """
        Check if client is currently authenticated.
        
        Returns:
            True if valid token is available
        """
        return (
            self._current_token is not None and 
            not self.storage.is_token_expired(self._current_token)
        )