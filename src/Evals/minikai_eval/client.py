"""
Real Minikai client for evaluation using the minikai-client-api-client.

This module provides a wrapper around the generated API client that implements
the same interface as the previous DummyMiniClient for backward compatibility.
"""
import json
from typing import List, Optional

from minikai_frontend_client import AuthenticatedClient
from minikai_frontend_client.api.evals import (
    create_eval_chat,
    get_eval_messages,
    send_eval_message,
)
from minikai_frontend_client.models.chat_request import ChatRequest
from minikai_frontend_client.models.create_chat_request import CreateChatRequest
from minikai_frontend_client.models.create_chat_response import CreateChatResponse
from minikai_frontend_client.models.error_response import ErrorResponse
from minikai_frontend_client.models.get_messages_response import GetMessagesResponse
from minikai_frontend_client.models.send_message_response import SendMessageResponse
from minikai_frontend_client.models.ui_message import UIMessage
from minikai_frontend_client.models.ui_message_role import UIMessageRole
from minikai_frontend_client.types import UNSET

from .auth import AuthenticationError, MinikaiAuth
from .config import Config, load_config


class MinikaiClientError(Exception):
    """Exception raised when Minikai client operations fail."""
    pass


class MinikaiClient:
    """
    Real Minikai client that interacts with the Minikai platform via the API.

    This client provides the same interface as DummyMiniClient but connects
    to the actual Minikai evaluation endpoints with proper authentication.
    """

    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the Minikai client.

        Args:
            config: Configuration instance (loads from environment if None)
        """
        if config is None:
            config = load_config()

        self.config = config
        self.auth = MinikaiAuth(config)
        self._api_client: Optional[AuthenticatedClient] = None

    def _get_api_client(self) -> AuthenticatedClient:
        """Get authenticated API client, creating if necessary."""
        if self._api_client is None:
            # Get a valid token
            token = self.auth.get_valid_token_sync()

            # Create authenticated client
            self._api_client = AuthenticatedClient(
                base_url=self.config.api_base_url,
                token=token,
                headers=self.auth.get_auth_headers(token),
                raise_on_unexpected_status=True,
                verify_ssl=False
            )

        return self._api_client

    def _refresh_client_if_needed(self) -> None:
        """Refresh the API client if authentication has changed."""
        if not self.auth.is_authenticated():
            self._api_client = None

    def _create_api_error_message(self, response: ErrorResponse) -> str:
        """Create a detailed error message from an ErrorResponse."""
        error_obj = response.error if response.error is not UNSET else "N/A"
        if isinstance(error_obj, dict):
            error = json.dumps(error_obj)
        else:
            error = str(error_obj)

        details_obj = response.details if response.details is not UNSET else "N/A"
        if isinstance(details_obj, dict):
            details = json.dumps(details_obj)
        else:
            details = str(details_obj)

        message = response.message if response.message is not UNSET else "No message"

        return f"API Error: {error} | Details: {details} | Message: {message}"

    def create_chat(self, mini_id: int, first_message: str = "Hello"):
        """
        Create a new chat session.

        Args:
            mini_id: The Mini identifier (will be converted to int)
            first_message: First message to start the chat

        Returns:
            Generated chat ID

        Raises:
            MinikaiClientError: If chat creation fails
        """

        try:
            self._refresh_client_if_needed()
            client = self._get_api_client()

            # Create the request
            request = CreateChatRequest(
                mini_id=mini_id,
                first_message_content=first_message
            )

            # Call the API
            response = create_eval_chat.sync(client=client, body=request)

            if isinstance(response, ErrorResponse):
                error_msg = self._create_api_error_message(response)
                raise MinikaiClientError(error_msg)

            if isinstance(response, CreateChatResponse):
                return response

            raise MinikaiClientError(
                "Unexpected response type from create_chat API")

        except AuthenticationError as e:
            raise MinikaiClientError(f"Authentication failed: {e}")
        except Exception as e:
            raise MinikaiClientError(f"Failed to create chat: {e}")

    def send_message(self, mini_id: str, chat_id: str, user_msg: str, previous_messages: Optional[List[UIMessage]] = None) -> UIMessage:
        """
        Send a message and get the assistant's response.

        Args:
            mini_id: The Mini identifier  
            chat_id: The chat session ID
            user_msg: The user's message

        Returns:
            Assistant's UIMessage response

        Raises:
            MinikaiClientError: If message sending fails
        """
        try:
            self._refresh_client_if_needed()
            client = self._get_api_client()

            # Create user message
            user_message = UIMessage(
                role=UIMessageRole.USER,
                content=user_msg
            )

            # Create the request
            messages = list(previous_messages) if previous_messages else []
            messages.append(user_message)

            request = ChatRequest(
                messages=messages,
                chat_id=chat_id,
                mini_id=mini_id,
                timezone=UNSET
            )

            # Call the API
            response = send_eval_message.sync(client=client, body=request)

            if isinstance(response, ErrorResponse):
                error_msg = self._create_api_error_message(response)
                raise MinikaiClientError(error_msg)

            if isinstance(response, SendMessageResponse):
                return response.message

            raise MinikaiClientError(
                "Unexpected response type from send_message API")

        except AuthenticationError as e:
            raise MinikaiClientError(f"Authentication failed: {e}")
        except Exception as e:
            raise MinikaiClientError(f"Failed to send message: {e}")

    def get_messages(self, chat_id: str) -> List[UIMessage]:
        """
        Get all messages from a chat session.

        Args:
            chat_id: The chat session ID

        Returns:
            List of messages in the chat

        Raises:
            MinikaiClientError: If getting messages fails
        """
        try:
            self._refresh_client_if_needed()
            client = self._get_api_client()

            # Call the API
            response = get_eval_messages.sync(client=client, chat_id=chat_id)

            if isinstance(response, ErrorResponse):
                error_msg = self._create_api_error_message(response)
                raise MinikaiClientError(error_msg)

            if isinstance(response, GetMessagesResponse):
                return response.messages

            raise MinikaiClientError(
                "Unexpected response type from get_messages API")

        except AuthenticationError as e:
            raise MinikaiClientError(f"Authentication failed: {e}")
        except Exception as e:
            raise MinikaiClientError(f"Failed to get messages: {e}")

    def is_authenticated(self) -> bool:
        """
        Check if client is currently authenticated.

        Returns:
            True if client is authenticated
        """
        return self.auth.is_authenticated()

    def login(self) -> str:
        """
        Authenticate with the Minikai platform.

        Returns:
            Access token

        Raises:
            MinikaiClientError: If authentication fails
        """
        try:
            token = self.auth.login_sync()
            # Reset API client to use new token
            self._api_client = None
            return token
        except AuthenticationError as e:
            raise MinikaiClientError(f"Authentication failed: {e}")
