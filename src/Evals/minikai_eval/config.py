"""
Configuration management for Minikai Evaluation Library.

This module handles loading configuration from environment variables
for Azure AD authentication and API endpoints.
"""

import os


class Config:
    """
    Configuration class for Minikai client authentication and API settings.

    Loads configuration from environment variables with sensible defaults.
    Use .env file for local development or set environment variables in production.
    """

    def __init__(self):
        """Load configuration from environment variables."""
        # Azure AD Authentication Configuration
        self.azure_tenant_id = os.getenv("AZURE_TENANT_ID")
        self.azure_client_id = os.getenv("AZURE_AD_CLIENT_ID")
        self.azure_client_secret = os.getenv("AZURE_AD_CLIENT_SECRET")

        # API Configuration
        self.api_subscription_key = os.getenv("API_SUBSCRIPTION_KEY")
        self.api_base_url = os.getenv(
            "API_BASE_URL", "https://api.minikai.com")

        # Optional Configuration
        self.default_timezone = os.getenv("DEFAULT_TIMEZONE", "UTC")

    def validate(self) -> None:
        """
        Validate that all required configuration is present.

        Raises:
            ValueError: If required configuration is missing
        """
        required_vars = {
            "AZURE_TENANT_ID": self.azure_tenant_id,
            "AZURE_AD_CLIENT_ID": self.azure_client_id,
            "AZURE_AD_CLIENT_SECRET": self.azure_client_secret,
            "API_SUBSCRIPTION_KEY": self.api_subscription_key,
        }

        missing_vars = [name for name,
                        value in required_vars.items() if not value]

        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                "Please check your .env file or environment configuration."
            )

    def get_auth_config(self) -> dict:
        """
        Get authentication configuration dictionary.

        Returns:
            Dictionary with Azure AD configuration
        """
        return {
            "tenant_id": self.azure_tenant_id,
            "client_id": self.azure_client_id,
            "client_secret": self.azure_client_secret,
        }

    def get_api_config(self) -> dict:
        """
        Get API configuration dictionary.

        Returns:
            Dictionary with API configuration
        """
        return {
            "base_url": self.api_base_url,
            "subscription_key": self.api_subscription_key,
        }

    def __repr__(self) -> str:
        """String representation with masked secrets."""
        return (
            f"Config("
            f"tenant_id={'*' * 8 if self.azure_tenant_id else None}, "
            f"client_id={'*' * 8 if self.azure_client_id else None}, "
            f"base_url={self.api_base_url})"
        )


def load_config() -> Config:
    """
    Load and validate configuration.

    Returns:
        Validated Config instance

    Raises:
        ValueError: If required configuration is missing
    """
    config = Config()
    config.validate()
    return config


def try_load_dotenv() -> None:
    """
    Attempt to load .env file if python-dotenv is available.

    This is a convenience function that silently continues if
    python-dotenv is not installed, making it optional.
    """
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        # python-dotenv not installed, continue without it
        pass
