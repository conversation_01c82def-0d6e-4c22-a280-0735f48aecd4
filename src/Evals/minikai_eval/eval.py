"""
Main evaluation pipeline with type safety.

This module provides the core functionality for running CSV-driven evaluations
of Mini responses with full type checking and pandas integration.
"""

import time
from pathlib import Path
from typing import Any, Callable, Dict, List

import pandas as pd
from minikai_frontend_client.models.ui_message import UIMessage
from minikai_frontend_client.models.ui_message_role import UIMessageRole
from minikai_frontend_client.types import UNSET
from tqdm import tqdm

from .client import MinikaiClient
from .config import try_load_dotenv
from .constants import (
    REQUIRED_INPUT_COLUMNS,
)
from .schemas import CSVRow, EvaluationConfig


def prepare_previous_messages(chat_id: str, client: MinikaiClient) -> List[UIMessage]:
    """
    For a chatId, get the current message history and prepare it for a new user message.

    Get the list of messages with get_messages(chatId).
    Remove all the system messages from the List[UIMessage].
    Return this list of messages.
    """
    all_messages = client.get_messages(chat_id)

    # Filter out system messages
    previous_messages = [
        msg for msg in all_messages if msg.role != UIMessageRole.SYSTEM
    ]

    return previous_messages


def run_evaluation(
    input_csv_path: str | Path,
    output_csv_path: str | Path,
    extract_fn: Callable[[UIMessage], Dict[str, Any]],
    client: MinikaiClient | None = None,
    config: EvaluationConfig | None = None,
) -> pd.DataFrame:
    """
    Run batch evaluation from CSV input with full type safety.

    This function:
    1. Reads and validates the input CSV
    2. Processes each row through the Mini client
    3. Extracts response fields using the provided function
    4. Writes results to output CSV with configured column order

    Args:
        input_csv_path: Path to input CSV file
        output_csv_path: Path for output CSV file
        client: Mini client instance (creates MinikaiClient if None)
        extract_fn: Function to extract fields from UIMessage responses
        config: Evaluation configuration (uses defaults if None)

    Returns:
        DataFrame with evaluation results

    Raises:
        ValueError: If required CSV columns are missing
        FileNotFoundError: If input CSV doesn't exist
        MinikaiClientError: If authentication or API calls fail
    """
    input_path = Path(input_csv_path)
    output_path = Path(output_csv_path)

    try_load_dotenv()

    if client is None:
        client = MinikaiClient()

    if config is None:
        config = EvaluationConfig(
            input_csv_path=str(input_path), output_csv_path=str(output_path)
        )

    print(f"📂 Loading CSV from {input_path}")
    df = pd.read_csv(input_path)

    missing_columns = REQUIRED_INPUT_COLUMNS - set(df.columns)
    if missing_columns:
        raise ValueError(f"Missing required CSV columns: {missing_columns}")

    print(f"✅ Loaded {len(df)} rows with valid columns")

    def process_row_wrapper(row: pd.Series) -> Dict[str, Any]:
        """
        Process a single DataFrame row, handling validation, API calls, and errors.
        """
        original_row = row.to_dict()
        try:
            validated_row = CSVRow.model_validate(original_row)

            chat_id = validated_row.chat_id
            if not chat_id.strip():
                create_chat_response = client.create_chat(
                    validated_row.mini_id)
                if create_chat_response.success and create_chat_response.chat_id is not UNSET:
                    chat_id = create_chat_response.chat_id
                else:
                    error_message = create_chat_response.error or "Unknown error creating chat"
                    raise RuntimeError(
                        f"Failed to create chat: {error_message}")

            response = _send_message_with_retry(
                client=client,
                mini_id=validated_row.mini_id,
                chat_id=chat_id,
                user_message=validated_row.user_message,
                max_retries=config.max_retries,
                retry_delay=config.retry_delay_seconds,
            )

            extracted_fields = extract_fn(response)

            result = {k.lower(): v for k, v in original_row.items()}
            result.update(extracted_fields)
            return result

        except BaseException as e:
            print(f"❌ Error processing row: {e}")
            error_info = {"error": str(e)}
            return {**original_row, **error_info}

    tqdm.pandas(desc="Processing rows")
    results_list = df.progress_apply(process_row_wrapper, axis=1).tolist()

    results_df = pd.DataFrame(results_list)

    print(f"💾 Writing results to {output_path}")
    results_df.to_csv(output_path, index=False)

    print(f"✅ Evaluation complete! {len(results_df)} rows processed")
    return results_df


def _send_message_with_retry(
    client: MinikaiClient,
    mini_id: str,
    chat_id: str,
    user_message: str,
    max_retries: int,
    retry_delay: int,
) -> UIMessage:
    """Send message with retry logic for empty responses."""

    for attempt in range(max_retries + 1):
        try:
            response = client.send_message(mini_id, chat_id, user_message)

            # Check if response is empty (simulate the 20s retry requirement)
            empty_content = not response.content or response.content.strip() == ""
            empty_parts = response.parts is None or len(response.parts) == 0

            if empty_content and empty_parts:
                if attempt < max_retries:
                    print(
                        f"🔄 Empty response, retrying in {retry_delay}s (attempt {attempt + 1})"
                    )
                    time.sleep(retry_delay)
                    continue
                else:
                    print(f"⚠️ Empty response after {max_retries} retries")

            return response

        except BaseException as e:
            if attempt < max_retries:
                print(
                    f"🔄 Error sending message, retrying in {retry_delay}s: {e}")
                time.sleep(retry_delay)
                continue
            else:
                raise e

    raise RuntimeError("Unexpected end of retry loop")
