"""
Extensible column extractors for UIMessage analysis.

This module provides the extract_fields function and related utilities for 
deriving metrics and columns from assistant UIMessage responses. Engineers 
can easily extend this by adding new extraction functions.
"""

from datetime import datetime
from typing import Any, Dict, List

from minikai_frontend_client.models import UIMessage
from minikai_frontend_client.types import UNSET


def extract_fields(message: UIMessage) -> Dict[str, Any]:
    """
    Extract fields from UIMessage for CSV output (≤30 LOC).

    This function extracts standard metrics from assistant responses.
    Engineers can extend this by:
    1. Adding new extraction functions below
    2. Calling them from this function
    3. Adding new column names to DEFAULT_OUTPUT_ORDER in constants.py

    Args:
        message: The assistant's UIMessage response

    Returns:
        Dictionary of extracted fields for DataFrame columns
    """
    # Extract basic text content
    text_content = _extract_text_content(message)

    # Extract tool usage information
    tool_calls = _extract_tool_calls(message)

    # Get timestamp, handling UNSET type
    timestamp = message.created_at if message.created_at is not UNSET else datetime.now()
    timestamp_str = timestamp.isoformat() if timestamp else datetime.now().isoformat()

    fields = {
        "assistant_response_text": text_content,
        "assistant_response_length": len(text_content) if text_content else 0,
        "tool_calls_count": len(tool_calls),
        "tool_calls_names": ", ".join([call["name"] for call in tool_calls]),
        "has_tool_calls": len(tool_calls) > 0,
        "response_timestamp": timestamp_str,
    }

    return fields


def _extract_text_content(message: UIMessage) -> str:
    """Extract all text content from message parts."""
    # Handle UNSET parts
    if message.parts is UNSET or not message.parts:
        return message.content or ""

    text_parts = []
    for part in message.parts:
        # Check if part has the right structure and is a text part
        if (
            hasattr(part, "additional_properties")
            and part.additional_properties.get("type") == "text"
        ):
            text_content = part.additional_properties.get("text", "")
            if text_content:
                text_parts.append(text_content)

    return " ".join(text_parts) if text_parts else (message.content or "")


def extract_system_message_content(messages: List[UIMessage]) -> str:
    """Extract all text content from message parts."""
    # Handle UNSET parts
    for message in messages:
        if message.parts is UNSET or not message.parts:
            return message.content or ""

        if message.role == "system":
            text_parts = []
            for part in message.parts:
                # Check if part has the right structure and is a text part
                if (
                    hasattr(part, "additional_properties")
                    and part.additional_properties.get("type") == "text"
                ):
                    text_content = part.additional_properties.get("text", "")
                    if text_content:
                        text_parts.append(text_content)

    return " ".join(text_parts) if text_parts else (message.content or "")


def _extract_tool_calls(message: UIMessage) -> List[Dict[str, Any]]:
    """Extract tool call information from message parts."""
    # Handle UNSET parts
    if message.parts is UNSET or not message.parts:
        return []

    tool_calls = []
    for part in message.parts:
        # Check if part has the right structure and is a tool-invocation part
        if (
            hasattr(part, "additional_properties")
            and part.additional_properties.get("type") == "tool-invocation"
        ):
            tool_inv = part.additional_properties.get("toolInvocation", {})
            if tool_inv:
                tool_calls.append(
                    {
                        "name": tool_inv.get("toolName", "unknown"),
                        "args": tool_inv.get("args", {}),
                        "has_result": "result" in tool_inv,
                        "state": tool_inv.get("state", "unknown"),
                    }
                )

    return tool_calls
