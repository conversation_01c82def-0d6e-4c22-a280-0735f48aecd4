"""
Pydantic schemas for CSV validation and data structures.
"""

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class CSVRow(BaseModel):
    """
    Schema for validating CSV input rows.

    Represents a single row from the input CSV with the five required columns:
    - MiniId: Identifier for the Mini to evaluate
    - User message: The user's input message  
    """
    mini_id: int = Field(alias="MiniId")
    user_message: str = Field(alias="User message", min_length=1)
    chat_id: str = Field(alias="ChatId", default="")



class EvaluationConfig(BaseModel):
    """Configuration for the evaluation process."""
    input_csv_path: str
    output_csv_path: str
    retry_delay_seconds: int = 5
    max_retries: int = 1
    output_column_order: Optional[list[str]] = None
