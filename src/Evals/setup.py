"""Setup script for minikai-eval package."""

from setuptools import setup, find_packages

setup(
    name="minikai-eval",
    version="0.1.0",
    description="Lightweight CSV-driven evaluation library for Minikai",
    packages=find_packages(),
    python_requires=">=3.11",
    install_requires=[
        "pandas>=2.0.0",
        "pydantic>=2.0.0",
        "typing-extensions>=4.0.0",
        "tqdm>=4.65.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "mypy>=1.5.0",
            "black>=23.0.0",
        ]
    },
)