using Microsoft.Extensions.Logging;
using Minikai.Application.Common.Interfaces;

namespace Minikai.Infrastructure.Client;

/// <summary>
/// Client for generating populated instructions by combining template versions and profiles
/// </summary>
public class MiniInstructionsClient : IMiniInstructionsClient
{
    private readonly IMiniTemplateVersionsClient _miniTemplateVersionsClient;
    private readonly IMiniProfilesClient _miniProfilesClient;
    private readonly ILogger<MiniInstructionsClient> _logger;

    public MiniInstructionsClient(
        IMiniTemplateVersionsClient miniTemplateVersionsClient,
        IMiniProfilesClient miniProfilesClient,
        ILogger<MiniInstructionsClient> logger)
    {
        _miniTemplateVersionsClient = miniTemplateVersionsClient;
        _miniProfilesClient = miniProfilesClient;
        _logger = logger;
    }

    public async Task<string?> GetPopulatedInstructionsAsync(int miniId, Guid? templateId, CancellationToken cancellationToken = default)
    {
        if (!templateId.HasValue)
        {
            _logger.LogDebug("No template ID provided for mini {MiniId}", miniId);
            return null;
        }

        try
        {
            // Get latest template version
            var templateVersion = await _miniTemplateVersionsClient.GetLatestMiniTemplateVersionAsync(templateId.Value, cancellationToken);
            if (templateVersion == null)
            {
                _logger.LogWarning("No template version found for template {TemplateId}", templateId.Value);
                return null;
            }

            // Get latest profile
            var profile = await _miniProfilesClient.GetLatestMiniProfileAsync(miniId, cancellationToken);
            
            // If no profile exists, still replace system tokens but skip profile tokens
            if (profile == null)
            {
                _logger.LogDebug("No profile found for mini {MiniId}, returning template system prompt with system tokens replaced", miniId);
                return ReplaceSystemTokens(templateVersion.System);
            }

            // Replace all tokens in the system prompt
            var instructions = templateVersion.System;
            
            // First, replace system tokens
            instructions = ReplaceSystemTokens(instructions);
            
            // Then, replace profile tokens
            foreach (var value in profile.Values)
            {
                var token = $"{{{{{value.Name}}}}}";
                instructions = instructions.Replace(token, value.Value);
                _logger.LogTrace("Replaced token {Token} with value for mini {MiniId}", token, miniId);
            }

            _logger.LogDebug("Successfully populated instructions for mini {MiniId} using template {TemplateId} and profile", miniId, templateId.Value);
            return instructions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting populated instructions for mini {MiniId} with template {TemplateId}", miniId, templateId);
            return null; // Don't break the main query if instructions fail
        }
    }

    /// <summary>
    /// Replaces system-defined tokens in the provided text with their current values
    /// </summary>
    /// <param name="text">The text containing system tokens to replace</param>
    /// <returns>The text with system tokens replaced</returns>
    private string ReplaceSystemTokens(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        try
        {
            // Replace {{CurrentDateTime}} with current UTC time in ISO format
            var currentDateTime = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            text = text.Replace("{{CurrentDateTime}}", currentDateTime);
            
            _logger.LogTrace("Replaced {{CurrentDateTime}} token with {DateTime}", currentDateTime);
            
            return text;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error replacing system tokens, returning original text");
            return text;
        }
    }
}
