using Minikai.Application.Common.Interfaces;
using Minikai.Domain.Entities;

namespace Minikai.Infrastructure.Client;

/// <summary>
/// Service for managing mini profiles in CosmosDB
/// </summary>
public class MiniProfilesClient : IMiniProfilesClient
{
    private readonly CosmosDbClient _cosmosDbClient;
    
    // Constants for container and database names
    private const string MiniProfilesContainer = "MiniProfiles";
    private const string ProfilesDatabaseId = "ProfilesDatabase";
    
    public MiniProfilesClient(CosmosDbClient cosmosDbClient)
    {
        _cosmosDbClient = cosmosDbClient;
    }
    
    /// <summary>
    /// Creates a new mini profile
    /// </summary>
    public async Task<MiniProfile> CreateMiniProfileAsync(MiniProfile profile, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(profile, nameof(profile));
        Guard.Against.NullOrEmpty(profile.Id, nameof(profile.Id));
        
        var partitionKey = profile.MiniId;

        
        // Use the miniId as the partition key and specify the ProfilesDatabase
        return await _cosmosDbClient.CreateItemAsync(
            MiniProfilesContainer,
            profile, 
            partitionKey, // Using miniId as partition key
            ProfilesDatabaseId, 
            cancellationToken);
    }
    
    /// <summary>
    /// Gets paginated profiles for a specific mini
    /// </summary>
    public async Task<(List<MiniProfile> Items, int TotalCount)> GetMiniProfilesAsync(int miniId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        Guard.Against.NegativeOrZero(pageNumber, nameof(pageNumber));
        Guard.Against.NegativeOrZero(pageSize, nameof(pageSize));
        
        var miniIdString = miniId.ToString();
        var query = "SELECT * FROM c WHERE c.miniId = @miniId ORDER BY c.createdAt DESC";
        var parameters = new Dictionary<string, object> { { "@miniId", miniIdString } };
        
        var (items, totalCount) = await _cosmosDbClient.QueryItemsWithPaginationAsync<MiniProfile>(
            MiniProfilesContainer, 
            query, 
            parameters,
            pageNumber,
            pageSize,
            miniIdString, // Using miniId as partition key for more efficient querying
            ProfilesDatabaseId,
            cancellationToken);
            
        return (items.ToList(), totalCount);
    }
    
    /// <summary>
    /// Gets a specific profile version for a mini
    /// </summary>
    public async Task<MiniProfile> GetMiniProfileAsync(int miniId, string version, CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(version, nameof(version));
        
        var id = $"{miniId}_{version}";
        
        return await _cosmosDbClient.ReadItemAsync<MiniProfile>(
            MiniProfilesContainer,
            id,
            miniId.ToString(), // Using miniId as partition key
            ProfilesDatabaseId,
            cancellationToken);
    }
    
    /// <summary>
    /// Gets the latest profile for a mini
    /// </summary>
    public async Task<MiniProfile?> GetLatestMiniProfileAsync(int miniId, CancellationToken cancellationToken = default)
    {
        var miniIdString = miniId.ToString();
        var query = "SELECT TOP 1 * FROM c WHERE c.miniId = @miniId ORDER BY c.createdAt DESC";
        var parameters = new Dictionary<string, object> { { "@miniId", miniIdString } };
        
        var results = await _cosmosDbClient.QueryItemsWithParametersAsync<MiniProfile>(
            MiniProfilesContainer, 
            query, 
            parameters, 
            miniIdString, // Using miniId as partition key for more efficient querying
            ProfilesDatabaseId,
            cancellationToken);
            
        return results.FirstOrDefault();
    }
    
    /// <summary>
    /// Deletes a specific profile version
    /// </summary>
    public async Task DeleteMiniProfileAsync(int miniId, string version, CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(version, nameof(version));
        
        var id = $"{miniId}_{version}";
        
        await _cosmosDbClient.DeleteItemAsync(
            MiniProfilesContainer,
            id,
            miniId.ToString(), // Using miniId as partition key
            ProfilesDatabaseId,
            cancellationToken);
    }
}
