using Minikai.Application.Common.Interfaces;
using Minikai.Domain.Entities;

namespace Minikai.Infrastructure.Client;

/// <summary>
/// Service for managing mini template versions in CosmosDB
/// </summary>
public class MiniTemplateVersionsClient : IMiniTemplateVersionsClient
{
    private readonly CosmosDbClient _cosmosDbClient;
    
    // Constants for container and database names
    private const string MiniTemplatesContainer = "MiniTemplates";
    private const string ProfilesDatabaseId = "ProfilesDatabase";
    
    public MiniTemplateVersionsClient(CosmosDbClient cosmosDbClient)
    {
        _cosmosDbClient = cosmosDbClient;
    }
    
    /// <summary>
    /// Creates a new mini template version
    /// </summary>
    public async Task<MiniTemplateVersion> CreateMiniTemplateVersionAsync(MiniTemplateVersion templateVersion, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(templateVersion, nameof(templateVersion));
        Guard.Against.NullOrEmpty(templateVersion.Id, nameof(templateVersion.Id));
        
        // Use the templateId as the partition key and specify the ProfilesDatabase
        return await _cosmosDbClient.CreateItemAsync(
            MiniTemplatesContainer,
            templateVersion, 
            templateVersion.TemplateId.ToString(), // Using templateId as partition key
            ProfilesDatabaseId, 
            cancellationToken);
    }
    
    /// <summary>
    /// Gets paginated versions for a specific template
    /// </summary>
    public async Task<(List<MiniTemplateVersion> Items, int TotalCount)> GetMiniTemplateVersionsAsync(Guid templateId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(templateId, nameof(templateId));
        Guard.Against.NegativeOrZero(pageNumber, nameof(pageNumber));
        Guard.Against.NegativeOrZero(pageSize, nameof(pageSize));
        
        var query = "SELECT * FROM c WHERE c.templateId = @templateId ORDER BY c.createdAt DESC";
        var parameters = new Dictionary<string, object> { { "@templateId", templateId } };
        
        var (items, totalCount) = await _cosmosDbClient.QueryItemsWithPaginationAsync<MiniTemplateVersion>(
            MiniTemplatesContainer, 
            query, 
            parameters,
            pageNumber,
            pageSize,
            templateId.ToString(), // Using templateId as partition key for more efficient querying
            ProfilesDatabaseId,
            cancellationToken);
            
        return (items.ToList(), totalCount);
    }
    
    /// <summary>
    /// Gets a specific version of a template
    /// </summary>
    public async Task<MiniTemplateVersion> GetMiniTemplateVersionAsync(Guid templateId, string version, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(templateId, nameof(templateId));
        Guard.Against.NullOrEmpty(version, nameof(version));
        
        var id = $"{templateId}_{version}";
        
        return await _cosmosDbClient.ReadItemAsync<MiniTemplateVersion>(
            MiniTemplatesContainer,
            id,
            templateId.ToString(), // Using templateId as partition key
            ProfilesDatabaseId,
            cancellationToken);
    }
    
    /// <summary>
    /// Gets the latest version of a template
    /// </summary>
    public async Task<MiniTemplateVersion?> GetLatestMiniTemplateVersionAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(templateId, nameof(templateId));
        
        var query = "SELECT TOP 1 * FROM c WHERE c.templateId = @templateId ORDER BY c.createdAt DESC";
        var parameters = new Dictionary<string, object> { { "@templateId", templateId } };
        
        var results = await _cosmosDbClient.QueryItemsWithParametersAsync<MiniTemplateVersion>(
            MiniTemplatesContainer, 
            query, 
            parameters, 
            templateId.ToString(), // Using templateId as partition key for more efficient querying
            ProfilesDatabaseId,
            cancellationToken);
            
        return results.FirstOrDefault();
    }
    
    /// <summary>
    /// Deletes a specific version of a template
    /// </summary>
    public async Task DeleteMiniTemplateVersionAsync(Guid templateId, string version, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(templateId, nameof(templateId));
        Guard.Against.NullOrEmpty(version, nameof(version));
        
        var id = $"{templateId}_{version}";
        
        await _cosmosDbClient.DeleteItemAsync(
            MiniTemplatesContainer,
            id,
            templateId.ToString(), // Using templateId as partition key
            ProfilesDatabaseId,
            cancellationToken);
    }
}
