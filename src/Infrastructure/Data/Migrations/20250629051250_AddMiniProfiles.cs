﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Minikai.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddMiniProfiles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Minis_Type",
                table: "Minis");

            migrationBuilder.DropColumn(
                name: "Instructions",
                table: "MiniTemplates");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "MiniTemplates");

            migrationBuilder.DropColumn(
                name: "Instructions",
                table: "Minis");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Minis");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Instructions",
                table: "MiniTemplates",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "MiniTemplates",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Instructions",
                table: "Minis",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "Minis",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Minis_Type",
                table: "Minis",
                column: "Type");
        }
    }
}
