﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Minikai.Infrastructure.Data;

#nullable disable

namespace Minikai.Infrastructure.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250629053631_AddIndexes")]
    partial class AddIndexes
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Chat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("ChatId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastMessageSentDatetime")
                        .HasColumnType("datetime2");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiniId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Summary")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ThreadId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ChatId")
                        .HasDatabaseName("IX_Chats_ChatId");

                    b.HasIndex("MiniId")
                        .HasDatabaseName("IX_Chats_MiniId");

                    b.HasIndex("ThreadId")
                        .HasDatabaseName("IX_Chats_ThreadId");

                    b.ToTable("Chats");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ContentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DocFileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ExternalId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ExternalSource")
                        .HasColumnType("nvarchar(450)");

                    b.Property<byte[]>("FileData")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("FileExtension")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<long>("Size")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("DocFileId")
                        .IsUnique()
                        .HasDatabaseName("IX_DocumentFiles_DocFileId");

                    b.HasIndex("FileId")
                        .HasDatabaseName("IX_DocumentFiles_FileId");

                    b.HasIndex("GroupId")
                        .HasDatabaseName("IX_DocumentFiles_GroupId");

                    b.HasIndex("ExternalId", "ExternalSource")
                        .HasDatabaseName("IX_DocumentFiles_ExternalId_ExternalSource")
                        .HasFilter("[ExternalId] IS NOT NULL AND [ExternalSource] IS NOT NULL");

                    b.ToTable("DocumentFiles");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Data")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DocId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("GroupId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ListId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("Draft");

                    b.HasKey("Id");

                    b.HasIndex("DocId")
                        .HasDatabaseName("IX_DocumentItems_DocId");

                    b.HasIndex("Title")
                        .HasDatabaseName("IX_DocumentItems_Title");

                    b.HasIndex("ListId", "GroupId")
                        .HasDatabaseName("IX_DocumentItems_ListId_GroupId");

                    b.ToTable("DocumentItems");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentItemFile", b =>
                {
                    b.Property<int>("DocumentItemId")
                        .HasColumnType("int");

                    b.Property<int>("DocumentFileId")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("DocumentItemId", "DocumentFileId");

                    b.HasIndex("DocumentFileId");

                    b.ToTable("DocumentItemFiles");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentList", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("DefaultMiniId")
                        .HasColumnType("int");

                    b.Property<string>("GroupId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("GroupId")
                        .HasDatabaseName("IX_DocumentLists_GroupId");

                    b.HasIndex("Title")
                        .HasDatabaseName("IX_DocumentLists_Title");

                    b.ToTable("DocumentLists");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentToChat", b =>
                {
                    b.Property<int>("DocId")
                        .HasColumnType("int");

                    b.Property<int>("ChatId")
                        .HasColumnType("int");

                    b.HasKey("DocId", "ChatId");

                    b.HasIndex("ChatId")
                        .HasDatabaseName("IX_DocumentToChats_ChatId");

                    b.HasIndex("DocId")
                        .HasDatabaseName("IX_DocumentToChats_DocId");

                    b.ToTable("DocumentToChats");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Group", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("GroupId")
                        .HasDatabaseName("IX_Groups_GroupId");

                    b.ToTable("Groups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Mini", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("ExternalId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ExternalSource")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MiniGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("TemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("MiniGroupId")
                        .HasDatabaseName("IX_Minis_MiniGroupId");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Minis_Name");

                    b.HasIndex("TemplateId")
                        .HasDatabaseName("IX_Minis_TemplateId");

                    b.HasIndex("ExternalId", "ExternalSource")
                        .HasDatabaseName("IX_Minis_ExternalId_ExternalSource")
                        .HasFilter("[ExternalId] IS NOT NULL AND [ExternalSource] IS NOT NULL");

                    b.ToTable("Minis");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("GroupId")
                        .HasDatabaseName("IX_MiniGroups_GroupId");

                    b.ToTable("MiniGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_MiniTemplates_Name");

                    b.ToTable("MiniTemplates");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniTemplateToGroup", b =>
                {
                    b.Property<Guid>("MiniTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("GroupId")
                        .HasColumnType("int");

                    b.HasKey("MiniTemplateId", "GroupId");

                    b.HasIndex("GroupId")
                        .HasDatabaseName("IX_MiniTemplateToGroups_GroupId");

                    b.HasIndex("MiniTemplateId")
                        .HasDatabaseName("IX_MiniTemplateToGroups_MiniTemplateId");

                    b.ToTable("MiniTemplateToGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniTemplateToTool", b =>
                {
                    b.Property<Guid>("MiniTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ToolId")
                        .HasColumnType("int");

                    b.HasKey("MiniTemplateId", "ToolId");

                    b.HasIndex("ToolId");

                    b.ToTable("MiniTemplateToTools");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniToDocumentFile", b =>
                {
                    b.Property<int>("MiniId")
                        .HasColumnType("int");

                    b.Property<int>("DocumentFileId")
                        .HasColumnType("int");

                    b.HasKey("MiniId", "DocumentFileId");

                    b.HasIndex("DocumentFileId");

                    b.HasIndex("MiniId")
                        .HasDatabaseName("IX_MiniToDocumentFiles_MiniId");

                    b.ToTable("MiniToDocumentFiles");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniToGroup", b =>
                {
                    b.Property<int>("MiniId")
                        .HasColumnType("int");

                    b.Property<int>("GroupId")
                        .HasColumnType("int");

                    b.HasKey("MiniId", "GroupId");

                    b.HasIndex("GroupId")
                        .HasDatabaseName("IX_MiniToGroups_GroupId");

                    b.HasIndex("MiniId")
                        .HasDatabaseName("IX_MiniToGroups_MiniId");

                    b.ToTable("MiniToGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniToTool", b =>
                {
                    b.Property<int>("MiniId")
                        .HasColumnType("int");

                    b.Property<int>("ToolId")
                        .HasColumnType("int");

                    b.HasKey("MiniId", "ToolId");

                    b.HasIndex("MiniId")
                        .HasDatabaseName("IX_MiniToTools_MiniId");

                    b.HasIndex("ToolId")
                        .HasDatabaseName("IX_MiniToTools_ToolId");

                    b.ToTable("MiniToTools");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Tool", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Schema")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.HasKey("Id");

                    b.ToTable("Tools");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.ToTable("UserGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroupMember", b =>
                {
                    b.Property<int>("UserGroupId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsManager")
                        .HasColumnType("bit");

                    b.HasKey("UserGroupId", "UserId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserGroupMembers_UserId");

                    b.HasIndex("UserId", "IsManager")
                        .HasDatabaseName("IX_UserGroupMembers_UserId_IsManager");

                    b.ToTable("UserGroupMembers");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroupToMiniGroup", b =>
                {
                    b.Property<int>("UserGroupId")
                        .HasColumnType("int");

                    b.Property<Guid>("MiniGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserGroupId", "MiniGroupId");

                    b.HasIndex("MiniGroupId")
                        .HasDatabaseName("IX_UserGroupToMiniGroups_MiniGroupId");

                    b.HasIndex("UserGroupId")
                        .HasDatabaseName("IX_UserGroupToMiniGroups_UserGroupId");

                    b.ToTable("UserGroupToMiniGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserToChat", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ChatId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "ChatId");

                    b.HasIndex("ChatId")
                        .HasDatabaseName("IX_UserToChats_ChatId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserToChats_UserId");

                    b.ToTable("UserToChats");
                });

            modelBuilder.Entity("Minikai.Infrastructure.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Minikai.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Minikai.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Minikai.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentItem", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.DocumentList", "List")
                        .WithMany("Items")
                        .HasForeignKey("ListId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("List");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentItemFile", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.DocumentFile", "DocumentFile")
                        .WithMany("DocumentItemFiles")
                        .HasForeignKey("DocumentFileId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.DocumentItem", "DocumentItem")
                        .WithMany("DocumentItemFiles")
                        .HasForeignKey("DocumentItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentFile");

                    b.Navigation("DocumentItem");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentToChat", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Chat", "Chat")
                        .WithMany("DocumentToChats")
                        .HasForeignKey("ChatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.DocumentItem", "Document")
                        .WithMany()
                        .HasForeignKey("DocId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Chat");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Mini", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.MiniGroup", "MiniGroup")
                        .WithMany("Minis")
                        .HasForeignKey("MiniGroupId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Minikai.Domain.Entities.MiniTemplate", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId");

                    b.Navigation("MiniGroup");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniGroup", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupId")
                        .HasPrincipalKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniTemplateToGroup", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.MiniTemplate", "MiniTemplate")
                        .WithMany("MiniTemplateToGroups")
                        .HasForeignKey("MiniTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("MiniTemplate");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniTemplateToTool", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.MiniTemplate", "MiniTemplate")
                        .WithMany("MiniTemplateToTools")
                        .HasForeignKey("MiniTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.Tool", "Tool")
                        .WithMany()
                        .HasForeignKey("ToolId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MiniTemplate");

                    b.Navigation("Tool");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniToDocumentFile", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.DocumentFile", "DocumentFile")
                        .WithMany("MiniToDocumentFiles")
                        .HasForeignKey("DocumentFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.Mini", "Mini")
                        .WithMany("MiniToDocumentFiles")
                        .HasForeignKey("MiniId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentFile");

                    b.Navigation("Mini");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniToGroup", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Group", "Group")
                        .WithMany("MiniToGroups")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.Mini", "Mini")
                        .WithMany("MiniToGroups")
                        .HasForeignKey("MiniId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Mini");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniToTool", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Mini", "Mini")
                        .WithMany("MiniToTools")
                        .HasForeignKey("MiniId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.Tool", "Tool")
                        .WithMany("MiniToTools")
                        .HasForeignKey("ToolId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Mini");

                    b.Navigation("Tool");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroup", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupId")
                        .HasPrincipalKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroupMember", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.UserGroup", "UserGroup")
                        .WithMany("Members")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroupToMiniGroup", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.MiniGroup", "MiniGroup")
                        .WithMany("UserGroupToMiniGroups")
                        .HasForeignKey("MiniGroupId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Minikai.Domain.Entities.UserGroup", "UserGroup")
                        .WithMany("UserGroupToMiniGroups")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MiniGroup");

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserToChat", b =>
                {
                    b.HasOne("Minikai.Domain.Entities.Chat", "Chat")
                        .WithMany("UserToChats")
                        .HasForeignKey("ChatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Chat");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Chat", b =>
                {
                    b.Navigation("DocumentToChats");

                    b.Navigation("UserToChats");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentFile", b =>
                {
                    b.Navigation("DocumentItemFiles");

                    b.Navigation("MiniToDocumentFiles");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentItem", b =>
                {
                    b.Navigation("DocumentItemFiles");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.DocumentList", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Group", b =>
                {
                    b.Navigation("MiniToGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Mini", b =>
                {
                    b.Navigation("MiniToDocumentFiles");

                    b.Navigation("MiniToGroups");

                    b.Navigation("MiniToTools");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniGroup", b =>
                {
                    b.Navigation("Minis");

                    b.Navigation("UserGroupToMiniGroups");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.MiniTemplate", b =>
                {
                    b.Navigation("MiniTemplateToGroups");

                    b.Navigation("MiniTemplateToTools");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.Tool", b =>
                {
                    b.Navigation("MiniToTools");
                });

            modelBuilder.Entity("Minikai.Domain.Entities.UserGroup", b =>
                {
                    b.Navigation("Members");

                    b.Navigation("UserGroupToMiniGroups");
                });
#pragma warning restore 612, 618
        }
    }
}
