﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Minikai.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_DocumentItems_ListId",
                table: "DocumentItems");

            migrationBuilder.AlterColumn<string>(
                name: "GroupId",
                table: "DocumentLists",
                type: "nvarchar(450)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "GroupId",
                table: "DocumentItems",
                type: "nvarchar(450)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserToChats_UserId",
                table: "UserToChats",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupToMiniGroups_UserGroupId",
                table: "UserGroupToMiniGroups",
                column: "UserGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMembers_UserId",
                table: "UserGroupMembers",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMembers_UserId_IsManager",
                table: "UserGroupMembers",
                columns: new[] { "UserId", "IsManager" });

            migrationBuilder.CreateIndex(
                name: "IX_MiniToTools_MiniId",
                table: "MiniToTools",
                column: "MiniId");

            migrationBuilder.CreateIndex(
                name: "IX_MiniToGroups_MiniId",
                table: "MiniToGroups",
                column: "MiniId");

            migrationBuilder.CreateIndex(
                name: "IX_MiniTemplateToGroups_MiniTemplateId",
                table: "MiniTemplateToGroups",
                column: "MiniTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_MiniTemplates_Name",
                table: "MiniTemplates",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Groups_GroupId",
                table: "Groups",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentToChats_DocId",
                table: "DocumentToChats",
                column: "DocId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentLists_GroupId",
                table: "DocumentLists",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentLists_Title",
                table: "DocumentLists",
                column: "Title");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentItems_DocId",
                table: "DocumentItems",
                column: "DocId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentItems_ListId_GroupId",
                table: "DocumentItems",
                columns: new[] { "ListId", "GroupId" });

            migrationBuilder.CreateIndex(
                name: "IX_DocumentItems_Title",
                table: "DocumentItems",
                column: "Title");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UserToChats_UserId",
                table: "UserToChats");

            migrationBuilder.DropIndex(
                name: "IX_UserGroupToMiniGroups_UserGroupId",
                table: "UserGroupToMiniGroups");

            migrationBuilder.DropIndex(
                name: "IX_UserGroupMembers_UserId",
                table: "UserGroupMembers");

            migrationBuilder.DropIndex(
                name: "IX_UserGroupMembers_UserId_IsManager",
                table: "UserGroupMembers");

            migrationBuilder.DropIndex(
                name: "IX_MiniToTools_MiniId",
                table: "MiniToTools");

            migrationBuilder.DropIndex(
                name: "IX_MiniToGroups_MiniId",
                table: "MiniToGroups");

            migrationBuilder.DropIndex(
                name: "IX_MiniTemplateToGroups_MiniTemplateId",
                table: "MiniTemplateToGroups");

            migrationBuilder.DropIndex(
                name: "IX_MiniTemplates_Name",
                table: "MiniTemplates");

            migrationBuilder.DropIndex(
                name: "IX_Groups_GroupId",
                table: "Groups");

            migrationBuilder.DropIndex(
                name: "IX_DocumentToChats_DocId",
                table: "DocumentToChats");

            migrationBuilder.DropIndex(
                name: "IX_DocumentLists_GroupId",
                table: "DocumentLists");

            migrationBuilder.DropIndex(
                name: "IX_DocumentLists_Title",
                table: "DocumentLists");

            migrationBuilder.DropIndex(
                name: "IX_DocumentItems_DocId",
                table: "DocumentItems");

            migrationBuilder.DropIndex(
                name: "IX_DocumentItems_ListId_GroupId",
                table: "DocumentItems");

            migrationBuilder.DropIndex(
                name: "IX_DocumentItems_Title",
                table: "DocumentItems");

            migrationBuilder.AlterColumn<string>(
                name: "GroupId",
                table: "DocumentLists",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "GroupId",
                table: "DocumentItems",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DocumentItems_ListId",
                table: "DocumentItems",
                column: "ListId");
        }
    }
}
