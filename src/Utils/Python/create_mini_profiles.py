#!/usr/bin/env python3
"""
Script to create initial profiles for all existing Minis.
Each mini will get a profile with the Name token filled with the mini's name.
"""

import os
import sys
import requests
import logging
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mini_profiles.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MiniProfileCreator:
    def __init__(self):
        """Initialize the mini profile creator with environment variables."""
        load_dotenv()
        
        self.api_base_url = os.getenv('API_BASE_URL')
        self.jwt_token = os.getenv('JWT_TOKEN')
        self.group_id = os.getenv('GROUP_ID')  # Optional group ID to filter minis
        self.subscription_key = os.getenv('OCP_APIM_SUBSCRIPTION_KEY')
        
        # Validate required environment variables
        required_vars = ['API_BASE_URL', 'JWT_TOKEN', 'OCP_APIM_SUBSCRIPTION_KEY']

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        self.access_token: Optional[str] = None
        self.session = requests.Session()
        
        # Disable SSL verification for localhost development
        if 'localhost' in self.api_base_url.lower():
            self.session.verify = False
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            logger.warning("SSL verification disabled for localhost development")
        
        logger.info("Mini Profile Creator initialized")
        logger.info(f"API Base URL: {self.api_base_url}")
        
        # Set up the session with the provided JWT token and subscription key
        self.session.headers.update({
            'Authorization': f'Bearer {self.jwt_token}',
            'Ocp-Apim-Subscription-Key': self.subscription_key
        })
        logger.info("Using provided JWT token for authentication")

    def get_minis(self) -> List[Dict]:
        """Fetch all Minis from the API."""
        logger.info("Fetching all Minis...")
        
        url = f"{self.api_base_url}/minis"
        params = {}
        
        # Add groupId parameter if specified
        if self.group_id:
            params['groupId'] = self.group_id
            logger.info(f"Filtering by group ID: {self.group_id}")
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            minis = response.json()
            logger.info(f"Successfully fetched {len(minis)} minis")
            
            return minis
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch minis: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response body: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching minis: {e}")
            raise

    def check_existing_profile(self, mini_id: int) -> Optional[Dict]:
        """Check if a mini already has a profile."""
        url = f"{self.api_base_url}/minis/{mini_id}/profile/latest"
        
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 404:
                # No profile exists
                return None
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            if hasattr(e, 'response') and e.response is not None and e.response.status_code == 404:
                # No profile exists
                return None
            logger.error(f"Error checking existing profile: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error checking existing profile: {e}")
            raise

    def create_mini_profile(self, mini: Dict) -> str:
        """Create a profile for a single mini with Name token if it doesn't already exist."""
        mini_id = mini.get('id')
        mini_name = mini.get('name', 'Unknown')
        template_id = mini.get('templateId')
        
        logger.info(f"Processing mini: {mini_name} (ID: {mini_id})")
        
        # Validate mini has a template
        if not template_id:
            logger.warning(f"⚠️  Mini {mini_name} has no template assigned - skipping")
            return "skipped"
        
        # Check if profile already exists
        try:
            existing_profile = self.check_existing_profile(mini_id)
            if existing_profile:
                logger.info(f"⏭️  Mini {mini_name} already has profile {existing_profile.get('version', 'unknown')} - skipping")
                return "skipped"
        except Exception as e:
            logger.error(f"❌ Failed to check existing profile for mini {mini_name}: {e}")
            return "error"
        
        logger.info(f"Creating new profile for mini: {mini_name}")
        
        # Prepare the profile data with Name token
        profile_data = {
            "values": [
                {
                    "name": "Name",
                    "value": mini_name
                }
            ]
        }
        
        url = f"{self.api_base_url}/minis/{mini_id}/profiles"
        
        try:
            response = self.session.post(
                url,
                json=profile_data,
                timeout=30
            )
            response.raise_for_status()
            
            version = response.json()
            logger.info(f"✅ Successfully created profile {version} for mini: {mini_name}")
            return "created"
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to create profile for mini {mini_name}: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response body: {e.response.text}")
            return "error"
        except Exception as e:
            logger.error(f"❌ Unexpected error creating profile for mini {mini_name}: {e}")
            return "error"

    def run(self):
        """Main execution method."""
        logger.info("Starting mini profile creation process...")
        
        try:
            # Step 1: Fetch all minis
            minis = self.get_minis()
            
            if not minis:
                logger.warning("No minis found. Nothing to process.")
                return
            
            # Step 3: Create profiles for each mini
            created_count = 0
            skipped_count = 0
            error_count = 0
            
            for i, mini in enumerate(minis, 1):
                logger.info(f"Processing mini {i}/{len(minis)}")
                
                result = self.create_mini_profile(mini)
                if result == "created":
                    created_count += 1
                elif result == "skipped":
                    skipped_count += 1
                else:  # "error"
                    error_count += 1
            
            # Step 4: Report summary
            logger.info("=" * 50)
            logger.info("SUMMARY")
            logger.info("=" * 50)
            logger.info(f"Total minis processed: {len(minis)}")
            logger.info(f"New profiles created: {created_count}")
            logger.info(f"Minis skipped (already have profiles or no template): {skipped_count}")
            logger.info(f"Errors encountered: {error_count}")
            
            if error_count > 0:
                logger.warning(f"⚠️  {error_count} minis encountered errors. Check logs above for details.")
            elif created_count == 0 and skipped_count > 0:
                logger.info("ℹ️  All minis already have profiles or no templates - no new profiles created.")
            else:
                logger.info("🎉 All minis successfully processed!")
                
        except Exception as e:
            logger.error(f"Script execution failed: {e}")
            sys.exit(1)

def main():
    """Entry point for the script."""
    try:
        creator = MiniProfileCreator()
        creator.run()
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
