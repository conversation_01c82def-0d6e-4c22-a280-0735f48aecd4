#!/usr/bin/env python3
"""
<PERSON>ript to create initial versions for all existing MiniTemplates.
Each template will get a version with a single {{Name}} token.
"""

import os
import sys
import requests
import logging
import json
import yaml
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('template_versions.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TemplateVersionCreator:
    def __init__(self):
        """Initialize the template version creator with environment variables."""
        load_dotenv()
        
        self.api_base_url = os.getenv('API_BASE_URL')
        self.jwt_token = os.getenv('JWT_TOKEN')
        self.subscription_key = os.getenv('OCP_APIM_SUBSCRIPTION_KEY')
        
        # Validate required environment variables
        required_vars = ['API_BASE_URL', 'JWT_TOKEN', 'OCP_APIM_SUBSCRIPTION_KEY']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        self.access_token: Optional[str] = None
        self.session = requests.Session()
        self.instructions_mapping = self.load_instructions_mapping()
        
        # Disable SSL verification for localhost development
        if 'localhost' in self.api_base_url.lower():
            self.session.verify = False
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            logger.warning("SSL verification disabled for localhost development")
        
        logger.info("Template Version Creator initialized")
        logger.info(f"API Base URL: {self.api_base_url}")
        
        # Set up the session with the provided JWT token and subscription key
        self.session.headers.update({
            'Authorization': f'Bearer {self.jwt_token}',
            'Ocp-Apim-Subscription-Key': self.subscription_key
        })
        logger.info("Using provided JWT token for authentication")

    def load_instructions_mapping(self) -> Dict[str, str]:
        """Load the template instructions mapping from YAML file."""
        # Try YAML file first, then fall back to JSON for backward compatibility
        yaml_file = os.path.join(os.path.dirname(__file__), 'template_instructions_mapping.yaml')
        json_file = os.path.join(os.path.dirname(__file__), 'template_instructions_mapping.json')
        
        # Try YAML first
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                mapping = data.get('template_instructions', {})
                logger.info(f"Loaded instructions mapping from YAML for {len(mapping)} templates")
                return mapping
        except FileNotFoundError:
            logger.info(f"YAML mapping file not found: {yaml_file}, trying JSON fallback")
        except yaml.YAMLError as e:
            logger.error(f"Invalid YAML in mapping file: {e}")
            logger.warning("Trying JSON fallback")
        except Exception as e:
            logger.error(f"Error loading YAML mapping: {e}")
            logger.warning("Trying JSON fallback")
        
        # Fall back to JSON
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                mapping = data.get('template_instructions', {})
                logger.info(f"Loaded instructions mapping from JSON for {len(mapping)} templates")
                return mapping
        except FileNotFoundError:
            logger.warning(f"Neither YAML nor JSON mapping files found")
            logger.warning("Will use existing template instructions as fallback")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in mapping file: {e}")
            logger.warning("Will use existing template instructions as fallback")
            return {}
        except Exception as e:
            logger.error(f"Error loading instructions mapping: {e}")
            logger.warning("Will use existing template instructions as fallback")
            return {}

    def get_mini_templates(self) -> List[Dict]:
        """Fetch all MiniTemplates from the API."""
        logger.info("Fetching all MiniTemplates...")
        
        url = f"{self.api_base_url}/minitemplates"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            templates = response.json()
            logger.info(f"Successfully fetched {len(templates)} templates")
            
            return templates
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch templates: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response body: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching templates: {e}")
            raise

    def check_existing_version(self, template_id: str) -> Optional[Dict]:
        """Check if a template already has a version."""
        url = f"{self.api_base_url}/minitemplates/{template_id}/versions/latest"
        
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 404:
                # No version exists
                return None
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            if hasattr(e, 'response') and e.response is not None and e.response.status_code == 404:
                # No version exists
                return None
            logger.error(f"Error checking existing version: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error checking existing version: {e}")
            raise

    def create_template_version(self, template: Dict) -> str:
        """Create a version for a single template with {{Name}} token if it doesn't already exist."""
        template_id = template.get('id')
        template_name = template.get('name', 'Unknown')
        fallback_instructions = template.get('instructions')
        
        logger.info(f"Processing template: {template_name} (ID: {template_id})")
        
        # Check if version already exists
        try:
            existing_version = self.check_existing_version(template_id)
            if existing_version:
                logger.info(f"⏭️  Template {template_name} already has version {existing_version.get('version', 'unknown')} - skipping")
                return "skipped"
        except Exception as e:
            logger.error(f"❌ Failed to check existing version for template {template_name}: {e}")
            return "error"
        
        # Use mapping file instructions first, then fall back to template instructions
        if template_id in self.instructions_mapping:
            instructions = self.instructions_mapping[template_id]
            logger.info(f"Using custom instructions from mapping for template: {template_name} (ID: {template_id})")
        elif fallback_instructions and fallback_instructions.strip():
            instructions = fallback_instructions
            logger.info(f"Using existing template instructions for: {template_name} (ID: {template_id})")
        else:
            logger.warning(f"⚠️  Template {template_name} (ID: {template_id}) has no instructions in mapping or template - skipping version creation")
            return "skipped"
        
        logger.info(f"Creating new version for template: {template_name}")
        
        # Prepare the version data
        version_data = {
            "system": instructions,
            "tokens": [
                {
                    "name": "Name",
                    "type": "string",
                    "description": "Template name parameter"
                }
            ]
        }
        
        url = f"{self.api_base_url}/minitemplates/{template_id}/versions"
        
        try:
            response = self.session.post(
                url,
                json=version_data,
                timeout=30
            )
            response.raise_for_status()
            
            version = response.json()
            logger.info(f"✅ Successfully created version {version} for template: {template_name}")
            return "created"
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to create version for template {template_name}: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response body: {e.response.text}")
            return "error"
        except Exception as e:
            logger.error(f"❌ Unexpected error creating version for template {template_name}: {e}")
            return "error"

    def run(self):
        """Main execution method."""
        logger.info("Starting template version creation process...")
        
        try:
            # Step 1: Fetch all templates
            templates = self.get_mini_templates()
            
            if not templates:
                logger.warning("No templates found. Nothing to process.")
                return
            
            # Step 2: Create versions for each template
            created_count = 0
            skipped_count = 0
            error_count = 0
            
            for i, template in enumerate(templates, 1):
                logger.info(f"Processing template {i}/{len(templates)}")
                
                result = self.create_template_version(template)
                if result == "created":
                    created_count += 1
                elif result == "skipped":
                    skipped_count += 1
                else:  # "error"
                    error_count += 1
            
            # Step 3: Report summary
            logger.info("=" * 50)
            logger.info("SUMMARY")
            logger.info("=" * 50)
            logger.info(f"Total templates processed: {len(templates)}")
            logger.info(f"New versions created: {created_count}")
            logger.info(f"Templates skipped (already have versions): {skipped_count}")
            logger.info(f"Errors encountered: {error_count}")
            
            if error_count > 0:
                logger.warning(f"⚠️  {error_count} templates encountered errors. Check logs above for details.")
            elif created_count == 0 and skipped_count > 0:
                logger.info("ℹ️  All templates already have versions - no new versions created.")
            else:
                logger.info("🎉 All templates successfully processed!")
                
        except Exception as e:
            logger.error(f"Script execution failed: {e}")
            sys.exit(1)

def main():
    """Entry point for the script."""
    try:
        creator = TemplateVersionCreator()
        creator.run()
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
