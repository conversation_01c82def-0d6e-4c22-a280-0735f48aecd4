"""
CSV to Markdown Converter

This script reads CSV file(s) and transforms each data row into a new Markdown file.
Each column in a row is converted into its own section in the Markdown file,
with the column header used as the section title (if available).

Extra header rows at the top that don't fit the main structure (i.e. the most common column count)
are automatically skipped. This is useful when the first few rows contain descriptive text rather than
actual column names.

If no output directory is specified (or if a relative path is provided), the output folder will be created
next to the input CSV file. By default, the output folder is named after the input CSV file (without its extension).

If no prefix is provided, the default prefix for the output Markdown file names will be the CSV file name
(with an extra "_" appended).

Usage:
    For a single CSV file with headers:
        python csv_to_markdown.py your_input.csv

    For a single CSV file without headers:
        python csv_to_markdown.py your_input.csv --no-header

    For a directory of CSV files:
        python csv_to_markdown.py /path/to/csv_directory
        python csv_to_markdown.py /path/to/csv_directory --no-header

Options:
    -o, --output_dir  Directory to store the output Markdown files.
                      For a single file, if not provided, a folder named after the CSV file (without extension)
                      is created next to the CSV file.
                      For a directory input, if not provided, each CSV file's output folder is created next to that CSV file.
    --prefix          Prefix for output Markdown files. The file number is appended after this prefix.
                      Default is the CSV file name (without extension) with an extra "_" appended.
    --no-header       Specify if the CSV file does not have a header row.
"""

import csv
import os
import argparse
from collections import Counter

def read_csv_file(input_path):
    """Read a CSV file and return a list of rows (each row is a list of strings)."""
    with open(input_path, newline='', encoding='utf-8') as csvfile:
        return list(csv.reader(csvfile))

def convert_file(input_csv, output_dir, prefix, no_header):
    # Determine the input CSV directory and base name.
    input_csv_dir = os.path.dirname(os.path.abspath(input_csv))
    csv_basename = os.path.basename(input_csv)
    default_prefix = os.path.splitext(csv_basename)[0]
    
    # Set default prefix if not provided, then ensure it ends with an underscore.
    if prefix is None:
        prefix = default_prefix
    prefix = prefix.rstrip("_") + "_"
    
    # Determine output directory for this CSV file.
    # If not provided, create a folder next to the CSV named after the CSV (without extension).
    if output_dir is None:
        output_dir = os.path.join(input_csv_dir, default_prefix)
    elif not os.path.isabs(output_dir):
        output_dir = os.path.join(input_csv_dir, output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    # Read all rows from the CSV file.
    all_rows = read_csv_file(input_csv)
    # Filter out completely empty rows.
    all_rows = [row for row in all_rows if any(cell.strip() for cell in row)]
    if not all_rows:
        print(f"No valid rows found in {input_csv}")
        return

    # Determine the most common row length (expected structure).
    row_lengths = [len(row) for row in all_rows]
    mode_length, _ = Counter(row_lengths).most_common(1)[0]

    if no_header:
        # Treat every row matching the structure as a data row.
        data_rows = [row for row in all_rows if len(row) == mode_length]
        for row_index, row in enumerate(data_rows, start=1):
            md_filename = os.path.join(output_dir, f"{prefix}{row_index}.md")
            with open(md_filename, 'w', encoding='utf-8') as md_file:
                for col_index, cell in enumerate(row, start=1):
                    md_file.write(f"## Column {col_index}\n\n")
                    md_file.write(f"{cell}\n\n")
    else:
        # Find the first row with the expected structure to use as the header row.
        header_row = None
        header_index = None
        for i, row in enumerate(all_rows):
            if len(row) == mode_length:
                header_row = row
                header_index = i
                break
        if header_row is None:
            print(f"No header row matching the expected structure was found in {input_csv}")
            return

        # Only process rows after the header that match the expected structure.
        data_rows = [row for row in all_rows[header_index + 1:] if len(row) == mode_length]
        for row_index, row in enumerate(data_rows, start=1):
            md_filename = os.path.join(output_dir, f"{prefix}{row_index}.md")
            with open(md_filename, 'w', encoding='utf-8') as md_file:
                for header, cell in zip(header_row, row):
                    md_file.write(f"## {header}\n\n")
                    md_file.write(f"{cell}\n\n")
    print(f"Markdown files have been created in '{output_dir}' for {input_csv}")

def main():
    parser = argparse.ArgumentParser(
        description="Convert CSV file(s) to Markdown files, one per row."
    )
    parser.add_argument("input_path", help="Path to an input CSV file or directory containing CSV files.")
    parser.add_argument("-o", "--output_dir", default=None,
                        help="Output directory to store Markdown files. For a single file, if not provided, a folder named after the CSV file (without extension) is created next to it. For a directory input, if not provided, each CSV file's output folder is created next to that CSV file.")
    parser.add_argument("--prefix", default=None,
                        help="Prefix for output Markdown files. Default is the CSV file name (without extension) with an extra '_' appended.")
    parser.add_argument("--no-header", action="store_true",
                        help="Specify if the CSV file does not have a header row.")
    args = parser.parse_args()
    
    input_path = args.input_path
    
    if os.path.isdir(input_path):
        # Process every CSV file in the directory.
        csv_files = [os.path.join(input_path, f) for f in os.listdir(input_path)
                     if os.path.splitext(f)[1].lower() == ".csv"]
        if not csv_files:
            print("No CSV files found in the specified directory.")
            return
        for csv_file in csv_files:
            # For each CSV file, use the global output_dir if provided;
            # otherwise, create the output folder next to the CSV file.
            convert_file(csv_file, args.output_dir, args.prefix, args.no_header)
    elif os.path.isfile(input_path):
        ext = os.path.splitext(input_path)[1].lower()
        if ext != ".csv":
            print("Error: Input file must be a CSV.")
            return
        convert_file(input_path, args.output_dir, args.prefix, args.no_header)
    else:
        print("The specified input path does not exist.")

if __name__ == '__main__':
    main()