"""
Excel to CSV Converter

This script converts an Excel file (xls/xlsx) to a CSV file.
If the input path is a directory, all Excel files (.xls or .xlsx) in that directory will be converted.
If a file cannot be read as a standard Excel file and appears to be HTML (e.g. an HTML table masquerading as an .xls file),
the script will attempt to read it using pandas.read_html().

Usage:
    For a single Excel file:
        python excel_to_csv.py input_file.xlsx
        python excel_to_csv.py input_file.xls -o output_file.csv

    For a directory containing Excel files:
        python excel_to_csv.py /path/to/excel_files_directory
        python excel_to_csv.py /path/to/excel_files_directory -o /path/to/output_directory

Options:
    -o, --output    For a file input: path to the output CSV file.
                    For a directory input: path to the output directory where CSV files will be saved.
                    If not provided, files are saved alongside the input file(s).
"""

import os
import sys
import argparse
import pandas as pd

def drop_index_column_if_present(df):
    """
    If the first column header appears to be an index (e.g. "Unnamed: 0" or "index"),
    drop that column.
    """
    if len(df.columns) > 1:
        first_col = str(df.columns[0]).strip().lower()
        if first_col == "unnamed: 0" or first_col == "index":
            df = df.iloc[:, 1:]
    return df

def convert_excel_to_csv(input_file, output_file):
    ext = os.path.splitext(input_file)[1].lower()
    # Choose the appropriate engine
    engine = 'openpyxl' if ext == '.xlsx' else 'xlrd'
    df = None

    try:
        df = pd.read_excel(input_file, engine=engine)
    except Exception as e:
        # Check if the file might actually be HTML
        try:
            with open(input_file, 'rb') as f:
                head = f.read(200).decode('utf-8', errors='ignore')
            if head.lstrip().startswith("<html"):
                print(f"File {input_file} appears to be HTML. Trying to read it using pandas.read_html()...")
                tables = pd.read_html(input_file)
                if tables:
                    df = tables[0]
                else:
                    print(f"No tables found in the HTML file {input_file}.")
                    return False
            else:
                print(f"Error reading Excel file {input_file}: {e}")
                return False
        except Exception as html_e:
            print(f"Error processing file {input_file}: {html_e}")
            return False

    # Drop a potential index column if present
    df = drop_index_column_if_present(df)

    try:
        # Export CSV without writing an extra index column/header
        df.to_csv(output_file, index=False)
        print(f"Converted {input_file} -> {output_file}")
        return True
    except Exception as e:
        print(f"Error writing CSV file {output_file}: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Convert an Excel file (xls/xlsx) to CSV. If a directory is provided, all Excel files in that directory will be converted."
    )
    parser.add_argument("input_path", help="Path to the input Excel file or directory containing Excel files.")
    parser.add_argument("-o", "--output", default=None,
                        help="For a file input: path to the output CSV file. For a directory input: path to the output directory where CSV files will be saved. If not provided, files are saved alongside the input file(s).")
    args = parser.parse_args()

    input_path = args.input_path

    if os.path.isdir(input_path):
        # Convert all Excel files in the directory.
        excel_files = [os.path.join(input_path, f)
                       for f in os.listdir(input_path)
                       if os.path.splitext(f)[1].lower() in ['.xls', '.xlsx']]
        if not excel_files:
            print("No Excel files (.xls or .xlsx) found in the specified directory.")
            sys.exit(1)
        # Determine output directory if provided.
        if args.output:
            output_dir = args.output
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
        else:
            output_dir = None

        for excel_file in excel_files:
            base_name = os.path.splitext(os.path.basename(excel_file))[0]
            if output_dir:
                csv_file = os.path.join(output_dir, base_name + ".csv")
            else:
                csv_file = os.path.join(os.path.dirname(excel_file), base_name + ".csv")
            convert_excel_to_csv(excel_file, csv_file)
    elif os.path.isfile(input_path):
        # Single file input.
        ext = os.path.splitext(input_path)[1].lower()
        if ext not in ['.xls', '.xlsx']:
            print("Error: The input file must be an Excel file with extension .xls or .xlsx")
            sys.exit(1)
        if args.output:
            output_file = args.output
        else:
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_file = os.path.join(os.path.dirname(os.path.abspath(input_path)), base_name + ".csv")
        convert_excel_to_csv(input_path, output_file)
    else:
        print("Error: The specified input path does not exist.")
        sys.exit(1)

if __name__ == '__main__':
    main()