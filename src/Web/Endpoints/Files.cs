using Microsoft.AspNetCore.Mvc;
using Minikai.Application.Files.Queries.DownloadFile;
using Minikai.Application.Files.Queries.GetDocumentFile;
using Minikai.Application.Files.Queries.GetFileByExternalId;
using Minikai.Application.Files.Queries.GetFilesByExternalIds;
using Minikai.Application.Files.Commands.UploadFiles;
using Minikai.Application.Files.Commands.RemoveFiles;
using Minikai.Application.Files.Commands.UpdateFileByExternalId;

namespace Minikai.Web.Endpoints;

public class Files : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this).DisableAntiforgery()
            .MapGet(DownloadFile, "download/{docFileId}")
            .MapGet(GetDocumentFile, "{id}")
            .MapGet(GetDocumentFileNames, "names")
            .MapGet(GetDocumentFileByExternalId, "{externalSource}/{externalId}")
            .MapPost(GetDocumentFilesByExternalIds, "{externalSource}/files")
            .MapPost(UploadFiles)
            .MapPut(UpdateFileByExternalId, "{externalSource}/{externalId}")
            .MapDelete(RemoveFiles, "files");
    }

    public async Task<IResult> GetDocumentFileByExternalId(ISender sender, string externalSource, string externalId)
    {
        var query = new GetFileByExternalIdQuery(externalId, externalSource);
        var file = await sender.Send(query);
        
        if (file == null)
            return Results.NotFound();
            
        return Results.Ok(file);
    }
    
    public async Task<IResult> GetDocumentFilesByExternalIds(ISender sender, string externalSource, [FromBody] string[] externalIds)
    {
        var query = new GetFilesByExternalIdsQuery(externalIds, externalSource);
        var files = await sender.Send(query);
        return Results.Ok(files);
    }

    public async Task<IResult> GetDocumentFileNames(ISender sender, [FromQuery] string[] fileIds)
    {
        var query = new GetDocumentFileNamesQuery(fileIds);
        var result = await sender.Send(query);
        return Results.Ok(result);
    }

    public async Task<IResult> GetDocumentFile(ISender sender, string id)
    {
        // Try parsing as Guid for DocFileId
        if (Guid.TryParse(id, out Guid docFileId))
        {
            var documentFile = await sender.Send(new GetDocumentFileQuery(docFileId));
            return Results.Ok(documentFile);
        }
        
        // Otherwise treat as FileId
        var documentFileByFileId = await sender.Send(new GetDocumentFileQuery(id));
        return Results.Ok(documentFileByFileId);
    }

    [IgnoreAntiforgeryToken(Order = 2000)]
    public async Task<IResult> UploadFiles(
        ISender sender, 
        [FromForm] IFormFileCollection files, 
        [FromQuery] string groupId, 
        [FromQuery] string? miniId = null,
        [FromHeader(Name = "X-External-Ids")] string? externalIdsJson = null,
        [FromHeader(Name = "X-External-Source")] string? externalSource = null,
        [FromHeader(Name = "X-Tenant-Id")] string? tenantId = null,
        [FromHeader(Name = "X-Allowed-Users")] string? allowedUsersJson = null,
        [FromHeader(Name = "X-Fields")] string? fieldsJson = null)
    {
        Dictionary<string, string>? externalIds = null;
        List<string>? allowedUsers = null;
        Dictionary<string, string>? fields = null;
        
        // Parse externalIds if provided
        if (!string.IsNullOrEmpty(externalIdsJson))
        {
            try
            {
                externalIds = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(externalIdsJson);
            }
            catch (Exception ex)
            {
                return Results.BadRequest($"Invalid X-External-Ids format: {ex.Message}");
            }
        }
        
        // Parse allowedUsers if provided
        if (!string.IsNullOrEmpty(allowedUsersJson))
        {
            try
            {
                allowedUsers = System.Text.Json.JsonSerializer.Deserialize<List<string>>(allowedUsersJson);
            }
            catch (Exception ex)
            {
                return Results.BadRequest($"Invalid X-Allowed-Users format: {ex.Message}");
            }
        }
        
        // Parse fields if provided
        if (!string.IsNullOrEmpty(fieldsJson))
        {
            try
            {
                fields = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(fieldsJson);
            }
            catch (Exception ex)
            {
                return Results.BadRequest($"Invalid X-Fields format: {ex.Message}");
            }
        }
        
        var command = new UploadFilesCommand 
        { 
            Files = files, 
            GroupId = groupId, 
            MiniId = miniId,
            ExternalIds = externalIds,
            ExternalSource = externalSource,
            TenantId = tenantId,
            AllowedUsers = allowedUsers,
            Fields = fields
        };
        
        var fileIds = await sender.Send(command);
        return Results.Ok(fileIds);
    }

    [IgnoreAntiforgeryToken(Order = 2000)]
    public async Task<IResult> UpdateFileByExternalId(
        ISender sender,
        string externalSource,
        string externalId,
        [FromForm] IFormFile file,
        [FromQuery] string groupId)
    {
        var command = new UpdateFileByExternalIdCommand
        {
            ExternalId = externalId,
            ExternalSource = externalSource,
            File = file,
            GroupId = groupId
        };
        
        var docFileId = await sender.Send(command);
        return Results.Ok(docFileId);
    }

    [ProducesResponseType(typeof(FileResult), StatusCodes.Status200OK, "application/octet-stream")]
    public async Task<IResult> DownloadFile(ISender sender, Guid docFileId)
    {
        var stream = await sender.Send(new DownloadFileQuery(docFileId));
        
        // Get file info from database to determine content type and filename
        var documentFile = await sender.Send(new GetDocumentFileQuery(docFileId));
        
        return Results.File(
            stream,
            documentFile?.ContentType ?? "application/octet-stream",
            documentFile?.Name ?? "download"
        );
    }


    public async Task<IResult> RemoveFiles(
        ISender sender, 
        [FromQuery] string groupId, 
        [FromBody] string[] fileIds,
        [FromQuery] string? miniId = null)
    {
        await sender.Send(new RemoveFilesCommand { 
            GroupId = groupId, 
            FileIds = fileIds,
            MiniId = miniId
        });
        return Results.Ok();
    }
}
