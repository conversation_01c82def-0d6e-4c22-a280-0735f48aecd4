using Microsoft.AspNetCore.Mvc;
using Minikai.Application.Common.Interfaces;
using Minikai.Application.Common.Models;
using Minikai.Application.Search.Queries;

namespace Minikai.Web.Endpoints;

public class Search : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapPost(SearchText, "text")
            .MapPost(SearchVector, "vector")
            .MapPost(SearchHybrid, "hybrid");
    }

    /// <summary>
    /// Performs a vector search using a user query. Vector embedding is performed by AI Search (via Azure OpenAI Skillset).
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<SearchResultDto> SearchText(
        ISender sender,
        [FromBody] SearchTextRequest request,
        CancellationToken cancellationToken)
    {
        // Validate request
        Guard.Against.Null(request, nameof(request));
        Guard.Against.NullOrWhiteSpace(request.Query, nameof(request.Query));
        Guard.Against.NullOrWhiteSpace(request.MiniId, nameof(request.MiniId));
        
        // Construct the index name
        string indexName = $"minikai-file-index";

        // Parse date strings to DateTime objects
        DateTime? targetDateStart = null;
        DateTime? targetDateEnd = null;

        if (!string.IsNullOrEmpty(request.TargetDateStart) && DateTime.TryParse(request.TargetDateStart, out var startDate))
        {
            targetDateStart = startDate;
        }

        if (!string.IsNullOrEmpty(request.TargetDateEnd) && DateTime.TryParse(request.TargetDateEnd, out var endDate))
        {
            targetDateEnd = endDate;
        }

        // Create and send query
        var query = new SearchTextQuery
        {
            IndexName = indexName,
            Query = request.Query,
            MiniId = request.MiniId,
            TargetDateStart = targetDateStart,
            TargetDateEnd = targetDateEnd,
            Top = request.Top
        };

        return await sender.Send(query, cancellationToken);
    }

    /// <summary>
    /// Performs a vector search using a user query. Vector embedding is performed by Azure OpenAI.
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="embeddingService"></param>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<SearchResultDto> SearchVector(
        ISender sender,
        IEmbeddingService embeddingService,
        [FromBody] SearchVectorRequest request,
        CancellationToken cancellationToken)
    {
        // Validate request
        Guard.Against.Null(request, nameof(request));
        Guard.Against.NullOrWhiteSpace(request.Query, nameof(request.Query));
        Guard.Against.NullOrWhiteSpace(request.MiniId, nameof(request.MiniId));
        
        // Construct the index name
        string indexName = $"minikai-file-index";

        // Generate embedding from the text query
        var queryVector = await embeddingService.GenerateEmbeddingAsync(request.Query, cancellationToken);

        // Parse date strings to DateTime objects
        DateTime? targetDateStart = null;
        DateTime? targetDateEnd = null;

        if (!string.IsNullOrEmpty(request.TargetDateStart) && DateTime.TryParse(request.TargetDateStart, out var startDate))
        {
            targetDateStart = startDate;
        }

        if (!string.IsNullOrEmpty(request.TargetDateEnd) && DateTime.TryParse(request.TargetDateEnd, out var endDate))
        {
            targetDateEnd = endDate;
        }

        // Create and send query
        var query = new SearchVectorQuery
        {
            IndexName = indexName,
            QueryVector = queryVector,
            MiniId = request.MiniId,
            TargetDateStart = targetDateStart,
            TargetDateEnd = targetDateEnd,
            Top = request.Top
        };

        return await sender.Send(query, cancellationToken);
    }

    /// <summary>
    /// Performs a hybrid search combining text search, vector search, and semantic ranking.
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="embeddingService"></param>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<SearchResultDto> SearchHybrid(
        ISender sender,
        IEmbeddingService embeddingService,
        [FromBody] SearchHybridRequest request,
        CancellationToken cancellationToken)
    {
        // Validate request
        Guard.Against.Null(request, nameof(request));
        Guard.Against.NullOrWhiteSpace(request.Query, nameof(request.Query));
        Guard.Against.NullOrWhiteSpace(request.MiniId, nameof(request.MiniId));
        
        // Construct the index name
        string indexName = $"minikai-file-index";

        // Generate embedding from the text query
        var queryVector = await embeddingService.GenerateEmbeddingAsync(request.Query, cancellationToken);

        // Parse date strings to DateTime objects
        DateTime? targetDateStart = null;
        DateTime? targetDateEnd = null;

        if (!string.IsNullOrEmpty(request.TargetDateStart) && DateTime.TryParse(request.TargetDateStart, out var startDate))
        {
            targetDateStart = startDate;
        }

        if (!string.IsNullOrEmpty(request.TargetDateEnd) && DateTime.TryParse(request.TargetDateEnd, out var endDate))
        {
            targetDateEnd = endDate;
        }

        // Create and send query
        var query = new SearchHybridQuery
        {
            IndexName = indexName,
            TextQuery = request.Query,
            QueryVector = queryVector,
            MiniId = request.MiniId,
            TargetDateStart = targetDateStart,
            TargetDateEnd = targetDateEnd,
            Top = request.Top
        };

        return await sender.Send(query, cancellationToken);
    }
}

public class SearchTextRequest
{
    /// <summary>
    /// The search query text
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// The Mini ID to filter results by
    /// </summary>
    public required string MiniId { get; set; }

    /// <summary>
    /// Optional start date for filtering results (inclusive) - ISO 8601 string format
    /// </summary>
    public string? TargetDateStart { get; set; }

    /// <summary>
    /// Optional end date for filtering results (inclusive) - ISO 8601 string format
    /// </summary>
    public string? TargetDateEnd { get; set; }

    /// <summary>
    /// Optional limit on the number of results to return
    /// </summary>
    public int? Top { get; set; }
}

public class SearchVectorRequest
{
    /// <summary>
    /// The text query to convert to a vector embedding
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// The Mini ID to filter results by
    /// </summary>
    public required string MiniId { get; set; }

    /// <summary>
    /// Optional start date for filtering results (inclusive) - ISO 8601 string format
    /// </summary>
    public string? TargetDateStart { get; set; }

    /// <summary>
    /// Optional end date for filtering results (inclusive) - ISO 8601 string format
    /// </summary>
    public string? TargetDateEnd { get; set; }

    /// <summary>
    /// Optional limit on the number of results to return
    /// </summary>
    public int? Top { get; set; }
}

public class SearchHybridRequest
{
    /// <summary>
    /// The text query for semantic understanding and text matching
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// The Mini ID to filter results by
    /// </summary>
    public required string MiniId { get; set; }

    /// <summary>
    /// Optional start date for filtering results (inclusive) - ISO 8601 string format
    /// </summary>
    public string? TargetDateStart { get; set; }

    /// <summary>
    /// Optional end date for filtering results (inclusive) - ISO 8601 string format
    /// </summary>
    public string? TargetDateEnd { get; set; }

    /// <summary>
    /// Optional limit on the number of results to return
    /// </summary>
    public int? Top { get; set; }
}
