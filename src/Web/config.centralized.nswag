{"runtime": "Net80", "defaultVariables": null, "documentGenerator": {"aspNetCoreToOpenApi": {"project": "Web.csproj", "msBuildProjectExtensionsPath": null, "configuration": null, "runtime": null, "targetFramework": null, "noBuild": true, "msBuildOutputPath": null, "verbose": false, "workingDirectory": null, "requireParametersWithoutDefault": true, "apiGroupNames": null, "defaultPropertyNameHandling": "CamelCase", "defaultReferenceTypeNullHandling": "NotNull", "defaultDictionaryValueReferenceTypeNullHandling": "NotNull", "defaultResponseReferenceTypeNullHandling": "NotNull", "generateOriginalParameterNames": true, "defaultEnumHandling": "Integer", "flattenInheritanceHierarchy": false, "generateKnownTypes": true, "generateEnumMappingDescription": false, "generateXmlObjects": false, "generateAbstractProperties": false, "generateAbstractSchemas": true, "ignoreObsoleteProperties": false, "allowReferencesWithProperties": false, "useXmlDocumentation": true, "resolveExternalXmlDocumentation": true, "excludedTypeNames": [], "serviceHost": null, "serviceBasePath": null, "serviceSchemes": [], "infoTitle": "Minikai Web API", "infoDescription": "Core backend API for the Minikai platform", "infoVersion": "1.0.0", "documentTemplate": null, "documentProcessorTypes": [], "operationProcessorTypes": [], "typeNameGeneratorType": null, "schemaNameGeneratorType": null, "contractResolverType": null, "serializerSettingsType": null, "useDocumentProvider": true, "documentName": "v1", "aspNetCoreEnvironment": null, "createWebHostBuilderMethod": null, "startupType": null, "allowNullableBodyParameters": true, "useHttpAttributeNameAsOperationId": false, "output": "../../apis/specs/web-api/web-api.openapi.json", "outputType": "OpenApi3", "newLineBehavior": "Auto", "assemblyPaths": [], "assemblyConfig": null, "referencePaths": [], "useNuGetCache": false}}, "codeGenerators": {}}